import { create<PERSON>ontex<PERSON>, use<PERSON>ontext, useMemo, useReducer, useState, createElement, useEffect } from "react"
import { applyDelta, Event, hydrateClientStorage, useEventLoop, refs } from "$/utils/state"
import { jsx } from "@emotion/react";

export const initialState = {"reflex___state____state": {"is_hydrated_rx_state_": false, "router_rx_state_": {"session": {"client_token": "", "client_ip": "", "session_id": ""}, "headers": {"host": "", "origin": "", "upgrade": "", "connection": "", "cookie": "", "pragma": "", "cache_control": "", "user_agent": "", "sec_websocket_version": "", "sec_websocket_key": "", "sec_websocket_extensions": "", "accept_encoding": "", "accept_language": "", "raw_headers": {}}, "page": {"host": "", "path": "", "raw_path": "", "full_path": "", "full_raw_path": "", "params": {}}, "url": "", "route_id": ""}}, "reflex___state____state.reflex___state____frontend_event_exception_state": {}, "reflex___state____state.reflex___state____on_load_internal_state": {}, "reflex___state____state.reflex___state____update_vars_internal_state": {}, "reflex___state____state.states___auth_state____auth_state": {"current_user_rx_state_": null, "current_user_id_rx_state_": 0, "error_message_rx_state_": "", "is_admin_rx_state_": false, "is_authenticated_rx_state_": false, "is_loading_rx_state_": false, "is_supervisor_rx_state_": false, "login_password_rx_state_": "", "login_username_rx_state_": "", "session_token_rx_state_": null, "success_message_rx_state_": ""}, "reflex___state____state.states___clinical_state____clinical_state": {"assessment_form_rx_state_": {"patient_id": 0, "assessment_date": "", "chief_complaint": "", "history_present_illness": "", "primary_diagnosis": "", "secondary_diagnoses": "", "treatment_plan": ""}, "available_disorders_rx_state_": [], "criteria_met_percentage_rx_state_": 0.0, "criterion_responses_rx_state_": {}, "current_assessment_rx_state_": null, "current_user_id_rx_state_": 1, "diagnosis_confidence_rx_state_": "Not evaluated", "disorder_criteria_rx_state_": null, "dsm5_evaluation_rx_state_": null, "error_message_rx_state_": "", "is_loading_rx_state_": false, "patient_assessments_rx_state_": [], "required_criteria_status_rx_state_": "", "selected_disorder_rx_state_": "", "success_message_rx_state_": ""}, "reflex___state____state.states___patient_state____patient_state": {"current_page_rx_state_": 1, "current_patient_rx_state_": null, "current_user_id_rx_state_": 0, "error_message_rx_state_": "", "is_loading_rx_state_": false, "page_size_rx_state_": 25, "potential_duplicates_rx_state_": [], "search_results_rx_state_": [], "search_term_rx_state_": "", "show_advanced_filters_rx_state_": false, "success_message_rx_state_": "", "total_pages_rx_state_": 0, "total_results_rx_state_": 0}}

export const defaultColorMode = "light"
export const ColorModeContext = createContext(null);
export const UploadFilesContext = createContext(null);
export const DispatchContext = createContext(null);
export const StateContexts = {
  reflex___state____state: createContext(null),
  reflex___state____state__reflex___state____frontend_event_exception_state: createContext(null),
  reflex___state____state__reflex___state____on_load_internal_state: createContext(null),
  reflex___state____state__reflex___state____update_vars_internal_state: createContext(null),
  reflex___state____state__states___auth_state____auth_state: createContext(null),
  reflex___state____state__states___clinical_state____clinical_state: createContext(null),
  reflex___state____state__states___patient_state____patient_state: createContext(null),
}
export const EventLoopContext = createContext(null);
export const clientStorage = {"cookies": {}, "local_storage": {}, "session_storage": {}}

export const state_name = "reflex___state____state"

export const exception_state_name = "reflex___state____state.reflex___state____frontend_event_exception_state"

// These events are triggered on initial load and each page navigation.
export const onLoadInternalEvent = () => {
    const internal_events = [];

    // Get tracked cookie and local storage vars to send to the backend.
    const client_storage_vars = hydrateClientStorage(clientStorage);
    // But only send the vars if any are actually set in the browser.
    if (client_storage_vars && Object.keys(client_storage_vars).length !== 0) {
        internal_events.push(
            Event(
                'reflex___state____state.reflex___state____update_vars_internal_state.update_vars_internal',
                {vars: client_storage_vars},
            ),
        );
    }

    // `on_load_internal` triggers the correct on_load event(s) for the current page.
    // If the page does not define any on_load event, this will just set `is_hydrated = true`.
    internal_events.push(Event('reflex___state____state.reflex___state____on_load_internal_state.on_load_internal'));

    return internal_events;
}

// The following events are sent when the websocket connects or reconnects.
export const initialEvents = () => [
    Event('reflex___state____state.hydrate'),
    ...onLoadInternalEvent()
]

export const isDevMode = true

export function UploadFilesProvider({ children }) {
  const [filesById, setFilesById] = useState({})
  refs["__clear_selected_files"] = (id) => setFilesById(filesById => {
    const newFilesById = {...filesById}
    delete newFilesById[id]
    return newFilesById
  })
  return createElement(
    UploadFilesContext.Provider,
    { value: [filesById, setFilesById] },
    children
  );
}

export function ClientSide(component) {
  return ({ children, ...props }) => {
    const [Component, setComponent] = useState(null);
    useEffect(() => {
      setComponent(component);
    }, []);
    return Component ? jsx(Component, props, children) : null;
  };
}

export function EventLoopProvider({ children }) {
  const dispatch = useContext(DispatchContext)
  const [addEvents, connectErrors] = useEventLoop(
    dispatch,
    initialEvents,
    clientStorage,
  )
  return createElement(
    EventLoopContext.Provider,
    { value: [addEvents, connectErrors] },
    children
  );
}

export function StateProvider({ children }) {
  const [reflex___state____state, dispatch_reflex___state____state] = useReducer(applyDelta, initialState["reflex___state____state"])
  const [reflex___state____state__reflex___state____frontend_event_exception_state, dispatch_reflex___state____state__reflex___state____frontend_event_exception_state] = useReducer(applyDelta, initialState["reflex___state____state.reflex___state____frontend_event_exception_state"])
  const [reflex___state____state__reflex___state____on_load_internal_state, dispatch_reflex___state____state__reflex___state____on_load_internal_state] = useReducer(applyDelta, initialState["reflex___state____state.reflex___state____on_load_internal_state"])
  const [reflex___state____state__reflex___state____update_vars_internal_state, dispatch_reflex___state____state__reflex___state____update_vars_internal_state] = useReducer(applyDelta, initialState["reflex___state____state.reflex___state____update_vars_internal_state"])
  const [reflex___state____state__states___auth_state____auth_state, dispatch_reflex___state____state__states___auth_state____auth_state] = useReducer(applyDelta, initialState["reflex___state____state.states___auth_state____auth_state"])
  const [reflex___state____state__states___clinical_state____clinical_state, dispatch_reflex___state____state__states___clinical_state____clinical_state] = useReducer(applyDelta, initialState["reflex___state____state.states___clinical_state____clinical_state"])
  const [reflex___state____state__states___patient_state____patient_state, dispatch_reflex___state____state__states___patient_state____patient_state] = useReducer(applyDelta, initialState["reflex___state____state.states___patient_state____patient_state"])
  const dispatchers = useMemo(() => {
    return {
      "reflex___state____state": dispatch_reflex___state____state,
      "reflex___state____state.reflex___state____frontend_event_exception_state": dispatch_reflex___state____state__reflex___state____frontend_event_exception_state,
      "reflex___state____state.reflex___state____on_load_internal_state": dispatch_reflex___state____state__reflex___state____on_load_internal_state,
      "reflex___state____state.reflex___state____update_vars_internal_state": dispatch_reflex___state____state__reflex___state____update_vars_internal_state,
      "reflex___state____state.states___auth_state____auth_state": dispatch_reflex___state____state__states___auth_state____auth_state,
      "reflex___state____state.states___clinical_state____clinical_state": dispatch_reflex___state____state__states___clinical_state____clinical_state,
      "reflex___state____state.states___patient_state____patient_state": dispatch_reflex___state____state__states___patient_state____patient_state,
    }
  }, [])

  return (
    createElement(StateContexts.reflex___state____state,{value: reflex___state____state},
    createElement(StateContexts.reflex___state____state__reflex___state____frontend_event_exception_state,{value: reflex___state____state__reflex___state____frontend_event_exception_state},
    createElement(StateContexts.reflex___state____state__reflex___state____on_load_internal_state,{value: reflex___state____state__reflex___state____on_load_internal_state},
    createElement(StateContexts.reflex___state____state__reflex___state____update_vars_internal_state,{value: reflex___state____state__reflex___state____update_vars_internal_state},
    createElement(StateContexts.reflex___state____state__states___auth_state____auth_state,{value: reflex___state____state__states___auth_state____auth_state},
    createElement(StateContexts.reflex___state____state__states___clinical_state____clinical_state,{value: reflex___state____state__states___clinical_state____clinical_state},
    createElement(StateContexts.reflex___state____state__states___patient_state____patient_state,{value: reflex___state____state__states___patient_state____patient_state},
    createElement(DispatchContext, {value: dispatchers}, children)
)))))))  )
}