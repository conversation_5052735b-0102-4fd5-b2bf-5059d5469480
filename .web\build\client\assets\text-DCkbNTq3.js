import{a as u,y as E}from"./chunk-QMGIS6GS-suYYFPSk.js";import{j as v}from"./jsx-runtime-D_zvdyIk.js";function N(e,t){if(typeof e=="function")return e(t);e!=null&&(e.current=t)}function S(...e){return t=>{let r=!1;const l=e.map(n=>{const s=N(n,t);return!r&&typeof s=="function"&&(r=!0),s});if(r)return()=>{for(let n=0;n<l.length;n++){const s=l[n];typeof s=="function"?s():N(e[n],null)}}}}function le(...e){return u.useCallback(S(...e),e)}function O(e){const t=A(e),r=u.forwardRef((l,n)=>{const{children:s,...i}=l,o=u.Children.toArray(s),a=o.find(I);if(a){const c=a.props.children,p=o.map(y=>y===a?u.Children.count(c)>1?u.Children.only(null):u.isValidElement(c)?c.props.children:null:y);return v.jsx(t,{...i,ref:n,children:u.isValidElement(c)?u.cloneElement(c,void 0,p):null})}return v.jsx(t,{...i,ref:n,children:s})});return r.displayName=`${e}.Slot`,r}var R=O("Slot");function A(e){const t=u.forwardRef((r,l)=>{const{children:n,...s}=r;if(u.isValidElement(n)){const i=T(n),o=W(s,n.props);return n.type!==u.Fragment&&(o.ref=l?S(l,i):i),u.cloneElement(n,o)}return u.Children.count(n)>1?u.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}var j=Symbol("radix.slottable");function _(e){const t=({children:r})=>v.jsx(v.Fragment,{children:r});return t.displayName=`${e}.Slottable`,t.__radixId=j,t}var ue=_("Slottable");function I(e){return u.isValidElement(e)&&typeof e.type=="function"&&"__radixId"in e.type&&e.type.__radixId===j}function W(e,t){const r={...t};for(const l in t){const n=e[l],s=t[l];/^on[A-Z]/.test(l)?n&&s?r[l]=(...o)=>{const a=s(...o);return n(...o),a}:n&&(r[l]=n):l==="style"?r[l]={...n,...s}:l==="className"&&(r[l]=[n,s].filter(Boolean).join(" "))}return{...e,...r}}function T(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(t=Object.getOwnPropertyDescriptor(e,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning,r?e.props.ref:e.props.ref||e.ref)}var g={exports:{}};/*!
	Copyright (c) 2018 Jed Watson.
	Licensed under the MIT License (MIT), see
	http://jedwatson.github.io/classnames
*/var h;function k(){return h||(h=1,function(e){(function(){var t={}.hasOwnProperty;function r(){for(var s="",i=0;i<arguments.length;i++){var o=arguments[i];o&&(s=n(s,l(o)))}return s}function l(s){if(typeof s=="string"||typeof s=="number")return s;if(typeof s!="object")return"";if(Array.isArray(s))return r.apply(null,s);if(s.toString!==Object.prototype.toString&&!s.toString.toString().includes("[native code]"))return s.toString();var i="";for(var o in s)t.call(s,o)&&s[o]&&(i=n(i,o));return i}function n(s,i){return i?s?s+" "+i:s+i:s}e.exports?(r.default=r,e.exports=r):window.classNames=r})()}(g)),g.exports}var D=k();const f=E(D),F={asChild:{type:"boolean"}},P=["gray","gold","bronze","brown","yellow","amber","orange","tomato","red","ruby","crimson","pink","plum","purple","violet","iris","indigo","blue","cyan","teal","jade","green","grass","lime","mint","sky"],ce=["auto","gray","mauve","slate","sage","olive","sand"],z={color:{type:"enum",values:P,default:void 0}},pe={color:{type:"enum",values:P,default:""}},q={highContrast:{type:"boolean",className:"rt-high-contrast",default:void 0}},B=["normal","start","end","both"],H={trim:{type:"enum",className:"rt-r-lt",values:B,responsive:!0}},L=["left","center","right"],Z={align:{type:"enum",className:"rt-r-ta",values:L,responsive:!0}},G=["wrap","nowrap","pretty","balance"],J={wrap:{type:"enum",className:"rt-r-tw",values:G,responsive:!0}},K={truncate:{type:"boolean",className:"rt-truncate"}},M=["light","regular","medium","bold"],Q={weight:{type:"enum",className:"rt-r-weight",values:M,responsive:!0}},b=["initial","xs","sm","md","lg","xl"];function V(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function d(e){return typeof e=="object"&&Object.keys(e).some(t=>b.includes(t))}function U({className:e,customProperties:t,...r}){const l=w({allowArbitraryValues:!0,className:e,...r}),n=X({customProperties:t,...r});return[l,n]}function w({allowArbitraryValues:e,value:t,className:r,propValues:l,parseValue:n=s=>s}){const s=[];if(t){if(typeof t=="string"&&l.includes(t))return $(r,t,n);if(d(t)){const i=t;for(const o in i){if(!V(i,o)||!b.includes(o))continue;const a=i[o];if(a!==void 0){if(l.includes(a)){const c=$(r,a,n),p=o==="initial"?c:`${o}:${c}`;s.push(p)}else if(e){const c=o==="initial"?r:`${o}:${r}`;s.push(c)}}}return s.join(" ")}if(e)return r}}function $(e,t,r){const l=e?"-":"",n=r(t),s=n?.startsWith("-"),i=s?"-":"",o=s?n?.substring(1):n;return`${i}${e}${l}${o}`}function X({customProperties:e,value:t,propValues:r,parseValue:l=n=>n}){let n={};if(!(!t||typeof t=="string"&&r.includes(t))){if(typeof t=="string"&&(n=Object.fromEntries(e.map(s=>[s,t]))),d(t)){const s=t;for(const i in s){if(!V(s,i)||!b.includes(i))continue;const o=s[i];if(!r.includes(o))for(const a of e)n={[i==="initial"?a:`${a}-${i}`]:o,...n}}}for(const s in n){const i=n[s];i!==void 0&&(n[s]=l(i))}return n}}function x(...e){let t={};for(const r of e)r&&(t={...t,...r});return Object.keys(t).length?t:void 0}function Y(...e){return Object.assign({},...e)}function ee(e,...t){let r,l;const n={...e},s=Y(...t);for(const i in s){let o=n[i];const a=s[i];if(a.default!==void 0&&o===void 0&&(o=a.default),a.type==="enum"&&![a.default,...a.values].includes(o)&&!d(o)&&(o=a.default),n[i]=o,"className"in a&&a.className){delete n[i];const c="responsive"in a;if(!o||d(o)&&!c)continue;if(d(o)&&(a.default!==void 0&&o.initial===void 0&&(o.initial=a.default),a.type==="enum"&&([a.default,...a.values].includes(o.initial)||(o.initial=a.default))),a.type==="enum"){const p=w({allowArbitraryValues:!1,value:o,className:a.className,propValues:a.values,parseValue:a.parseValue});r=f(r,p);continue}if(a.type==="string"||a.type==="enum | string"){const p=a.type==="string"?[]:a.values,[y,C]=U({className:a.className,customProperties:a.customProperties,propValues:p,parseValue:a.parseValue,value:o});l=x(l,C),r=f(r,y);continue}if(a.type==="boolean"&&o){r=f(r,a.className);continue}}}return n.className=f(r,e.className),n.style=x(l,e.style),n}const m=["0","1","2","3","4","5","6","7","8","9","-1","-2","-3","-4","-5","-6","-7","-8","-9"],te={m:{type:"enum | string",values:m,responsive:!0,className:"rt-r-m",customProperties:["--m"]},mx:{type:"enum | string",values:m,responsive:!0,className:"rt-r-mx",customProperties:["--ml","--mr"]},my:{type:"enum | string",values:m,responsive:!0,className:"rt-r-my",customProperties:["--mt","--mb"]},mt:{type:"enum | string",values:m,responsive:!0,className:"rt-r-mt",customProperties:["--mt"]},mr:{type:"enum | string",values:m,responsive:!0,className:"rt-r-mr",customProperties:["--mr"]},mb:{type:"enum | string",values:m,responsive:!0,className:"rt-r-mb",customProperties:["--mb"]},ml:{type:"enum | string",values:m,responsive:!0,className:"rt-r-ml",customProperties:["--ml"]}},se=["span","div","label","p"],re=["1","2","3","4","5","6","7","8","9"],ne={as:{type:"enum",values:se,default:"span"},...F,size:{type:"enum",className:"rt-r-size",values:re,responsive:!0},...Q,...Z,...H,...K,...J,...z,...q},oe=u.forwardRef((e,t)=>{const{children:r,className:l,asChild:n,as:s="span",color:i,...o}=ee(e,ne,te);return u.createElement(R,{"data-accent-color":i,...o,ref:t,className:f("rt-Text",l)},n?r:u.createElement(s,null,r))});oe.displayName="Text";export{U as R,R as S,_ as a,S as b,O as c,q as d,z as e,J as f,K as g,ue as h,H as i,w as j,P as k,x as l,ce as m,Z as n,F as o,oe as p,te as r,pe as s,Q as t,le as u,ee as v,f as y};
