import{a as reactExports,o as useLocation,p as useNavigate,z as useParams,A as useSearchParams}from"./chunk-QMGIS6GS-suYYFPSk.js";import{j as jsx}from"./emotion-react.browser.esm-BNSIgtcs.js";const PACKET_TYPES=Object.create(null);PACKET_TYPES.open="0";PACKET_TYPES.close="1";PACKET_TYPES.ping="2";PACKET_TYPES.pong="3";PACKET_TYPES.message="4";PACKET_TYPES.upgrade="5";PACKET_TYPES.noop="6";const PACKET_TYPES_REVERSE=Object.create(null);Object.keys(PACKET_TYPES).forEach(t=>{PACKET_TYPES_REVERSE[PACKET_TYPES[t]]=t});const ERROR_PACKET={type:"error",data:"parser error"},withNativeBlob$1=typeof Blob=="function"||typeof Blob<"u"&&Object.prototype.toString.call(Blob)==="[object BlobConstructor]",withNativeArrayBuffer$2=typeof ArrayBuffer=="function",isView$1=t=>typeof ArrayBuffer.isView=="function"?ArrayBuffer.isView(t):t&&t.buffer instanceof ArrayBuffer,encodePacket=({type:t,data:e},r,n)=>withNativeBlob$1&&e instanceof Blob?r?n(e):encodeBlobAsBase64(e,n):withNativeArrayBuffer$2&&(e instanceof ArrayBuffer||isView$1(e))?r?n(e):encodeBlobAsBase64(new Blob([e]),n):n(PACKET_TYPES[t]+(e||"")),encodeBlobAsBase64=(t,e)=>{const r=new FileReader;return r.onload=function(){const n=r.result.split(",")[1];e("b"+(n||""))},r.readAsDataURL(t)};function toArray(t){return t instanceof Uint8Array?t:t instanceof ArrayBuffer?new Uint8Array(t):new Uint8Array(t.buffer,t.byteOffset,t.byteLength)}let TEXT_ENCODER;function encodePacketToBinary(t,e){if(withNativeBlob$1&&t.data instanceof Blob)return t.data.arrayBuffer().then(toArray).then(e);if(withNativeArrayBuffer$2&&(t.data instanceof ArrayBuffer||isView$1(t.data)))return e(toArray(t.data));encodePacket(t,!1,r=>{TEXT_ENCODER||(TEXT_ENCODER=new TextEncoder),e(TEXT_ENCODER.encode(r))})}const chars="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",lookup$1=typeof Uint8Array>"u"?[]:new Uint8Array(256);for(let t=0;t<chars.length;t++)lookup$1[chars.charCodeAt(t)]=t;const decode$1=t=>{let e=t.length*.75,r=t.length,n,u=0,s,o,l,D;t[t.length-1]==="="&&(e--,t[t.length-2]==="="&&e--);const y=new ArrayBuffer(e),C=new Uint8Array(y);for(n=0;n<r;n+=4)s=lookup$1[t.charCodeAt(n)],o=lookup$1[t.charCodeAt(n+1)],l=lookup$1[t.charCodeAt(n+2)],D=lookup$1[t.charCodeAt(n+3)],C[u++]=s<<2|o>>4,C[u++]=(o&15)<<4|l>>2,C[u++]=(l&3)<<6|D&63;return y},withNativeArrayBuffer$1=typeof ArrayBuffer=="function",decodePacket=(t,e)=>{if(typeof t!="string")return{type:"message",data:mapBinary(t,e)};const r=t.charAt(0);return r==="b"?{type:"message",data:decodeBase64Packet(t.substring(1),e)}:PACKET_TYPES_REVERSE[r]?t.length>1?{type:PACKET_TYPES_REVERSE[r],data:t.substring(1)}:{type:PACKET_TYPES_REVERSE[r]}:ERROR_PACKET},decodeBase64Packet=(t,e)=>{if(withNativeArrayBuffer$1){const r=decode$1(t);return mapBinary(r,e)}else return{base64:!0,data:t}},mapBinary=(t,e)=>{switch(e){case"blob":return t instanceof Blob?t:new Blob([t]);case"arraybuffer":default:return t instanceof ArrayBuffer?t:t.buffer}},SEPARATOR="",encodePayload=(t,e)=>{const r=t.length,n=new Array(r);let u=0;t.forEach((s,o)=>{encodePacket(s,!1,l=>{n[o]=l,++u===r&&e(n.join(SEPARATOR))})})},decodePayload=(t,e)=>{const r=t.split(SEPARATOR),n=[];for(let u=0;u<r.length;u++){const s=decodePacket(r[u],e);if(n.push(s),s.type==="error")break}return n};function createPacketEncoderStream(){return new TransformStream({transform(t,e){encodePacketToBinary(t,r=>{const n=r.length;let u;if(n<126)u=new Uint8Array(1),new DataView(u.buffer).setUint8(0,n);else if(n<65536){u=new Uint8Array(3);const s=new DataView(u.buffer);s.setUint8(0,126),s.setUint16(1,n)}else{u=new Uint8Array(9);const s=new DataView(u.buffer);s.setUint8(0,127),s.setBigUint64(1,BigInt(n))}t.data&&typeof t.data!="string"&&(u[0]|=128),e.enqueue(u),e.enqueue(r)})}})}let TEXT_DECODER;function totalLength(t){return t.reduce((e,r)=>e+r.length,0)}function concatChunks(t,e){if(t[0].length===e)return t.shift();const r=new Uint8Array(e);let n=0;for(let u=0;u<e;u++)r[u]=t[0][n++],n===t[0].length&&(t.shift(),n=0);return t.length&&n<t[0].length&&(t[0]=t[0].slice(n)),r}function createPacketDecoderStream(t,e){TEXT_DECODER||(TEXT_DECODER=new TextDecoder);const r=[];let n=0,u=-1,s=!1;return new TransformStream({transform(o,l){for(r.push(o);;){if(n===0){if(totalLength(r)<1)break;const D=concatChunks(r,1);s=(D[0]&128)===128,u=D[0]&127,u<126?n=3:u===126?n=1:n=2}else if(n===1){if(totalLength(r)<2)break;const D=concatChunks(r,2);u=new DataView(D.buffer,D.byteOffset,D.length).getUint16(0),n=3}else if(n===2){if(totalLength(r)<8)break;const D=concatChunks(r,8),y=new DataView(D.buffer,D.byteOffset,D.length),C=y.getUint32(0);if(C>Math.pow(2,21)-1){l.enqueue(ERROR_PACKET);break}u=C*Math.pow(2,32)+y.getUint32(4),n=3}else{if(totalLength(r)<u)break;const D=concatChunks(r,u);l.enqueue(decodePacket(s?D:TEXT_DECODER.decode(D),e)),n=0}if(u===0||u>t){l.enqueue(ERROR_PACKET);break}}}})}const protocol$1=4;function Emitter(t){if(t)return mixin(t)}function mixin(t){for(var e in Emitter.prototype)t[e]=Emitter.prototype[e];return t}Emitter.prototype.on=Emitter.prototype.addEventListener=function(t,e){return this._callbacks=this._callbacks||{},(this._callbacks["$"+t]=this._callbacks["$"+t]||[]).push(e),this};Emitter.prototype.once=function(t,e){function r(){this.off(t,r),e.apply(this,arguments)}return r.fn=e,this.on(t,r),this};Emitter.prototype.off=Emitter.prototype.removeListener=Emitter.prototype.removeAllListeners=Emitter.prototype.removeEventListener=function(t,e){if(this._callbacks=this._callbacks||{},arguments.length==0)return this._callbacks={},this;var r=this._callbacks["$"+t];if(!r)return this;if(arguments.length==1)return delete this._callbacks["$"+t],this;for(var n,u=0;u<r.length;u++)if(n=r[u],n===e||n.fn===e){r.splice(u,1);break}return r.length===0&&delete this._callbacks["$"+t],this};Emitter.prototype.emit=function(t){this._callbacks=this._callbacks||{};for(var e=new Array(arguments.length-1),r=this._callbacks["$"+t],n=1;n<arguments.length;n++)e[n-1]=arguments[n];if(r){r=r.slice(0);for(var n=0,u=r.length;n<u;++n)r[n].apply(this,e)}return this};Emitter.prototype.emitReserved=Emitter.prototype.emit;Emitter.prototype.listeners=function(t){return this._callbacks=this._callbacks||{},this._callbacks["$"+t]||[]};Emitter.prototype.hasListeners=function(t){return!!this.listeners(t).length};const nextTick=typeof Promise=="function"&&typeof Promise.resolve=="function"?e=>Promise.resolve().then(e):(e,r)=>r(e,0),globalThisShim=typeof self<"u"?self:typeof window<"u"?window:Function("return this")(),defaultBinaryType="arraybuffer";function createCookieJar(){}function pick(t,...e){return e.reduce((r,n)=>(t.hasOwnProperty(n)&&(r[n]=t[n]),r),{})}const NATIVE_SET_TIMEOUT=globalThisShim.setTimeout,NATIVE_CLEAR_TIMEOUT=globalThisShim.clearTimeout;function installTimerFunctions(t,e){e.useNativeTimers?(t.setTimeoutFn=NATIVE_SET_TIMEOUT.bind(globalThisShim),t.clearTimeoutFn=NATIVE_CLEAR_TIMEOUT.bind(globalThisShim)):(t.setTimeoutFn=globalThisShim.setTimeout.bind(globalThisShim),t.clearTimeoutFn=globalThisShim.clearTimeout.bind(globalThisShim))}const BASE64_OVERHEAD=1.33;function byteLength(t){return typeof t=="string"?utf8Length(t):Math.ceil((t.byteLength||t.size)*BASE64_OVERHEAD)}function utf8Length(t){let e=0,r=0;for(let n=0,u=t.length;n<u;n++)e=t.charCodeAt(n),e<128?r+=1:e<2048?r+=2:e<55296||e>=57344?r+=3:(n++,r+=4);return r}function randomString(){return Date.now().toString(36).substring(3)+Math.random().toString(36).substring(2,5)}function encode(t){let e="";for(let r in t)t.hasOwnProperty(r)&&(e.length&&(e+="&"),e+=encodeURIComponent(r)+"="+encodeURIComponent(t[r]));return e}function decode(t){let e={},r=t.split("&");for(let n=0,u=r.length;n<u;n++){let s=r[n].split("=");e[decodeURIComponent(s[0])]=decodeURIComponent(s[1])}return e}class TransportError extends Error{constructor(e,r,n){super(e),this.description=r,this.context=n,this.type="TransportError"}}class Transport extends Emitter{constructor(e){super(),this.writable=!1,installTimerFunctions(this,e),this.opts=e,this.query=e.query,this.socket=e.socket,this.supportsBinary=!e.forceBase64}onError(e,r,n){return super.emitReserved("error",new TransportError(e,r,n)),this}open(){return this.readyState="opening",this.doOpen(),this}close(){return(this.readyState==="opening"||this.readyState==="open")&&(this.doClose(),this.onClose()),this}send(e){this.readyState==="open"&&this.write(e)}onOpen(){this.readyState="open",this.writable=!0,super.emitReserved("open")}onData(e){const r=decodePacket(e,this.socket.binaryType);this.onPacket(r)}onPacket(e){super.emitReserved("packet",e)}onClose(e){this.readyState="closed",super.emitReserved("close",e)}pause(e){}createUri(e,r={}){return e+"://"+this._hostname()+this._port()+this.opts.path+this._query(r)}_hostname(){const e=this.opts.hostname;return e.indexOf(":")===-1?e:"["+e+"]"}_port(){return this.opts.port&&(this.opts.secure&&+(this.opts.port!==443)||!this.opts.secure&&Number(this.opts.port)!==80)?":"+this.opts.port:""}_query(e){const r=encode(e);return r.length?"?"+r:""}}class Polling extends Transport{constructor(){super(...arguments),this._polling=!1}get name(){return"polling"}doOpen(){this._poll()}pause(e){this.readyState="pausing";const r=()=>{this.readyState="paused",e()};if(this._polling||!this.writable){let n=0;this._polling&&(n++,this.once("pollComplete",function(){--n||r()})),this.writable||(n++,this.once("drain",function(){--n||r()}))}else r()}_poll(){this._polling=!0,this.doPoll(),this.emitReserved("poll")}onData(e){const r=n=>{if(this.readyState==="opening"&&n.type==="open"&&this.onOpen(),n.type==="close")return this.onClose({description:"transport closed by the server"}),!1;this.onPacket(n)};decodePayload(e,this.socket.binaryType).forEach(r),this.readyState!=="closed"&&(this._polling=!1,this.emitReserved("pollComplete"),this.readyState==="open"&&this._poll())}doClose(){const e=()=>{this.write([{type:"close"}])};this.readyState==="open"?e():this.once("open",e)}write(e){this.writable=!1,encodePayload(e,r=>{this.doWrite(r,()=>{this.writable=!0,this.emitReserved("drain")})})}uri(){const e=this.opts.secure?"https":"http",r=this.query||{};return this.opts.timestampRequests!==!1&&(r[this.opts.timestampParam]=randomString()),!this.supportsBinary&&!r.sid&&(r.b64=1),this.createUri(e,r)}}let value=!1;try{value=typeof XMLHttpRequest<"u"&&"withCredentials"in new XMLHttpRequest}catch{}const hasCORS=value;function empty(){}class BaseXHR extends Polling{constructor(e){if(super(e),typeof location<"u"){const r=location.protocol==="https:";let n=location.port;n||(n=r?"443":"80"),this.xd=typeof location<"u"&&e.hostname!==location.hostname||n!==e.port}}doWrite(e,r){const n=this.request({method:"POST",data:e});n.on("success",r),n.on("error",(u,s)=>{this.onError("xhr post error",u,s)})}doPoll(){const e=this.request();e.on("data",this.onData.bind(this)),e.on("error",(r,n)=>{this.onError("xhr poll error",r,n)}),this.pollXhr=e}}class Request extends Emitter{constructor(e,r,n){super(),this.createRequest=e,installTimerFunctions(this,n),this._opts=n,this._method=n.method||"GET",this._uri=r,this._data=n.data!==void 0?n.data:null,this._create()}_create(){var e;const r=pick(this._opts,"agent","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","autoUnref");r.xdomain=!!this._opts.xd;const n=this._xhr=this.createRequest(r);try{n.open(this._method,this._uri,!0);try{if(this._opts.extraHeaders){n.setDisableHeaderCheck&&n.setDisableHeaderCheck(!0);for(let u in this._opts.extraHeaders)this._opts.extraHeaders.hasOwnProperty(u)&&n.setRequestHeader(u,this._opts.extraHeaders[u])}}catch{}if(this._method==="POST")try{n.setRequestHeader("Content-type","text/plain;charset=UTF-8")}catch{}try{n.setRequestHeader("Accept","*/*")}catch{}(e=this._opts.cookieJar)===null||e===void 0||e.addCookies(n),"withCredentials"in n&&(n.withCredentials=this._opts.withCredentials),this._opts.requestTimeout&&(n.timeout=this._opts.requestTimeout),n.onreadystatechange=()=>{var u;n.readyState===3&&((u=this._opts.cookieJar)===null||u===void 0||u.parseCookies(n.getResponseHeader("set-cookie"))),n.readyState===4&&(n.status===200||n.status===1223?this._onLoad():this.setTimeoutFn(()=>{this._onError(typeof n.status=="number"?n.status:0)},0))},n.send(this._data)}catch(u){this.setTimeoutFn(()=>{this._onError(u)},0);return}typeof document<"u"&&(this._index=Request.requestsCount++,Request.requests[this._index]=this)}_onError(e){this.emitReserved("error",e,this._xhr),this._cleanup(!0)}_cleanup(e){if(!(typeof this._xhr>"u"||this._xhr===null)){if(this._xhr.onreadystatechange=empty,e)try{this._xhr.abort()}catch{}typeof document<"u"&&delete Request.requests[this._index],this._xhr=null}}_onLoad(){const e=this._xhr.responseText;e!==null&&(this.emitReserved("data",e),this.emitReserved("success"),this._cleanup())}abort(){this._cleanup()}}Request.requestsCount=0;Request.requests={};if(typeof document<"u"){if(typeof attachEvent=="function")attachEvent("onunload",unloadHandler);else if(typeof addEventListener=="function"){const t="onpagehide"in globalThisShim?"pagehide":"unload";addEventListener(t,unloadHandler,!1)}}function unloadHandler(){for(let t in Request.requests)Request.requests.hasOwnProperty(t)&&Request.requests[t].abort()}const hasXHR2=function(){const t=newRequest({xdomain:!1});return t&&t.responseType!==null}();class XHR extends BaseXHR{constructor(e){super(e);const r=e&&e.forceBase64;this.supportsBinary=hasXHR2&&!r}request(e={}){return Object.assign(e,{xd:this.xd},this.opts),new Request(newRequest,this.uri(),e)}}function newRequest(t){const e=t.xdomain;try{if(typeof XMLHttpRequest<"u"&&(!e||hasCORS))return new XMLHttpRequest}catch{}if(!e)try{return new globalThisShim[["Active"].concat("Object").join("X")]("Microsoft.XMLHTTP")}catch{}}const isReactNative=typeof navigator<"u"&&typeof navigator.product=="string"&&navigator.product.toLowerCase()==="reactnative";class BaseWS extends Transport{get name(){return"websocket"}doOpen(){const e=this.uri(),r=this.opts.protocols,n=isReactNative?{}:pick(this.opts,"agent","perMessageDeflate","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","localAddress","protocolVersion","origin","maxPayload","family","checkServerIdentity");this.opts.extraHeaders&&(n.headers=this.opts.extraHeaders);try{this.ws=this.createSocket(e,r,n)}catch(u){return this.emitReserved("error",u)}this.ws.binaryType=this.socket.binaryType,this.addEventListeners()}addEventListeners(){this.ws.onopen=()=>{this.opts.autoUnref&&this.ws._socket.unref(),this.onOpen()},this.ws.onclose=e=>this.onClose({description:"websocket connection closed",context:e}),this.ws.onmessage=e=>this.onData(e.data),this.ws.onerror=e=>this.onError("websocket error",e)}write(e){this.writable=!1;for(let r=0;r<e.length;r++){const n=e[r],u=r===e.length-1;encodePacket(n,this.supportsBinary,s=>{try{this.doWrite(n,s)}catch{}u&&nextTick(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){typeof this.ws<"u"&&(this.ws.onerror=()=>{},this.ws.close(),this.ws=null)}uri(){const e=this.opts.secure?"wss":"ws",r=this.query||{};return this.opts.timestampRequests&&(r[this.opts.timestampParam]=randomString()),this.supportsBinary||(r.b64=1),this.createUri(e,r)}}const WebSocketCtor=globalThisShim.WebSocket||globalThisShim.MozWebSocket;class WS extends BaseWS{createSocket(e,r,n){return isReactNative?new WebSocketCtor(e,r,n):r?new WebSocketCtor(e,r):new WebSocketCtor(e)}doWrite(e,r){this.ws.send(r)}}class WT extends Transport{get name(){return"webtransport"}doOpen(){try{this._transport=new WebTransport(this.createUri("https"),this.opts.transportOptions[this.name])}catch(e){return this.emitReserved("error",e)}this._transport.closed.then(()=>{this.onClose()}).catch(e=>{this.onError("webtransport error",e)}),this._transport.ready.then(()=>{this._transport.createBidirectionalStream().then(e=>{const r=createPacketDecoderStream(Number.MAX_SAFE_INTEGER,this.socket.binaryType),n=e.readable.pipeThrough(r).getReader(),u=createPacketEncoderStream();u.readable.pipeTo(e.writable),this._writer=u.writable.getWriter();const s=()=>{n.read().then(({done:l,value:D})=>{l||(this.onPacket(D),s())}).catch(l=>{})};s();const o={type:"open"};this.query.sid&&(o.data=`{"sid":"${this.query.sid}"}`),this._writer.write(o).then(()=>this.onOpen())})})}write(e){this.writable=!1;for(let r=0;r<e.length;r++){const n=e[r],u=r===e.length-1;this._writer.write(n).then(()=>{u&&nextTick(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){var e;(e=this._transport)===null||e===void 0||e.close()}}const transports={websocket:WS,webtransport:WT,polling:XHR},re=/^(?:(?![^:@\/?#]+:[^:@\/]*@)(http|https|ws|wss):\/\/)?((?:(([^:@\/?#]*)(?::([^:@\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/,parts=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"];function parse$1(t){if(t.length>8e3)throw"URI too long";const e=t,r=t.indexOf("["),n=t.indexOf("]");r!=-1&&n!=-1&&(t=t.substring(0,r)+t.substring(r,n).replace(/:/g,";")+t.substring(n,t.length));let u=re.exec(t||""),s={},o=14;for(;o--;)s[parts[o]]=u[o]||"";return r!=-1&&n!=-1&&(s.source=e,s.host=s.host.substring(1,s.host.length-1).replace(/;/g,":"),s.authority=s.authority.replace("[","").replace("]","").replace(/;/g,":"),s.ipv6uri=!0),s.pathNames=pathNames(s,s.path),s.queryKey=queryKey(s,s.query),s}function pathNames(t,e){const r=/\/{2,9}/g,n=e.replace(r,"/").split("/");return(e.slice(0,1)=="/"||e.length===0)&&n.splice(0,1),e.slice(-1)=="/"&&n.splice(n.length-1,1),n}function queryKey(t,e){const r={};return e.replace(/(?:^|&)([^&=]*)=?([^&]*)/g,function(n,u,s){u&&(r[u]=s)}),r}const withEventListeners=typeof addEventListener=="function"&&typeof removeEventListener=="function",OFFLINE_EVENT_LISTENERS=[];withEventListeners&&addEventListener("offline",()=>{OFFLINE_EVENT_LISTENERS.forEach(t=>t())},!1);class SocketWithoutUpgrade extends Emitter{constructor(e,r){if(super(),this.binaryType=defaultBinaryType,this.writeBuffer=[],this._prevBufferLen=0,this._pingInterval=-1,this._pingTimeout=-1,this._maxPayload=-1,this._pingTimeoutTime=1/0,e&&typeof e=="object"&&(r=e,e=null),e){const n=parse$1(e);r.hostname=n.host,r.secure=n.protocol==="https"||n.protocol==="wss",r.port=n.port,n.query&&(r.query=n.query)}else r.host&&(r.hostname=parse$1(r.host).host);installTimerFunctions(this,r),this.secure=r.secure!=null?r.secure:typeof location<"u"&&location.protocol==="https:",r.hostname&&!r.port&&(r.port=this.secure?"443":"80"),this.hostname=r.hostname||(typeof location<"u"?location.hostname:"localhost"),this.port=r.port||(typeof location<"u"&&location.port?location.port:this.secure?"443":"80"),this.transports=[],this._transportsByName={},r.transports.forEach(n=>{const u=n.prototype.name;this.transports.push(u),this._transportsByName[u]=n}),this.opts=Object.assign({path:"/engine.io",agent:!1,withCredentials:!1,upgrade:!0,timestampParam:"t",rememberUpgrade:!1,addTrailingSlash:!0,rejectUnauthorized:!0,perMessageDeflate:{threshold:1024},transportOptions:{},closeOnBeforeunload:!1},r),this.opts.path=this.opts.path.replace(/\/$/,"")+(this.opts.addTrailingSlash?"/":""),typeof this.opts.query=="string"&&(this.opts.query=decode(this.opts.query)),withEventListeners&&(this.opts.closeOnBeforeunload&&(this._beforeunloadEventListener=()=>{this.transport&&(this.transport.removeAllListeners(),this.transport.close())},addEventListener("beforeunload",this._beforeunloadEventListener,!1)),this.hostname!=="localhost"&&(this._offlineEventListener=()=>{this._onClose("transport close",{description:"network connection lost"})},OFFLINE_EVENT_LISTENERS.push(this._offlineEventListener))),this.opts.withCredentials&&(this._cookieJar=void 0),this._open()}createTransport(e){const r=Object.assign({},this.opts.query);r.EIO=protocol$1,r.transport=e,this.id&&(r.sid=this.id);const n=Object.assign({},this.opts,{query:r,socket:this,hostname:this.hostname,secure:this.secure,port:this.port},this.opts.transportOptions[e]);return new this._transportsByName[e](n)}_open(){if(this.transports.length===0){this.setTimeoutFn(()=>{this.emitReserved("error","No transports available")},0);return}const e=this.opts.rememberUpgrade&&SocketWithoutUpgrade.priorWebsocketSuccess&&this.transports.indexOf("websocket")!==-1?"websocket":this.transports[0];this.readyState="opening";const r=this.createTransport(e);r.open(),this.setTransport(r)}setTransport(e){this.transport&&this.transport.removeAllListeners(),this.transport=e,e.on("drain",this._onDrain.bind(this)).on("packet",this._onPacket.bind(this)).on("error",this._onError.bind(this)).on("close",r=>this._onClose("transport close",r))}onOpen(){this.readyState="open",SocketWithoutUpgrade.priorWebsocketSuccess=this.transport.name==="websocket",this.emitReserved("open"),this.flush()}_onPacket(e){if(this.readyState==="opening"||this.readyState==="open"||this.readyState==="closing")switch(this.emitReserved("packet",e),this.emitReserved("heartbeat"),e.type){case"open":this.onHandshake(JSON.parse(e.data));break;case"ping":this._sendPacket("pong"),this.emitReserved("ping"),this.emitReserved("pong"),this._resetPingTimeout();break;case"error":const r=new Error("server error");r.code=e.data,this._onError(r);break;case"message":this.emitReserved("data",e.data),this.emitReserved("message",e.data);break}}onHandshake(e){this.emitReserved("handshake",e),this.id=e.sid,this.transport.query.sid=e.sid,this._pingInterval=e.pingInterval,this._pingTimeout=e.pingTimeout,this._maxPayload=e.maxPayload,this.onOpen(),this.readyState!=="closed"&&this._resetPingTimeout()}_resetPingTimeout(){this.clearTimeoutFn(this._pingTimeoutTimer);const e=this._pingInterval+this._pingTimeout;this._pingTimeoutTime=Date.now()+e,this._pingTimeoutTimer=this.setTimeoutFn(()=>{this._onClose("ping timeout")},e),this.opts.autoUnref&&this._pingTimeoutTimer.unref()}_onDrain(){this.writeBuffer.splice(0,this._prevBufferLen),this._prevBufferLen=0,this.writeBuffer.length===0?this.emitReserved("drain"):this.flush()}flush(){if(this.readyState!=="closed"&&this.transport.writable&&!this.upgrading&&this.writeBuffer.length){const e=this._getWritablePackets();this.transport.send(e),this._prevBufferLen=e.length,this.emitReserved("flush")}}_getWritablePackets(){if(!(this._maxPayload&&this.transport.name==="polling"&&this.writeBuffer.length>1))return this.writeBuffer;let r=1;for(let n=0;n<this.writeBuffer.length;n++){const u=this.writeBuffer[n].data;if(u&&(r+=byteLength(u)),n>0&&r>this._maxPayload)return this.writeBuffer.slice(0,n);r+=2}return this.writeBuffer}_hasPingExpired(){if(!this._pingTimeoutTime)return!0;const e=Date.now()>this._pingTimeoutTime;return e&&(this._pingTimeoutTime=0,nextTick(()=>{this._onClose("ping timeout")},this.setTimeoutFn)),e}write(e,r,n){return this._sendPacket("message",e,r,n),this}send(e,r,n){return this._sendPacket("message",e,r,n),this}_sendPacket(e,r,n,u){if(typeof r=="function"&&(u=r,r=void 0),typeof n=="function"&&(u=n,n=null),this.readyState==="closing"||this.readyState==="closed")return;n=n||{},n.compress=n.compress!==!1;const s={type:e,data:r,options:n};this.emitReserved("packetCreate",s),this.writeBuffer.push(s),u&&this.once("flush",u),this.flush()}close(){const e=()=>{this._onClose("forced close"),this.transport.close()},r=()=>{this.off("upgrade",r),this.off("upgradeError",r),e()},n=()=>{this.once("upgrade",r),this.once("upgradeError",r)};return(this.readyState==="opening"||this.readyState==="open")&&(this.readyState="closing",this.writeBuffer.length?this.once("drain",()=>{this.upgrading?n():e()}):this.upgrading?n():e()),this}_onError(e){if(SocketWithoutUpgrade.priorWebsocketSuccess=!1,this.opts.tryAllTransports&&this.transports.length>1&&this.readyState==="opening")return this.transports.shift(),this._open();this.emitReserved("error",e),this._onClose("transport error",e)}_onClose(e,r){if(this.readyState==="opening"||this.readyState==="open"||this.readyState==="closing"){if(this.clearTimeoutFn(this._pingTimeoutTimer),this.transport.removeAllListeners("close"),this.transport.close(),this.transport.removeAllListeners(),withEventListeners&&(this._beforeunloadEventListener&&removeEventListener("beforeunload",this._beforeunloadEventListener,!1),this._offlineEventListener)){const n=OFFLINE_EVENT_LISTENERS.indexOf(this._offlineEventListener);n!==-1&&OFFLINE_EVENT_LISTENERS.splice(n,1)}this.readyState="closed",this.id=null,this.emitReserved("close",e,r),this.writeBuffer=[],this._prevBufferLen=0}}}SocketWithoutUpgrade.protocol=protocol$1;class SocketWithUpgrade extends SocketWithoutUpgrade{constructor(){super(...arguments),this._upgrades=[]}onOpen(){if(super.onOpen(),this.readyState==="open"&&this.opts.upgrade)for(let e=0;e<this._upgrades.length;e++)this._probe(this._upgrades[e])}_probe(e){let r=this.createTransport(e),n=!1;SocketWithoutUpgrade.priorWebsocketSuccess=!1;const u=()=>{n||(r.send([{type:"ping",data:"probe"}]),r.once("packet",_=>{if(!n)if(_.type==="pong"&&_.data==="probe"){if(this.upgrading=!0,this.emitReserved("upgrading",r),!r)return;SocketWithoutUpgrade.priorWebsocketSuccess=r.name==="websocket",this.transport.pause(()=>{n||this.readyState!=="closed"&&(C(),this.setTransport(r),r.send([{type:"upgrade"}]),this.emitReserved("upgrade",r),r=null,this.upgrading=!1,this.flush())})}else{const p=new Error("probe error");p.transport=r.name,this.emitReserved("upgradeError",p)}}))};function s(){n||(n=!0,C(),r.close(),r=null)}const o=_=>{const p=new Error("probe error: "+_);p.transport=r.name,s(),this.emitReserved("upgradeError",p)};function l(){o("transport closed")}function D(){o("socket closed")}function y(_){r&&_.name!==r.name&&s()}const C=()=>{r.removeListener("open",u),r.removeListener("error",o),r.removeListener("close",l),this.off("close",D),this.off("upgrading",y)};r.once("open",u),r.once("error",o),r.once("close",l),this.once("close",D),this.once("upgrading",y),this._upgrades.indexOf("webtransport")!==-1&&e!=="webtransport"?this.setTimeoutFn(()=>{n||r.open()},200):r.open()}onHandshake(e){this._upgrades=this._filterUpgrades(e.upgrades),super.onHandshake(e)}_filterUpgrades(e){const r=[];for(let n=0;n<e.length;n++)~this.transports.indexOf(e[n])&&r.push(e[n]);return r}}let Socket$1=class extends SocketWithUpgrade{constructor(e,r={}){const n=typeof e=="object"?e:r;(!n.transports||n.transports&&typeof n.transports[0]=="string")&&(n.transports=(n.transports||["polling","websocket","webtransport"]).map(u=>transports[u]).filter(u=>!!u)),super(e,n)}};function url(t,e="",r){let n=t;r=r||typeof location<"u"&&location,t==null&&(t=r.protocol+"//"+r.host),typeof t=="string"&&(t.charAt(0)==="/"&&(t.charAt(1)==="/"?t=r.protocol+t:t=r.host+t),/^(https?|wss?):\/\//.test(t)||(typeof r<"u"?t=r.protocol+"//"+t:t="https://"+t),n=parse$1(t)),n.port||(/^(http|ws)$/.test(n.protocol)?n.port="80":/^(http|ws)s$/.test(n.protocol)&&(n.port="443")),n.path=n.path||"/";const s=n.host.indexOf(":")!==-1?"["+n.host+"]":n.host;return n.id=n.protocol+"://"+s+":"+n.port+e,n.href=n.protocol+"://"+s+(r&&r.port===n.port?"":":"+n.port),n}const withNativeArrayBuffer=typeof ArrayBuffer=="function",isView=t=>typeof ArrayBuffer.isView=="function"?ArrayBuffer.isView(t):t.buffer instanceof ArrayBuffer,toString=Object.prototype.toString,withNativeBlob=typeof Blob=="function"||typeof Blob<"u"&&toString.call(Blob)==="[object BlobConstructor]",withNativeFile=typeof File=="function"||typeof File<"u"&&toString.call(File)==="[object FileConstructor]";function isBinary(t){return withNativeArrayBuffer&&(t instanceof ArrayBuffer||isView(t))||withNativeBlob&&t instanceof Blob||withNativeFile&&t instanceof File}function hasBinary(t,e){if(!t||typeof t!="object")return!1;if(Array.isArray(t)){for(let r=0,n=t.length;r<n;r++)if(hasBinary(t[r]))return!0;return!1}if(isBinary(t))return!0;if(t.toJSON&&typeof t.toJSON=="function"&&arguments.length===1)return hasBinary(t.toJSON(),!0);for(const r in t)if(Object.prototype.hasOwnProperty.call(t,r)&&hasBinary(t[r]))return!0;return!1}function deconstructPacket(t){const e=[],r=t.data,n=t;return n.data=_deconstructPacket(r,e),n.attachments=e.length,{packet:n,buffers:e}}function _deconstructPacket(t,e){if(!t)return t;if(isBinary(t)){const r={_placeholder:!0,num:e.length};return e.push(t),r}else if(Array.isArray(t)){const r=new Array(t.length);for(let n=0;n<t.length;n++)r[n]=_deconstructPacket(t[n],e);return r}else if(typeof t=="object"&&!(t instanceof Date)){const r={};for(const n in t)Object.prototype.hasOwnProperty.call(t,n)&&(r[n]=_deconstructPacket(t[n],e));return r}return t}function reconstructPacket(t,e){return t.data=_reconstructPacket(t.data,e),delete t.attachments,t}function _reconstructPacket(t,e){if(!t)return t;if(t&&t._placeholder===!0){if(typeof t.num=="number"&&t.num>=0&&t.num<e.length)return e[t.num];throw new Error("illegal attachments")}else if(Array.isArray(t))for(let r=0;r<t.length;r++)t[r]=_reconstructPacket(t[r],e);else if(typeof t=="object")for(const r in t)Object.prototype.hasOwnProperty.call(t,r)&&(t[r]=_reconstructPacket(t[r],e));return t}const RESERVED_EVENTS$1=["connect","connect_error","disconnect","disconnecting","newListener","removeListener"],protocol=5;var PacketType;(function(t){t[t.CONNECT=0]="CONNECT",t[t.DISCONNECT=1]="DISCONNECT",t[t.EVENT=2]="EVENT",t[t.ACK=3]="ACK",t[t.CONNECT_ERROR=4]="CONNECT_ERROR",t[t.BINARY_EVENT=5]="BINARY_EVENT",t[t.BINARY_ACK=6]="BINARY_ACK"})(PacketType||(PacketType={}));class Encoder{constructor(e){this.replacer=e}encode(e){return(e.type===PacketType.EVENT||e.type===PacketType.ACK)&&hasBinary(e)?this.encodeAsBinary({type:e.type===PacketType.EVENT?PacketType.BINARY_EVENT:PacketType.BINARY_ACK,nsp:e.nsp,data:e.data,id:e.id}):[this.encodeAsString(e)]}encodeAsString(e){let r=""+e.type;return(e.type===PacketType.BINARY_EVENT||e.type===PacketType.BINARY_ACK)&&(r+=e.attachments+"-"),e.nsp&&e.nsp!=="/"&&(r+=e.nsp+","),e.id!=null&&(r+=e.id),e.data!=null&&(r+=JSON.stringify(e.data,this.replacer)),r}encodeAsBinary(e){const r=deconstructPacket(e),n=this.encodeAsString(r.packet),u=r.buffers;return u.unshift(n),u}}function isObject(t){return Object.prototype.toString.call(t)==="[object Object]"}class Decoder extends Emitter{constructor(e){super(),this.reviver=e}add(e){let r;if(typeof e=="string"){if(this.reconstructor)throw new Error("got plaintext data when reconstructing a packet");r=this.decodeString(e);const n=r.type===PacketType.BINARY_EVENT;n||r.type===PacketType.BINARY_ACK?(r.type=n?PacketType.EVENT:PacketType.ACK,this.reconstructor=new BinaryReconstructor(r),r.attachments===0&&super.emitReserved("decoded",r)):super.emitReserved("decoded",r)}else if(isBinary(e)||e.base64)if(this.reconstructor)r=this.reconstructor.takeBinaryData(e),r&&(this.reconstructor=null,super.emitReserved("decoded",r));else throw new Error("got binary data when not reconstructing a packet");else throw new Error("Unknown type: "+e)}decodeString(e){let r=0;const n={type:Number(e.charAt(0))};if(PacketType[n.type]===void 0)throw new Error("unknown packet type "+n.type);if(n.type===PacketType.BINARY_EVENT||n.type===PacketType.BINARY_ACK){const s=r+1;for(;e.charAt(++r)!=="-"&&r!=e.length;);const o=e.substring(s,r);if(o!=Number(o)||e.charAt(r)!=="-")throw new Error("Illegal attachments");n.attachments=Number(o)}if(e.charAt(r+1)==="/"){const s=r+1;for(;++r&&!(e.charAt(r)===","||r===e.length););n.nsp=e.substring(s,r)}else n.nsp="/";const u=e.charAt(r+1);if(u!==""&&Number(u)==u){const s=r+1;for(;++r;){const o=e.charAt(r);if(o==null||Number(o)!=o){--r;break}if(r===e.length)break}n.id=Number(e.substring(s,r+1))}if(e.charAt(++r)){const s=this.tryParse(e.substr(r));if(Decoder.isPayloadValid(n.type,s))n.data=s;else throw new Error("invalid payload")}return n}tryParse(e){try{return JSON.parse(e,this.reviver)}catch{return!1}}static isPayloadValid(e,r){switch(e){case PacketType.CONNECT:return isObject(r);case PacketType.DISCONNECT:return r===void 0;case PacketType.CONNECT_ERROR:return typeof r=="string"||isObject(r);case PacketType.EVENT:case PacketType.BINARY_EVENT:return Array.isArray(r)&&(typeof r[0]=="number"||typeof r[0]=="string"&&RESERVED_EVENTS$1.indexOf(r[0])===-1);case PacketType.ACK:case PacketType.BINARY_ACK:return Array.isArray(r)}}destroy(){this.reconstructor&&(this.reconstructor.finishedReconstruction(),this.reconstructor=null)}}class BinaryReconstructor{constructor(e){this.packet=e,this.buffers=[],this.reconPack=e}takeBinaryData(e){if(this.buffers.push(e),this.buffers.length===this.reconPack.attachments){const r=reconstructPacket(this.reconPack,this.buffers);return this.finishedReconstruction(),r}return null}finishedReconstruction(){this.reconPack=null,this.buffers=[]}}const parser=Object.freeze(Object.defineProperty({__proto__:null,Decoder,Encoder,get PacketType(){return PacketType},protocol},Symbol.toStringTag,{value:"Module"}));function on(t,e,r){return t.on(e,r),function(){t.off(e,r)}}const RESERVED_EVENTS=Object.freeze({connect:1,connect_error:1,disconnect:1,disconnecting:1,newListener:1,removeListener:1});class Socket extends Emitter{constructor(e,r,n){super(),this.connected=!1,this.recovered=!1,this.receiveBuffer=[],this.sendBuffer=[],this._queue=[],this._queueSeq=0,this.ids=0,this.acks={},this.flags={},this.io=e,this.nsp=r,n&&n.auth&&(this.auth=n.auth),this._opts=Object.assign({},n),this.io._autoConnect&&this.open()}get disconnected(){return!this.connected}subEvents(){if(this.subs)return;const e=this.io;this.subs=[on(e,"open",this.onopen.bind(this)),on(e,"packet",this.onpacket.bind(this)),on(e,"error",this.onerror.bind(this)),on(e,"close",this.onclose.bind(this))]}get active(){return!!this.subs}connect(){return this.connected?this:(this.subEvents(),this.io._reconnecting||this.io.open(),this.io._readyState==="open"&&this.onopen(),this)}open(){return this.connect()}send(...e){return e.unshift("message"),this.emit.apply(this,e),this}emit(e,...r){var n,u,s;if(RESERVED_EVENTS.hasOwnProperty(e))throw new Error('"'+e.toString()+'" is a reserved event name');if(r.unshift(e),this._opts.retries&&!this.flags.fromQueue&&!this.flags.volatile)return this._addToQueue(r),this;const o={type:PacketType.EVENT,data:r};if(o.options={},o.options.compress=this.flags.compress!==!1,typeof r[r.length-1]=="function"){const C=this.ids++,_=r.pop();this._registerAckCallback(C,_),o.id=C}const l=(u=(n=this.io.engine)===null||n===void 0?void 0:n.transport)===null||u===void 0?void 0:u.writable,D=this.connected&&!(!((s=this.io.engine)===null||s===void 0)&&s._hasPingExpired());return this.flags.volatile&&!l||(D?(this.notifyOutgoingListeners(o),this.packet(o)):this.sendBuffer.push(o)),this.flags={},this}_registerAckCallback(e,r){var n;const u=(n=this.flags.timeout)!==null&&n!==void 0?n:this._opts.ackTimeout;if(u===void 0){this.acks[e]=r;return}const s=this.io.setTimeoutFn(()=>{delete this.acks[e];for(let l=0;l<this.sendBuffer.length;l++)this.sendBuffer[l].id===e&&this.sendBuffer.splice(l,1);r.call(this,new Error("operation has timed out"))},u),o=(...l)=>{this.io.clearTimeoutFn(s),r.apply(this,l)};o.withError=!0,this.acks[e]=o}emitWithAck(e,...r){return new Promise((n,u)=>{const s=(o,l)=>o?u(o):n(l);s.withError=!0,r.push(s),this.emit(e,...r)})}_addToQueue(e){let r;typeof e[e.length-1]=="function"&&(r=e.pop());const n={id:this._queueSeq++,tryCount:0,pending:!1,args:e,flags:Object.assign({fromQueue:!0},this.flags)};e.push((u,...s)=>n!==this._queue[0]?void 0:(u!==null?n.tryCount>this._opts.retries&&(this._queue.shift(),r&&r(u)):(this._queue.shift(),r&&r(null,...s)),n.pending=!1,this._drainQueue())),this._queue.push(n),this._drainQueue()}_drainQueue(e=!1){if(!this.connected||this._queue.length===0)return;const r=this._queue[0];r.pending&&!e||(r.pending=!0,r.tryCount++,this.flags=r.flags,this.emit.apply(this,r.args))}packet(e){e.nsp=this.nsp,this.io._packet(e)}onopen(){typeof this.auth=="function"?this.auth(e=>{this._sendConnectPacket(e)}):this._sendConnectPacket(this.auth)}_sendConnectPacket(e){this.packet({type:PacketType.CONNECT,data:this._pid?Object.assign({pid:this._pid,offset:this._lastOffset},e):e})}onerror(e){this.connected||this.emitReserved("connect_error",e)}onclose(e,r){this.connected=!1,delete this.id,this.emitReserved("disconnect",e,r),this._clearAcks()}_clearAcks(){Object.keys(this.acks).forEach(e=>{if(!this.sendBuffer.some(n=>String(n.id)===e)){const n=this.acks[e];delete this.acks[e],n.withError&&n.call(this,new Error("socket has been disconnected"))}})}onpacket(e){if(e.nsp===this.nsp)switch(e.type){case PacketType.CONNECT:e.data&&e.data.sid?this.onconnect(e.data.sid,e.data.pid):this.emitReserved("connect_error",new Error("It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)"));break;case PacketType.EVENT:case PacketType.BINARY_EVENT:this.onevent(e);break;case PacketType.ACK:case PacketType.BINARY_ACK:this.onack(e);break;case PacketType.DISCONNECT:this.ondisconnect();break;case PacketType.CONNECT_ERROR:this.destroy();const n=new Error(e.data.message);n.data=e.data.data,this.emitReserved("connect_error",n);break}}onevent(e){const r=e.data||[];e.id!=null&&r.push(this.ack(e.id)),this.connected?this.emitEvent(r):this.receiveBuffer.push(Object.freeze(r))}emitEvent(e){if(this._anyListeners&&this._anyListeners.length){const r=this._anyListeners.slice();for(const n of r)n.apply(this,e)}super.emit.apply(this,e),this._pid&&e.length&&typeof e[e.length-1]=="string"&&(this._lastOffset=e[e.length-1])}ack(e){const r=this;let n=!1;return function(...u){n||(n=!0,r.packet({type:PacketType.ACK,id:e,data:u}))}}onack(e){const r=this.acks[e.id];typeof r=="function"&&(delete this.acks[e.id],r.withError&&e.data.unshift(null),r.apply(this,e.data))}onconnect(e,r){this.id=e,this.recovered=r&&this._pid===r,this._pid=r,this.connected=!0,this.emitBuffered(),this.emitReserved("connect"),this._drainQueue(!0)}emitBuffered(){this.receiveBuffer.forEach(e=>this.emitEvent(e)),this.receiveBuffer=[],this.sendBuffer.forEach(e=>{this.notifyOutgoingListeners(e),this.packet(e)}),this.sendBuffer=[]}ondisconnect(){this.destroy(),this.onclose("io server disconnect")}destroy(){this.subs&&(this.subs.forEach(e=>e()),this.subs=void 0),this.io._destroy(this)}disconnect(){return this.connected&&this.packet({type:PacketType.DISCONNECT}),this.destroy(),this.connected&&this.onclose("io client disconnect"),this}close(){return this.disconnect()}compress(e){return this.flags.compress=e,this}get volatile(){return this.flags.volatile=!0,this}timeout(e){return this.flags.timeout=e,this}onAny(e){return this._anyListeners=this._anyListeners||[],this._anyListeners.push(e),this}prependAny(e){return this._anyListeners=this._anyListeners||[],this._anyListeners.unshift(e),this}offAny(e){if(!this._anyListeners)return this;if(e){const r=this._anyListeners;for(let n=0;n<r.length;n++)if(e===r[n])return r.splice(n,1),this}else this._anyListeners=[];return this}listenersAny(){return this._anyListeners||[]}onAnyOutgoing(e){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.push(e),this}prependAnyOutgoing(e){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.unshift(e),this}offAnyOutgoing(e){if(!this._anyOutgoingListeners)return this;if(e){const r=this._anyOutgoingListeners;for(let n=0;n<r.length;n++)if(e===r[n])return r.splice(n,1),this}else this._anyOutgoingListeners=[];return this}listenersAnyOutgoing(){return this._anyOutgoingListeners||[]}notifyOutgoingListeners(e){if(this._anyOutgoingListeners&&this._anyOutgoingListeners.length){const r=this._anyOutgoingListeners.slice();for(const n of r)n.apply(this,e.data)}}}function Backoff(t){t=t||{},this.ms=t.min||100,this.max=t.max||1e4,this.factor=t.factor||2,this.jitter=t.jitter>0&&t.jitter<=1?t.jitter:0,this.attempts=0}Backoff.prototype.duration=function(){var t=this.ms*Math.pow(this.factor,this.attempts++);if(this.jitter){var e=Math.random(),r=Math.floor(e*this.jitter*t);t=(Math.floor(e*10)&1)==0?t-r:t+r}return Math.min(t,this.max)|0};Backoff.prototype.reset=function(){this.attempts=0};Backoff.prototype.setMin=function(t){this.ms=t};Backoff.prototype.setMax=function(t){this.max=t};Backoff.prototype.setJitter=function(t){this.jitter=t};class Manager extends Emitter{constructor(e,r){var n;super(),this.nsps={},this.subs=[],e&&typeof e=="object"&&(r=e,e=void 0),r=r||{},r.path=r.path||"/socket.io",this.opts=r,installTimerFunctions(this,r),this.reconnection(r.reconnection!==!1),this.reconnectionAttempts(r.reconnectionAttempts||1/0),this.reconnectionDelay(r.reconnectionDelay||1e3),this.reconnectionDelayMax(r.reconnectionDelayMax||5e3),this.randomizationFactor((n=r.randomizationFactor)!==null&&n!==void 0?n:.5),this.backoff=new Backoff({min:this.reconnectionDelay(),max:this.reconnectionDelayMax(),jitter:this.randomizationFactor()}),this.timeout(r.timeout==null?2e4:r.timeout),this._readyState="closed",this.uri=e;const u=r.parser||parser;this.encoder=new u.Encoder,this.decoder=new u.Decoder,this._autoConnect=r.autoConnect!==!1,this._autoConnect&&this.open()}reconnection(e){return arguments.length?(this._reconnection=!!e,e||(this.skipReconnect=!0),this):this._reconnection}reconnectionAttempts(e){return e===void 0?this._reconnectionAttempts:(this._reconnectionAttempts=e,this)}reconnectionDelay(e){var r;return e===void 0?this._reconnectionDelay:(this._reconnectionDelay=e,(r=this.backoff)===null||r===void 0||r.setMin(e),this)}randomizationFactor(e){var r;return e===void 0?this._randomizationFactor:(this._randomizationFactor=e,(r=this.backoff)===null||r===void 0||r.setJitter(e),this)}reconnectionDelayMax(e){var r;return e===void 0?this._reconnectionDelayMax:(this._reconnectionDelayMax=e,(r=this.backoff)===null||r===void 0||r.setMax(e),this)}timeout(e){return arguments.length?(this._timeout=e,this):this._timeout}maybeReconnectOnOpen(){!this._reconnecting&&this._reconnection&&this.backoff.attempts===0&&this.reconnect()}open(e){if(~this._readyState.indexOf("open"))return this;this.engine=new Socket$1(this.uri,this.opts);const r=this.engine,n=this;this._readyState="opening",this.skipReconnect=!1;const u=on(r,"open",function(){n.onopen(),e&&e()}),s=l=>{this.cleanup(),this._readyState="closed",this.emitReserved("error",l),e?e(l):this.maybeReconnectOnOpen()},o=on(r,"error",s);if(this._timeout!==!1){const l=this._timeout,D=this.setTimeoutFn(()=>{u(),s(new Error("timeout")),r.close()},l);this.opts.autoUnref&&D.unref(),this.subs.push(()=>{this.clearTimeoutFn(D)})}return this.subs.push(u),this.subs.push(o),this}connect(e){return this.open(e)}onopen(){this.cleanup(),this._readyState="open",this.emitReserved("open");const e=this.engine;this.subs.push(on(e,"ping",this.onping.bind(this)),on(e,"data",this.ondata.bind(this)),on(e,"error",this.onerror.bind(this)),on(e,"close",this.onclose.bind(this)),on(this.decoder,"decoded",this.ondecoded.bind(this)))}onping(){this.emitReserved("ping")}ondata(e){try{this.decoder.add(e)}catch(r){this.onclose("parse error",r)}}ondecoded(e){nextTick(()=>{this.emitReserved("packet",e)},this.setTimeoutFn)}onerror(e){this.emitReserved("error",e)}socket(e,r){let n=this.nsps[e];return n?this._autoConnect&&!n.active&&n.connect():(n=new Socket(this,e,r),this.nsps[e]=n),n}_destroy(e){const r=Object.keys(this.nsps);for(const n of r)if(this.nsps[n].active)return;this._close()}_packet(e){const r=this.encoder.encode(e);for(let n=0;n<r.length;n++)this.engine.write(r[n],e.options)}cleanup(){this.subs.forEach(e=>e()),this.subs.length=0,this.decoder.destroy()}_close(){this.skipReconnect=!0,this._reconnecting=!1,this.onclose("forced close")}disconnect(){return this._close()}onclose(e,r){var n;this.cleanup(),(n=this.engine)===null||n===void 0||n.close(),this.backoff.reset(),this._readyState="closed",this.emitReserved("close",e,r),this._reconnection&&!this.skipReconnect&&this.reconnect()}reconnect(){if(this._reconnecting||this.skipReconnect)return this;const e=this;if(this.backoff.attempts>=this._reconnectionAttempts)this.backoff.reset(),this.emitReserved("reconnect_failed"),this._reconnecting=!1;else{const r=this.backoff.duration();this._reconnecting=!0;const n=this.setTimeoutFn(()=>{e.skipReconnect||(this.emitReserved("reconnect_attempt",e.backoff.attempts),!e.skipReconnect&&e.open(u=>{u?(e._reconnecting=!1,e.reconnect(),this.emitReserved("reconnect_error",u)):e.onreconnect()}))},r);this.opts.autoUnref&&n.unref(),this.subs.push(()=>{this.clearTimeoutFn(n)})}}onreconnect(){const e=this.backoff.attempts;this._reconnecting=!1,this.backoff.reset(),this.emitReserved("reconnect",e)}}const cache={};function lookup(t,e){typeof t=="object"&&(e=t,t=void 0),e=e||{};const r=url(t,e.path||"/socket.io"),n=r.source,u=r.id,s=r.path,o=cache[u]&&s in cache[u].nsps,l=e.forceNew||e["force new connection"]||e.multiplex===!1||o;let D;return l?D=new Manager(n,e):(cache[u]||(cache[u]=new Manager(n,e)),D=cache[u]),r.query&&!e.query&&(e.query=r.queryKey),D.socket(r.path,e)}Object.assign(lookup,{Manager,Socket,io:lookup,connect:lookup});var Space_Separator=/[\u1680\u2000-\u200A\u202F\u205F\u3000]/,ID_Start=/[\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u048A-\u052F\u0531-\u0556\u0559\u0561-\u0587\u05D0-\u05EA\u05F0-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u0860-\u086A\u08A0-\u08B4\u08B6-\u08BD\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0980\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u09FC\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0AF9\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D\u0C58-\u0C5A\u0C60\u0C61\u0C80\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D05-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D4E\u0D54-\u0D56\u0D5F-\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u170C\u170E-\u1711\u1720-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7\u17DC\u1820-\u1877\u1880-\u1884\u1887-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191E\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u1A00-\u1A16\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4B\u1B83-\u1BA0\u1BAE\u1BAF\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1C80-\u1C88\u1CE9-\u1CEC\u1CEE-\u1CF1\u1CF5\u1CF6\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2160-\u2188\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2E2F\u3005-\u3007\u3021-\u3029\u3031-\u3035\u3038-\u303C\u3041-\u3096\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312E\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FEA\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B\uA640-\uA66E\uA67F-\uA69D\uA6A0-\uA6EF\uA717-\uA71F\uA722-\uA788\uA78B-\uA7AE\uA7B0-\uA7B7\uA7F7-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB\uA8FD\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uA9E0-\uA9E4\uA9E6-\uA9EF\uA9FA-\uA9FE\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA7E-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB65\uAB70-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDD40-\uDD74\uDE80-\uDE9C\uDEA0-\uDED0\uDF00-\uDF1F\uDF2D-\uDF4A\uDF50-\uDF75\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF\uDFD1-\uDFD5]|\uD801[\uDC00-\uDC9D\uDCB0-\uDCD3\uDCD8-\uDCFB\uDD00-\uDD27\uDD30-\uDD63\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC60-\uDC76\uDC80-\uDC9E\uDCE0-\uDCF2\uDCF4\uDCF5\uDD00-\uDD15\uDD20-\uDD39\uDD80-\uDDB7\uDDBE\uDDBF\uDE00\uDE10-\uDE13\uDE15-\uDE17\uDE19-\uDE33\uDE60-\uDE7C\uDE80-\uDE9C\uDEC0-\uDEC7\uDEC9-\uDEE4\uDF00-\uDF35\uDF40-\uDF55\uDF60-\uDF72\uDF80-\uDF91]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2]|\uD804[\uDC03-\uDC37\uDC83-\uDCAF\uDCD0-\uDCE8\uDD03-\uDD26\uDD50-\uDD72\uDD76\uDD83-\uDDB2\uDDC1-\uDDC4\uDDDA\uDDDC\uDE00-\uDE11\uDE13-\uDE2B\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEDE\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3D\uDF50\uDF5D-\uDF61]|\uD805[\uDC00-\uDC34\uDC47-\uDC4A\uDC80-\uDCAF\uDCC4\uDCC5\uDCC7\uDD80-\uDDAE\uDDD8-\uDDDB\uDE00-\uDE2F\uDE44\uDE80-\uDEAA\uDF00-\uDF19]|\uD806[\uDCA0-\uDCDF\uDCFF\uDE00\uDE0B-\uDE32\uDE3A\uDE50\uDE5C-\uDE83\uDE86-\uDE89\uDEC0-\uDEF8]|\uD807[\uDC00-\uDC08\uDC0A-\uDC2E\uDC40\uDC72-\uDC8F\uDD00-\uDD06\uDD08\uDD09\uDD0B-\uDD30\uDD46]|\uD808[\uDC00-\uDF99]|\uD809[\uDC00-\uDC6E\uDC80-\uDD43]|[\uD80C\uD81C-\uD820\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872\uD874-\uD879][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2E]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDED0-\uDEED\uDF00-\uDF2F\uDF40-\uDF43\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDF00-\uDF44\uDF50\uDF93-\uDF9F\uDFE0\uDFE1]|\uD821[\uDC00-\uDFEC]|\uD822[\uDC00-\uDEF2]|\uD82C[\uDC00-\uDD1E\uDD70-\uDEFB]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB]|\uD83A[\uDC00-\uDCC4\uDD00-\uDD43]|\uD83B[\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD869[\uDC00-\uDED6\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF34\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1\uDEB0-\uDFFF]|\uD87A[\uDC00-\uDFE0]|\uD87E[\uDC00-\uDE1D]/,ID_Continue=/[\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0300-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u0483-\u0487\u048A-\u052F\u0531-\u0556\u0559\u0561-\u0587\u0591-\u05BD\u05BF\u05C1\u05C2\u05C4\u05C5\u05C7\u05D0-\u05EA\u05F0-\u05F2\u0610-\u061A\u0620-\u0669\u066E-\u06D3\u06D5-\u06DC\u06DF-\u06E8\u06EA-\u06FC\u06FF\u0710-\u074A\u074D-\u07B1\u07C0-\u07F5\u07FA\u0800-\u082D\u0840-\u085B\u0860-\u086A\u08A0-\u08B4\u08B6-\u08BD\u08D4-\u08E1\u08E3-\u0963\u0966-\u096F\u0971-\u0983\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BC-\u09C4\u09C7\u09C8\u09CB-\u09CE\u09D7\u09DC\u09DD\u09DF-\u09E3\u09E6-\u09F1\u09FC\u0A01-\u0A03\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A3C\u0A3E-\u0A42\u0A47\u0A48\u0A4B-\u0A4D\u0A51\u0A59-\u0A5C\u0A5E\u0A66-\u0A75\u0A81-\u0A83\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABC-\u0AC5\u0AC7-\u0AC9\u0ACB-\u0ACD\u0AD0\u0AE0-\u0AE3\u0AE6-\u0AEF\u0AF9-\u0AFF\u0B01-\u0B03\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3C-\u0B44\u0B47\u0B48\u0B4B-\u0B4D\u0B56\u0B57\u0B5C\u0B5D\u0B5F-\u0B63\u0B66-\u0B6F\u0B71\u0B82\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BBE-\u0BC2\u0BC6-\u0BC8\u0BCA-\u0BCD\u0BD0\u0BD7\u0BE6-\u0BEF\u0C00-\u0C03\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D-\u0C44\u0C46-\u0C48\u0C4A-\u0C4D\u0C55\u0C56\u0C58-\u0C5A\u0C60-\u0C63\u0C66-\u0C6F\u0C80-\u0C83\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBC-\u0CC4\u0CC6-\u0CC8\u0CCA-\u0CCD\u0CD5\u0CD6\u0CDE\u0CE0-\u0CE3\u0CE6-\u0CEF\u0CF1\u0CF2\u0D00-\u0D03\u0D05-\u0D0C\u0D0E-\u0D10\u0D12-\u0D44\u0D46-\u0D48\u0D4A-\u0D4E\u0D54-\u0D57\u0D5F-\u0D63\u0D66-\u0D6F\u0D7A-\u0D7F\u0D82\u0D83\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0DCA\u0DCF-\u0DD4\u0DD6\u0DD8-\u0DDF\u0DE6-\u0DEF\u0DF2\u0DF3\u0E01-\u0E3A\u0E40-\u0E4E\u0E50-\u0E59\u0E81\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB9\u0EBB-\u0EBD\u0EC0-\u0EC4\u0EC6\u0EC8-\u0ECD\u0ED0-\u0ED9\u0EDC-\u0EDF\u0F00\u0F18\u0F19\u0F20-\u0F29\u0F35\u0F37\u0F39\u0F3E-\u0F47\u0F49-\u0F6C\u0F71-\u0F84\u0F86-\u0F97\u0F99-\u0FBC\u0FC6\u1000-\u1049\u1050-\u109D\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u135D-\u135F\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u170C\u170E-\u1714\u1720-\u1734\u1740-\u1753\u1760-\u176C\u176E-\u1770\u1772\u1773\u1780-\u17D3\u17D7\u17DC\u17DD\u17E0-\u17E9\u180B-\u180D\u1810-\u1819\u1820-\u1877\u1880-\u18AA\u18B0-\u18F5\u1900-\u191E\u1920-\u192B\u1930-\u193B\u1946-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u19D0-\u19D9\u1A00-\u1A1B\u1A20-\u1A5E\u1A60-\u1A7C\u1A7F-\u1A89\u1A90-\u1A99\u1AA7\u1AB0-\u1ABD\u1B00-\u1B4B\u1B50-\u1B59\u1B6B-\u1B73\u1B80-\u1BF3\u1C00-\u1C37\u1C40-\u1C49\u1C4D-\u1C7D\u1C80-\u1C88\u1CD0-\u1CD2\u1CD4-\u1CF9\u1D00-\u1DF9\u1DFB-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u203F\u2040\u2054\u2071\u207F\u2090-\u209C\u20D0-\u20DC\u20E1\u20E5-\u20F0\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2160-\u2188\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D7F-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2DE0-\u2DFF\u2E2F\u3005-\u3007\u3021-\u302F\u3031-\u3035\u3038-\u303C\u3041-\u3096\u3099\u309A\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312E\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FEA\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA62B\uA640-\uA66F\uA674-\uA67D\uA67F-\uA6F1\uA717-\uA71F\uA722-\uA788\uA78B-\uA7AE\uA7B0-\uA7B7\uA7F7-\uA827\uA840-\uA873\uA880-\uA8C5\uA8D0-\uA8D9\uA8E0-\uA8F7\uA8FB\uA8FD\uA900-\uA92D\uA930-\uA953\uA960-\uA97C\uA980-\uA9C0\uA9CF-\uA9D9\uA9E0-\uA9FE\uAA00-\uAA36\uAA40-\uAA4D\uAA50-\uAA59\uAA60-\uAA76\uAA7A-\uAAC2\uAADB-\uAADD\uAAE0-\uAAEF\uAAF2-\uAAF6\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB65\uAB70-\uABEA\uABEC\uABED\uABF0-\uABF9\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE00-\uFE0F\uFE20-\uFE2F\uFE33\uFE34\uFE4D-\uFE4F\uFE70-\uFE74\uFE76-\uFEFC\uFF10-\uFF19\uFF21-\uFF3A\uFF3F\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDD40-\uDD74\uDDFD\uDE80-\uDE9C\uDEA0-\uDED0\uDEE0\uDF00-\uDF1F\uDF2D-\uDF4A\uDF50-\uDF7A\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF\uDFD1-\uDFD5]|\uD801[\uDC00-\uDC9D\uDCA0-\uDCA9\uDCB0-\uDCD3\uDCD8-\uDCFB\uDD00-\uDD27\uDD30-\uDD63\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC60-\uDC76\uDC80-\uDC9E\uDCE0-\uDCF2\uDCF4\uDCF5\uDD00-\uDD15\uDD20-\uDD39\uDD80-\uDDB7\uDDBE\uDDBF\uDE00-\uDE03\uDE05\uDE06\uDE0C-\uDE13\uDE15-\uDE17\uDE19-\uDE33\uDE38-\uDE3A\uDE3F\uDE60-\uDE7C\uDE80-\uDE9C\uDEC0-\uDEC7\uDEC9-\uDEE6\uDF00-\uDF35\uDF40-\uDF55\uDF60-\uDF72\uDF80-\uDF91]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2]|\uD804[\uDC00-\uDC46\uDC66-\uDC6F\uDC7F-\uDCBA\uDCD0-\uDCE8\uDCF0-\uDCF9\uDD00-\uDD34\uDD36-\uDD3F\uDD50-\uDD73\uDD76\uDD80-\uDDC4\uDDCA-\uDDCC\uDDD0-\uDDDA\uDDDC\uDE00-\uDE11\uDE13-\uDE37\uDE3E\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEEA\uDEF0-\uDEF9\uDF00-\uDF03\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3C-\uDF44\uDF47\uDF48\uDF4B-\uDF4D\uDF50\uDF57\uDF5D-\uDF63\uDF66-\uDF6C\uDF70-\uDF74]|\uD805[\uDC00-\uDC4A\uDC50-\uDC59\uDC80-\uDCC5\uDCC7\uDCD0-\uDCD9\uDD80-\uDDB5\uDDB8-\uDDC0\uDDD8-\uDDDD\uDE00-\uDE40\uDE44\uDE50-\uDE59\uDE80-\uDEB7\uDEC0-\uDEC9\uDF00-\uDF19\uDF1D-\uDF2B\uDF30-\uDF39]|\uD806[\uDCA0-\uDCE9\uDCFF\uDE00-\uDE3E\uDE47\uDE50-\uDE83\uDE86-\uDE99\uDEC0-\uDEF8]|\uD807[\uDC00-\uDC08\uDC0A-\uDC36\uDC38-\uDC40\uDC50-\uDC59\uDC72-\uDC8F\uDC92-\uDCA7\uDCA9-\uDCB6\uDD00-\uDD06\uDD08\uDD09\uDD0B-\uDD36\uDD3A\uDD3C\uDD3D\uDD3F-\uDD47\uDD50-\uDD59]|\uD808[\uDC00-\uDF99]|\uD809[\uDC00-\uDC6E\uDC80-\uDD43]|[\uD80C\uD81C-\uD820\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872\uD874-\uD879][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2E]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDE60-\uDE69\uDED0-\uDEED\uDEF0-\uDEF4\uDF00-\uDF36\uDF40-\uDF43\uDF50-\uDF59\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDF00-\uDF44\uDF50-\uDF7E\uDF8F-\uDF9F\uDFE0\uDFE1]|\uD821[\uDC00-\uDFEC]|\uD822[\uDC00-\uDEF2]|\uD82C[\uDC00-\uDD1E\uDD70-\uDEFB]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99\uDC9D\uDC9E]|\uD834[\uDD65-\uDD69\uDD6D-\uDD72\uDD7B-\uDD82\uDD85-\uDD8B\uDDAA-\uDDAD\uDE42-\uDE44]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB\uDFCE-\uDFFF]|\uD836[\uDE00-\uDE36\uDE3B-\uDE6C\uDE75\uDE84\uDE9B-\uDE9F\uDEA1-\uDEAF]|\uD838[\uDC00-\uDC06\uDC08-\uDC18\uDC1B-\uDC21\uDC23\uDC24\uDC26-\uDC2A]|\uD83A[\uDC00-\uDCC4\uDCD0-\uDCD6\uDD00-\uDD4A\uDD50-\uDD59]|\uD83B[\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD869[\uDC00-\uDED6\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF34\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1\uDEB0-\uDFFF]|\uD87A[\uDC00-\uDFE0]|\uD87E[\uDC00-\uDE1D]|\uDB40[\uDD00-\uDDEF]/,unicode={Space_Separator,ID_Start,ID_Continue},util={isSpaceSeparator(t){return typeof t=="string"&&unicode.Space_Separator.test(t)},isIdStartChar(t){return typeof t=="string"&&(t>="a"&&t<="z"||t>="A"&&t<="Z"||t==="$"||t==="_"||unicode.ID_Start.test(t))},isIdContinueChar(t){return typeof t=="string"&&(t>="a"&&t<="z"||t>="A"&&t<="Z"||t>="0"&&t<="9"||t==="$"||t==="_"||t==="‌"||t==="‍"||unicode.ID_Continue.test(t))},isDigit(t){return typeof t=="string"&&/[0-9]/.test(t)},isHexDigit(t){return typeof t=="string"&&/[0-9A-Fa-f]/.test(t)}};let source,parseState,stack,pos,line,column,token$1,key,root;var parse=function(e,r){source=String(e),parseState="start",stack=[],pos=0,line=1,column=0,token$1=void 0,key=void 0,root=void 0;do token$1=lex(),parseStates[parseState]();while(token$1.type!=="eof");return typeof r=="function"?internalize({"":root},"",r):root};function internalize(t,e,r){const n=t[e];if(n!=null&&typeof n=="object")if(Array.isArray(n))for(let u=0;u<n.length;u++){const s=String(u),o=internalize(n,s,r);o===void 0?delete n[s]:Object.defineProperty(n,s,{value:o,writable:!0,enumerable:!0,configurable:!0})}else for(const u in n){const s=internalize(n,u,r);s===void 0?delete n[u]:Object.defineProperty(n,u,{value:s,writable:!0,enumerable:!0,configurable:!0})}return r.call(t,e,n)}let lexState,buffer,doubleQuote,sign,c;function lex(){for(lexState="default",buffer="",doubleQuote=!1,sign=1;;){c=peek();const t=lexStates[lexState]();if(t)return t}}function peek(){if(source[pos])return String.fromCodePoint(source.codePointAt(pos))}function read(){const t=peek();return t===`
`?(line++,column=0):t?column+=t.length:column++,t&&(pos+=t.length),t}const lexStates={default(){switch(c){case"	":case"\v":case"\f":case" ":case" ":case"\uFEFF":case`
`:case"\r":case"\u2028":case"\u2029":read();return;case"/":read(),lexState="comment";return;case void 0:return read(),newToken("eof")}if(util.isSpaceSeparator(c)){read();return}return lexStates[parseState]()},comment(){switch(c){case"*":read(),lexState="multiLineComment";return;case"/":read(),lexState="singleLineComment";return}throw invalidChar(read())},multiLineComment(){switch(c){case"*":read(),lexState="multiLineCommentAsterisk";return;case void 0:throw invalidChar(read())}read()},multiLineCommentAsterisk(){switch(c){case"*":read();return;case"/":read(),lexState="default";return;case void 0:throw invalidChar(read())}read(),lexState="multiLineComment"},singleLineComment(){switch(c){case`
`:case"\r":case"\u2028":case"\u2029":read(),lexState="default";return;case void 0:return read(),newToken("eof")}read()},value(){switch(c){case"{":case"[":return newToken("punctuator",read());case"n":return read(),literal("ull"),newToken("null",null);case"t":return read(),literal("rue"),newToken("boolean",!0);case"f":return read(),literal("alse"),newToken("boolean",!1);case"-":case"+":read()==="-"&&(sign=-1),lexState="sign";return;case".":buffer=read(),lexState="decimalPointLeading";return;case"0":buffer=read(),lexState="zero";return;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":buffer=read(),lexState="decimalInteger";return;case"I":return read(),literal("nfinity"),newToken("numeric",1/0);case"N":return read(),literal("aN"),newToken("numeric",NaN);case'"':case"'":doubleQuote=read()==='"',buffer="",lexState="string";return}throw invalidChar(read())},identifierNameStartEscape(){if(c!=="u")throw invalidChar(read());read();const t=unicodeEscape();switch(t){case"$":case"_":break;default:if(!util.isIdStartChar(t))throw invalidIdentifier();break}buffer+=t,lexState="identifierName"},identifierName(){switch(c){case"$":case"_":case"‌":case"‍":buffer+=read();return;case"\\":read(),lexState="identifierNameEscape";return}if(util.isIdContinueChar(c)){buffer+=read();return}return newToken("identifier",buffer)},identifierNameEscape(){if(c!=="u")throw invalidChar(read());read();const t=unicodeEscape();switch(t){case"$":case"_":case"‌":case"‍":break;default:if(!util.isIdContinueChar(t))throw invalidIdentifier();break}buffer+=t,lexState="identifierName"},sign(){switch(c){case".":buffer=read(),lexState="decimalPointLeading";return;case"0":buffer=read(),lexState="zero";return;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":buffer=read(),lexState="decimalInteger";return;case"I":return read(),literal("nfinity"),newToken("numeric",sign*(1/0));case"N":return read(),literal("aN"),newToken("numeric",NaN)}throw invalidChar(read())},zero(){switch(c){case".":buffer+=read(),lexState="decimalPoint";return;case"e":case"E":buffer+=read(),lexState="decimalExponent";return;case"x":case"X":buffer+=read(),lexState="hexadecimal";return}return newToken("numeric",sign*0)},decimalInteger(){switch(c){case".":buffer+=read(),lexState="decimalPoint";return;case"e":case"E":buffer+=read(),lexState="decimalExponent";return}if(util.isDigit(c)){buffer+=read();return}return newToken("numeric",sign*Number(buffer))},decimalPointLeading(){if(util.isDigit(c)){buffer+=read(),lexState="decimalFraction";return}throw invalidChar(read())},decimalPoint(){switch(c){case"e":case"E":buffer+=read(),lexState="decimalExponent";return}if(util.isDigit(c)){buffer+=read(),lexState="decimalFraction";return}return newToken("numeric",sign*Number(buffer))},decimalFraction(){switch(c){case"e":case"E":buffer+=read(),lexState="decimalExponent";return}if(util.isDigit(c)){buffer+=read();return}return newToken("numeric",sign*Number(buffer))},decimalExponent(){switch(c){case"+":case"-":buffer+=read(),lexState="decimalExponentSign";return}if(util.isDigit(c)){buffer+=read(),lexState="decimalExponentInteger";return}throw invalidChar(read())},decimalExponentSign(){if(util.isDigit(c)){buffer+=read(),lexState="decimalExponentInteger";return}throw invalidChar(read())},decimalExponentInteger(){if(util.isDigit(c)){buffer+=read();return}return newToken("numeric",sign*Number(buffer))},hexadecimal(){if(util.isHexDigit(c)){buffer+=read(),lexState="hexadecimalInteger";return}throw invalidChar(read())},hexadecimalInteger(){if(util.isHexDigit(c)){buffer+=read();return}return newToken("numeric",sign*Number(buffer))},string(){switch(c){case"\\":read(),buffer+=escape();return;case'"':if(doubleQuote)return read(),newToken("string",buffer);buffer+=read();return;case"'":if(!doubleQuote)return read(),newToken("string",buffer);buffer+=read();return;case`
`:case"\r":throw invalidChar(read());case"\u2028":case"\u2029":separatorChar(c);break;case void 0:throw invalidChar(read())}buffer+=read()},start(){switch(c){case"{":case"[":return newToken("punctuator",read())}lexState="value"},beforePropertyName(){switch(c){case"$":case"_":buffer=read(),lexState="identifierName";return;case"\\":read(),lexState="identifierNameStartEscape";return;case"}":return newToken("punctuator",read());case'"':case"'":doubleQuote=read()==='"',lexState="string";return}if(util.isIdStartChar(c)){buffer+=read(),lexState="identifierName";return}throw invalidChar(read())},afterPropertyName(){if(c===":")return newToken("punctuator",read());throw invalidChar(read())},beforePropertyValue(){lexState="value"},afterPropertyValue(){switch(c){case",":case"}":return newToken("punctuator",read())}throw invalidChar(read())},beforeArrayValue(){if(c==="]")return newToken("punctuator",read());lexState="value"},afterArrayValue(){switch(c){case",":case"]":return newToken("punctuator",read())}throw invalidChar(read())},end(){throw invalidChar(read())}};function newToken(t,e){return{type:t,value:e,line,column}}function literal(t){for(const e of t){if(peek()!==e)throw invalidChar(read());read()}}function escape(){switch(peek()){case"b":return read(),"\b";case"f":return read(),"\f";case"n":return read(),`
`;case"r":return read(),"\r";case"t":return read(),"	";case"v":return read(),"\v";case"0":if(read(),util.isDigit(peek()))throw invalidChar(read());return"\0";case"x":return read(),hexEscape();case"u":return read(),unicodeEscape();case`
`:case"\u2028":case"\u2029":return read(),"";case"\r":return read(),peek()===`
`&&read(),"";case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":throw invalidChar(read());case void 0:throw invalidChar(read())}return read()}function hexEscape(){let t="",e=peek();if(!util.isHexDigit(e)||(t+=read(),e=peek(),!util.isHexDigit(e)))throw invalidChar(read());return t+=read(),String.fromCodePoint(parseInt(t,16))}function unicodeEscape(){let t="",e=4;for(;e-- >0;){const r=peek();if(!util.isHexDigit(r))throw invalidChar(read());t+=read()}return String.fromCodePoint(parseInt(t,16))}const parseStates={start(){if(token$1.type==="eof")throw invalidEOF();push()},beforePropertyName(){switch(token$1.type){case"identifier":case"string":key=token$1.value,parseState="afterPropertyName";return;case"punctuator":pop();return;case"eof":throw invalidEOF()}},afterPropertyName(){if(token$1.type==="eof")throw invalidEOF();parseState="beforePropertyValue"},beforePropertyValue(){if(token$1.type==="eof")throw invalidEOF();push()},beforeArrayValue(){if(token$1.type==="eof")throw invalidEOF();if(token$1.type==="punctuator"&&token$1.value==="]"){pop();return}push()},afterPropertyValue(){if(token$1.type==="eof")throw invalidEOF();switch(token$1.value){case",":parseState="beforePropertyName";return;case"}":pop()}},afterArrayValue(){if(token$1.type==="eof")throw invalidEOF();switch(token$1.value){case",":parseState="beforeArrayValue";return;case"]":pop()}},end(){}};function push(){let t;switch(token$1.type){case"punctuator":switch(token$1.value){case"{":t={};break;case"[":t=[];break}break;case"null":case"boolean":case"numeric":case"string":t=token$1.value;break}if(root===void 0)root=t;else{const e=stack[stack.length-1];Array.isArray(e)?e.push(t):Object.defineProperty(e,key,{value:t,writable:!0,enumerable:!0,configurable:!0})}if(t!==null&&typeof t=="object")stack.push(t),Array.isArray(t)?parseState="beforeArrayValue":parseState="beforePropertyName";else{const e=stack[stack.length-1];e==null?parseState="end":Array.isArray(e)?parseState="afterArrayValue":parseState="afterPropertyValue"}}function pop(){stack.pop();const t=stack[stack.length-1];t==null?parseState="end":Array.isArray(t)?parseState="afterArrayValue":parseState="afterPropertyValue"}function invalidChar(t){return syntaxError(t===void 0?`JSON5: invalid end of input at ${line}:${column}`:`JSON5: invalid character '${formatChar(t)}' at ${line}:${column}`)}function invalidEOF(){return syntaxError(`JSON5: invalid end of input at ${line}:${column}`)}function invalidIdentifier(){return column-=5,syntaxError(`JSON5: invalid identifier character at ${line}:${column}`)}function separatorChar(t){console.warn(`JSON5: '${formatChar(t)}' in strings is not valid ECMAScript; consider escaping`)}function formatChar(t){const e={"'":"\\'",'"':'\\"',"\\":"\\\\","\b":"\\b","\f":"\\f","\n":"\\n","\r":"\\r","	":"\\t","\v":"\\v","\0":"\\0","\u2028":"\\u2028","\u2029":"\\u2029"};if(e[t])return e[t];if(t<" "){const r=t.charCodeAt(0).toString(16);return"\\x"+("00"+r).substring(r.length)}return t}function syntaxError(t){const e=new SyntaxError(t);return e.lineNumber=line,e.columnNumber=column,e}var stringify=function(e,r,n){const u=[];let s="",o,l,D="",y;if(r!=null&&typeof r=="object"&&!Array.isArray(r)&&(n=r.space,y=r.quote,r=r.replacer),typeof r=="function")l=r;else if(Array.isArray(r)){o=[];for(const i of r){let d;typeof i=="string"?d=i:(typeof i=="number"||i instanceof String||i instanceof Number)&&(d=String(i)),d!==void 0&&o.indexOf(d)<0&&o.push(d)}}return n instanceof Number?n=Number(n):n instanceof String&&(n=String(n)),typeof n=="number"?n>0&&(n=Math.min(10,Math.floor(n)),D="          ".substr(0,n)):typeof n=="string"&&(D=n.substr(0,10)),C("",{"":e});function C(i,d){let a=d[i];switch(a!=null&&(typeof a.toJSON5=="function"?a=a.toJSON5(i):typeof a.toJSON=="function"&&(a=a.toJSON(i))),l&&(a=l.call(d,i,a)),a instanceof Number?a=Number(a):a instanceof String?a=String(a):a instanceof Boolean&&(a=a.valueOf()),a){case null:return"null";case!0:return"true";case!1:return"false"}if(typeof a=="string")return _(a);if(typeof a=="number")return String(a);if(typeof a=="object")return Array.isArray(a)?F(a):p(a)}function _(i){const d={"'":.1,'"':.2},a={"'":"\\'",'"':'\\"',"\\":"\\\\","\b":"\\b","\f":"\\f","\n":"\\n","\r":"\\r","	":"\\t","\v":"\\v","\0":"\\0","\u2028":"\\u2028","\u2029":"\\u2029"};let f="";for(let A=0;A<i.length;A++){const m=i[A];switch(m){case"'":case'"':d[m]++,f+=m;continue;case"\0":if(util.isDigit(i[A+1])){f+="\\x00";continue}}if(a[m]){f+=a[m];continue}if(m<" "){let g=m.charCodeAt(0).toString(16);f+="\\x"+("00"+g).substring(g.length);continue}f+=m}const E=y||Object.keys(d).reduce((A,m)=>d[A]<d[m]?A:m);return f=f.replace(new RegExp(E,"g"),a[E]),E+f+E}function p(i){if(u.indexOf(i)>=0)throw TypeError("Converting circular structure to JSON5");u.push(i);let d=s;s=s+D;let a=o||Object.keys(i),f=[];for(const A of a){const m=C(A,i);if(m!==void 0){let g=h(A)+":";D!==""&&(g+=" "),g+=m,f.push(g)}}let E;if(f.length===0)E="{}";else{let A;if(D==="")A=f.join(","),E="{"+A+"}";else{let m=`,
`+s;A=f.join(m),E=`{
`+s+A+`,
`+d+"}"}}return u.pop(),s=d,E}function h(i){if(i.length===0)return _(i);const d=String.fromCodePoint(i.codePointAt(0));if(!util.isIdStartChar(d))return _(i);for(let a=d.length;a<i.length;a++)if(!util.isIdContinueChar(String.fromCodePoint(i.codePointAt(a))))return _(i);return i}function F(i){if(u.indexOf(i)>=0)throw TypeError("Converting circular structure to JSON5");u.push(i);let d=s;s=s+D;let a=[];for(let E=0;E<i.length;E++){const A=C(String(E),i);a.push(A!==void 0?A:"null")}let f;if(a.length===0)f="[]";else if(D==="")f="["+a.join(",")+"]";else{let E=`,
`+s,A=a.join(E);f=`[
`+s+A+`,
`+d+"]"}return u.pop(),s=d,f}};const JSON5={parse,stringify};var lib=JSON5;const EVENT="ws://localhost:8000/_event",UPLOAD="http://localhost:8000/_upload",env={EVENT,UPLOAD},version="0.8.3",reflexEnvironment={version};var cookie={};/*!
 * cookie
 * Copyright(c) 2012-2014 Roman Shtylman
 * Copyright(c) 2015 Douglas Christopher Wilson
 * MIT Licensed
 */var hasRequiredCookie;function requireCookie(){if(hasRequiredCookie)return cookie;hasRequiredCookie=1,cookie.parse=o,cookie.serialize=y;var t=Object.prototype.toString,e=Object.prototype.hasOwnProperty,r=/^[!#$%&'*+\-.^_`|~0-9A-Za-z]+$/,n=/^("?)[\u0021\u0023-\u002B\u002D-\u003A\u003C-\u005B\u005D-\u007E]*\1$/,u=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,s=/^[\u0020-\u003A\u003D-\u007E]*$/;function o(h,F){if(typeof h!="string")throw new TypeError("argument str must be a string");var i={},d=h.length;if(d<2)return i;var a=F&&F.decode||C,f=0,E=0,A=0;do{if(E=h.indexOf("=",f),E===-1)break;if(A=h.indexOf(";",f),A===-1)A=d;else if(E>A){f=h.lastIndexOf(";",E-1)+1;continue}var m=l(h,f,E),g=D(h,E,m),x=h.slice(m,g);if(!e.call(i,x)){var B=l(h,E+1,A),v=D(h,A,B);h.charCodeAt(B)===34&&h.charCodeAt(v-1)===34&&(B++,v--);var w=h.slice(B,v);i[x]=p(w,a)}f=A+1}while(f<d);return i}function l(h,F,i){do{var d=h.charCodeAt(F);if(d!==32&&d!==9)return F}while(++F<i);return i}function D(h,F,i){for(;F>i;){var d=h.charCodeAt(--F);if(d!==32&&d!==9)return F+1}return i}function y(h,F,i){var d=i&&i.encode||encodeURIComponent;if(typeof d!="function")throw new TypeError("option encode is invalid");if(!r.test(h))throw new TypeError("argument name is invalid");var a=d(F);if(!n.test(a))throw new TypeError("argument val is invalid");var f=h+"="+a;if(!i)return f;if(i.maxAge!=null){var E=Math.floor(i.maxAge);if(!isFinite(E))throw new TypeError("option maxAge is invalid");f+="; Max-Age="+E}if(i.domain){if(!u.test(i.domain))throw new TypeError("option domain is invalid");f+="; Domain="+i.domain}if(i.path){if(!s.test(i.path))throw new TypeError("option path is invalid");f+="; Path="+i.path}if(i.expires){var A=i.expires;if(!_(A)||isNaN(A.valueOf()))throw new TypeError("option expires is invalid");f+="; Expires="+A.toUTCString()}if(i.httpOnly&&(f+="; HttpOnly"),i.secure&&(f+="; Secure"),i.partitioned&&(f+="; Partitioned"),i.priority){var m=typeof i.priority=="string"?i.priority.toLowerCase():i.priority;switch(m){case"low":f+="; Priority=Low";break;case"medium":f+="; Priority=Medium";break;case"high":f+="; Priority=High";break;default:throw new TypeError("option priority is invalid")}}if(i.sameSite){var g=typeof i.sameSite=="string"?i.sameSite.toLowerCase():i.sameSite;switch(g){case!0:f+="; SameSite=Strict";break;case"lax":f+="; SameSite=Lax";break;case"strict":f+="; SameSite=Strict";break;case"none":f+="; SameSite=None";break;default:throw new TypeError("option sameSite is invalid")}}return f}function C(h){return h.indexOf("%")!==-1?decodeURIComponent(h):h}function _(h){return t.call(h)==="[object Date]"}function p(h,F){try{return F(h)}catch{return h}}return cookie}var cookieExports=requireCookie();function hasDocumentCookie(){const t=typeof global>"u"?void 0:global.TEST_HAS_DOCUMENT_COOKIE;return typeof t=="boolean"?t:typeof document=="object"&&typeof document.cookie=="string"}function parseCookies(t){return typeof t=="string"?cookieExports.parse(t):typeof t=="object"&&t!==null?t:{}}function readCookie(t,e={}){const r=cleanupCookieValue(t);if(!e.doNotParse)try{return JSON.parse(r)}catch{}return t}function cleanupCookieValue(t){return t&&t[0]==="j"&&t[1]===":"?t.substr(2):t}class Cookies{constructor(e,r={}){this.changeListeners=[],this.HAS_DOCUMENT_COOKIE=!1,this.update=()=>{if(!this.HAS_DOCUMENT_COOKIE)return;const u=this.cookies;this.cookies=cookieExports.parse(document.cookie),this._checkChanges(u)};const n=typeof document>"u"?"":document.cookie;this.cookies=parseCookies(e||n),this.defaultSetOptions=r,this.HAS_DOCUMENT_COOKIE=hasDocumentCookie()}_emitChange(e){for(let r=0;r<this.changeListeners.length;++r)this.changeListeners[r](e)}_checkChanges(e){new Set(Object.keys(e).concat(Object.keys(this.cookies))).forEach(n=>{e[n]!==this.cookies[n]&&this._emitChange({name:n,value:readCookie(this.cookies[n])})})}_startPolling(){this.pollingInterval=setInterval(this.update,300)}_stopPolling(){this.pollingInterval&&clearInterval(this.pollingInterval)}get(e,r={}){return r.doNotUpdate||this.update(),readCookie(this.cookies[e],r)}getAll(e={}){e.doNotUpdate||this.update();const r={};for(let n in this.cookies)r[n]=readCookie(this.cookies[n],e);return r}set(e,r,n){n?n=Object.assign(Object.assign({},this.defaultSetOptions),n):n=this.defaultSetOptions;const u=typeof r=="string"?r:JSON.stringify(r);this.cookies=Object.assign(Object.assign({},this.cookies),{[e]:u}),this.HAS_DOCUMENT_COOKIE&&(document.cookie=cookieExports.serialize(e,u,n)),this._emitChange({name:e,value:r,options:n})}remove(e,r){const n=r=Object.assign(Object.assign(Object.assign({},this.defaultSetOptions),r),{expires:new Date(1970,1,1,0,0,1),maxAge:0});this.cookies=Object.assign({},this.cookies),delete this.cookies[e],this.HAS_DOCUMENT_COOKIE&&(document.cookie=cookieExports.serialize(e,"",n)),this._emitChange({name:e,value:void 0,options:r})}addChangeListener(e){this.changeListeners.push(e),this.HAS_DOCUMENT_COOKIE&&this.changeListeners.length===1&&(typeof window=="object"&&"cookieStore"in window?window.cookieStore.addEventListener("change",this.update):this._startPolling())}removeChangeListener(e){const r=this.changeListeners.indexOf(e);r>=0&&this.changeListeners.splice(r,1),this.HAS_DOCUMENT_COOKIE&&this.changeListeners.length===0&&(typeof window=="object"&&"cookieStore"in window?window.cookieStore.removeEventListener("change",this.update):this._stopPolling())}}const initialState={reflex___state____state:{is_hydrated_rx_state_:!1,router_rx_state_:{session:{client_token:"",client_ip:"",session_id:""},headers:{host:"",origin:"",upgrade:"",connection:"",cookie:"",pragma:"",cache_control:"",user_agent:"",sec_websocket_version:"",sec_websocket_key:"",sec_websocket_extensions:"",accept_encoding:"",accept_language:"",raw_headers:{}},page:{host:"",path:"",raw_path:"",full_path:"",full_raw_path:"",params:{}},url:"",route_id:""}},"reflex___state____state.reflex___state____frontend_event_exception_state":{},"reflex___state____state.reflex___state____on_load_internal_state":{},"reflex___state____state.reflex___state____update_vars_internal_state":{},"reflex___state____state.states___auth_state____auth_state":{current_user_rx_state_:null,current_user_id_rx_state_:0,error_message_rx_state_:"",is_admin_rx_state_:null,is_authenticated_rx_state_:!1,is_loading_rx_state_:!1,is_supervisor_rx_state_:null,login_password_rx_state_:"",login_username_rx_state_:"",session_token_rx_state_:null,success_message_rx_state_:""},"reflex___state____state.states___clinical_state____clinical_state":{assessment_form_rx_state_:{patient_id:0,assessment_date:"",chief_complaint:"",history_present_illness:"",primary_diagnosis:"",secondary_diagnoses:"",treatment_plan:""},available_disorders_rx_state_:[],criteria_met_percentage_rx_state_:0,criterion_responses_rx_state_:{},current_assessment_rx_state_:null,current_user_id_rx_state_:1,diagnosis_confidence_rx_state_:"Not evaluated",disorder_criteria_rx_state_:null,dsm5_evaluation_rx_state_:null,error_message_rx_state_:"",is_loading_rx_state_:!1,patient_assessments_rx_state_:[],required_criteria_status_rx_state_:"",selected_disorder_rx_state_:"",success_message_rx_state_:""},"reflex___state____state.states___patient_state____patient_state":{current_page_rx_state_:1,current_patient_rx_state_:null,current_user_id_rx_state_:0,error_message_rx_state_:"",is_loading_rx_state_:!1,page_size_rx_state_:25,potential_duplicates_rx_state_:[],search_results_rx_state_:[],search_term_rx_state_:"",show_advanced_filters_rx_state_:!1,success_message_rx_state_:"",total_pages_rx_state_:0,total_results_rx_state_:0}},defaultColorMode="light",ColorModeContext=reactExports.createContext(null),UploadFilesContext=reactExports.createContext(null),DispatchContext=reactExports.createContext(null),StateContexts={reflex___state____state:reactExports.createContext(null),reflex___state____state__reflex___state____frontend_event_exception_state:reactExports.createContext(null),reflex___state____state__reflex___state____on_load_internal_state:reactExports.createContext(null),reflex___state____state__reflex___state____update_vars_internal_state:reactExports.createContext(null),reflex___state____state__states___auth_state____auth_state:reactExports.createContext(null),reflex___state____state__states___clinical_state____clinical_state:reactExports.createContext(null),reflex___state____state__states___patient_state____patient_state:reactExports.createContext(null)},EventLoopContext=reactExports.createContext(null),clientStorage={cookies:{},local_storage:{},session_storage:{}},state_name="reflex___state____state",exception_state_name="reflex___state____state.reflex___state____frontend_event_exception_state",onLoadInternalEvent=()=>{const t=[],e=hydrateClientStorage(clientStorage);return e&&Object.keys(e).length!==0&&t.push(Event("reflex___state____state.reflex___state____update_vars_internal_state.update_vars_internal",{vars:e})),t.push(Event("reflex___state____state.reflex___state____on_load_internal_state.on_load_internal")),t},initialEvents=()=>[Event("reflex___state____state.hydrate"),...onLoadInternalEvent()],isDevMode=!1;function UploadFilesProvider({children:t}){const[e,r]=reactExports.useState({});return refs.__clear_selected_files=n=>r(u=>{const s={...u};return delete s[n],s}),reactExports.createElement(UploadFilesContext.Provider,{value:[e,r]},t)}function ClientSide(t){return({children:e,...r})=>{const[n,u]=reactExports.useState(null);return reactExports.useEffect(()=>{u(t)},[]),n?jsx(n,r,e):null}}function EventLoopProvider({children:t}){const e=reactExports.useContext(DispatchContext),[r,n]=useEventLoop(e,initialEvents,clientStorage);return reactExports.createElement(EventLoopContext.Provider,{value:[r,n]},t)}function StateProvider({children:t}){const[e,r]=reactExports.useReducer(applyDelta,initialState.reflex___state____state),[n,u]=reactExports.useReducer(applyDelta,initialState["reflex___state____state.reflex___state____frontend_event_exception_state"]),[s,o]=reactExports.useReducer(applyDelta,initialState["reflex___state____state.reflex___state____on_load_internal_state"]),[l,D]=reactExports.useReducer(applyDelta,initialState["reflex___state____state.reflex___state____update_vars_internal_state"]),[y,C]=reactExports.useReducer(applyDelta,initialState["reflex___state____state.states___auth_state____auth_state"]),[_,p]=reactExports.useReducer(applyDelta,initialState["reflex___state____state.states___clinical_state____clinical_state"]),[h,F]=reactExports.useReducer(applyDelta,initialState["reflex___state____state.states___patient_state____patient_state"]),i=reactExports.useMemo(()=>({reflex___state____state:r,"reflex___state____state.reflex___state____frontend_event_exception_state":u,"reflex___state____state.reflex___state____on_load_internal_state":o,"reflex___state____state.reflex___state____update_vars_internal_state":D,"reflex___state____state.states___auth_state____auth_state":C,"reflex___state____state.states___clinical_state____clinical_state":p,"reflex___state____state.states___patient_state____patient_state":F}),[]);return reactExports.createElement(StateContexts.reflex___state____state,{value:e},reactExports.createElement(StateContexts.reflex___state____state__reflex___state____frontend_event_exception_state,{value:n},reactExports.createElement(StateContexts.reflex___state____state__reflex___state____on_load_internal_state,{value:s},reactExports.createElement(StateContexts.reflex___state____state__reflex___state____update_vars_internal_state,{value:l},reactExports.createElement(StateContexts.reflex___state____state__states___auth_state____auth_state,{value:y},reactExports.createElement(StateContexts.reflex___state____state__states___clinical_state____clinical_state,{value:_},reactExports.createElement(StateContexts.reflex___state____state__states___patient_state____patient_state,{value:h},reactExports.createElement(DispatchContext,{value:i},t))))))))}const utils_context=Object.freeze(Object.defineProperty({__proto__:null,ClientSide,ColorModeContext,DispatchContext,EventLoopContext,EventLoopProvider,StateContexts,StateProvider,UploadFilesContext,UploadFilesProvider,clientStorage,defaultColorMode,exception_state_name,initialEvents,initialState,isDevMode,onLoadInternalEvent,state_name},Symbol.toStringTag,{value:"Module"})),debounce_timeout_id={};function debounce(t,e,r){const n=`${t}__${r}`;clearTimeout(debounce_timeout_id[n]),debounce_timeout_id[n]=setTimeout(()=>{e(),delete debounce_timeout_id[n]},r)}const in_throttle={};function throttle(t,e){const r=`${t}__${e}`;return in_throttle[r]?!1:(in_throttle[r]=!0,setTimeout(()=>{delete in_throttle[r]},e),!0)}const EVENTURL=env.EVENT,UPLOADURL=env.UPLOAD,SAME_DOMAIN_HOSTNAMES=["localhost","0.0.0.0","::","0:0:0:0:0:0:0:0"];let token;const TOKEN_KEY="token",cookies=new Cookies,refs={};let event_processing=!1;const event_queue=[],generateUUID=()=>{let t=new Date().getTime(),e=performance&&performance.now&&performance.now()*1e3||0;return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,r=>{let n=Math.random()*16;return t>0?(n=(t+n)%16|0,t=Math.floor(t/16)):(n=(e+n)%16|0,e=Math.floor(e/16)),(r=="x"?n:n&7|8).toString(16)})},getToken=()=>token||(typeof window<"u"&&(window.sessionStorage.getItem(TOKEN_KEY)||window.sessionStorage.setItem(TOKEN_KEY,generateUUID()),token=window.sessionStorage.getItem(TOKEN_KEY)),token),getBackendURL=t=>{const e=new URL(t);if(typeof window<"u"&&SAME_DOMAIN_HOSTNAMES.includes(e.hostname)){const r=window.location.hostname;e.hostname=r,window.location.protocol==="https:"&&(e.protocol==="ws:"?e.protocol="wss:":e.protocol==="http:"&&(e.protocol="https:"),e.port="")}return e},isBackendDisabled=()=>{const t=document.cookie.split("; ").find(e=>e.startsWith("backend-enabled="));return t!==void 0&&t.split("=")[1]=="false"},isStateful=()=>event_queue.length===0?!1:event_queue.some(t=>t.name.startsWith("reflex___state")),applyDelta=(t,e)=>({...t,...e}),evalReactComponent=async component=>{!window.React&&window.__reflex&&(window.React=window.__reflex.react);const module=await eval("import(dataUri)");return module.default},queueEventIfSocketExists=async(t,e,r,n)=>{e&&await queueEvents(t,e,r,n)};function urlFrom(t){try{return new URL(t)}catch{return}}const applyEvent=async(event,socket,navigate,params)=>{if(event.name=="_redirect"){if((event.payload.path??void 0)===void 0)return!1;if(event.payload.external)return window.open(event.payload.path,"_blank","noopener"),!1;const t=urlFrom(event.payload.path);let e=event.payload.path;if(t){if(t.host!==window.location.host)return window.location.assign(event.payload.path),!1;e=t.pathname+t.search+t.hash}return event.payload.replace?navigate(e,{replace:!0}):navigate(e),!1}if(event.name=="_remove_cookie")return cookies.remove(event.payload.key,{...event.payload.options}),queueEventIfSocketExists(initialEvents(),socket,navigate,params),!1;if(event.name=="_clear_local_storage")return localStorage.clear(),queueEventIfSocketExists(initialEvents(),socket,navigate,params),!1;if(event.name=="_remove_local_storage")return localStorage.removeItem(event.payload.key),queueEventIfSocketExists(initialEvents(),socket,navigate,params),!1;if(event.name=="_clear_session_storage")return sessionStorage.clear(),queueEvents(initialEvents(),socket,navigate,params),!1;if(event.name=="_remove_session_storage")return sessionStorage.removeItem(event.payload.key),queueEvents(initialEvents(),socket,navigate,params),!1;if(event.name=="_download"){const t=document.createElement("a");return t.hidden=!0,t.href=event.payload.url,t.href.includes("getBackendURL(env.UPLOAD)")&&(t.href=eval?.(event.payload.url.replace("getBackendURL(env.UPLOAD)",`"${getBackendURL(env.UPLOAD)}"`))),t.download=event.payload.filename,t.click(),t.remove(),!1}if(event.name=="_set_focus"){const e=(event.payload.ref in refs?refs[event.payload.ref]:event.payload.ref)?.current;return e===void 0||e?.focus===void 0?console.error(`No element found for ref ${event.payload.ref} in _set_focus`):e.focus(),!1}if(event.name=="_blur_focus"){const e=(event.payload.ref in refs?refs[event.payload.ref]:event.payload.ref)?.current;return e===void 0||e?.blur===void 0?console.error(`No element found for ref ${event.payload.ref} in _blur_focus`):e.blur(),!1}if(event.name=="_set_value"){const t=event.payload.ref in refs?refs[event.payload.ref]:event.payload.ref;return t.current&&(t.current.value=event.payload.value),!1}if(event.name=="_call_function"&&typeof event.payload.function!="string"){try{const eval_result=event.payload.function();if(event.payload.callback){const final_result=eval_result&&typeof eval_result.then=="function"?await eval_result:eval_result,callback=typeof event.payload.callback=="string"?eval(event.payload.callback):event.payload.callback;callback(final_result)}}catch(t){console.log("_call_function",t),window&&window?.onerror&&window.onerror(t.message,null,null,null,t)}return!1}if(event.name=="_call_script"||event.name=="_call_function"){try{const eval_result=event.name=="_call_script"?eval(event.payload.javascript_code):eval(event.payload.function)();if(event.payload.callback){const final_result=eval_result&&typeof eval_result.then=="function"?await eval_result:eval_result,callback=typeof event.payload.callback=="string"?eval(event.payload.callback):event.payload.callback;callback(final_result)}}catch(t){console.log("_call_script",t),window&&window?.onerror&&window.onerror(t.message,null,null,null,t)}return!1}return event.token=getToken(),(event.router_data===void 0||Object.keys(event.router_data).length===0)&&(event.router_data={pathname:window.location.pathname,query:{...Object.fromEntries(new URLSearchParams(window.location.search)),...params()},asPath:window.location.pathname+window.location.search+window.location.hash}),socket?(socket.emit("event",event),!0):!1},applyRestEvent=async(t,e,r,n)=>{let u=!1;return t.handler==="uploadFiles"?t.payload.files===void 0||t.payload.files.length===0?await applyEvent(Event(t.name,{files:[]}),e,r,n):(uploadFiles(t.name,t.payload.files,t.payload.upload_id,t.payload.on_upload_progress,e),!1):u},queueEvents=async(t,e,r,n,u)=>{r&&(t=[...t,...Array.from({length:event_queue.length}).map(()=>event_queue.shift())]),event_queue.push(...t.filter(s=>s!=null)),await processEvent(e.current,n,u)},processEvent=async(t,e,r)=>{if(!t&&isStateful()||event_queue.length===0||event_processing)return;event_processing=!0;const n=event_queue.shift();let u=!1;n.handler?u=await applyRestEvent(n,t,e,r):u=await applyEvent(n,t,e,r),u||(event_processing=!1,await processEvent(t,e,r))},connect=async(t,e,r,n,u={},s,o)=>{const l=getBackendURL(EVENTURL);t.current=lookup(l.href,{path:l.pathname,transports:r,protocols:[reflexEnvironment.version],autoUnref:!1}),t.current.io.encoder.replacer=(_,p)=>p===void 0?null:p,t.current.io.decoder.tryParse=_=>{try{return lib.parse(_)}catch{return!1}};function D(){document.visibilityState==="visible"&&(t.current.connected?console.log("Socket is reconnected "):(console.log("Socket is disconnected, attempting to reconnect "),t.current.connect()))}const y=_=>{t.current?.connected&&(console.log("Disconnect websocket on unload"),t.current.disconnect())},C=_=>{_.persisted&&t.current?.connected&&(console.log("Disconnect backend before bfcache on navigation"),t.current.disconnect())};t.current.on("connect",()=>{n([]),window.addEventListener("pagehide",C),window.addEventListener("beforeunload",y),window.addEventListener("unload",y)}),t.current.on("connect_error",_=>{n(p=>[p.slice(-9),_])}),t.current.on("disconnect",()=>{event_processing=!1,window.removeEventListener("unload",y),window.removeEventListener("beforeunload",y),window.removeEventListener("pagehide",C)}),t.current.on("event",async _=>{for(const p in _.delta)e[p](_.delta[p]);applyClientStorageDelta(u,_.delta),event_processing=!_.final,_.events&&queueEvents(_.events,t,!1,s,o)}),t.current.on("reload",async _=>{event_processing=!1,queueEvents([...initialEvents(),_],t,!0,s,o)}),document.addEventListener("visibilitychange",D)},uploadFiles=async(t,e,r,n,u)=>{if(e===void 0||e.length===0)return!1;const s=`__upload_controllers_${r}`;if(refs[s])return console.log("Upload already in progress for ",r),!1;let o=0;const l=C=>{const _=u._callbacks.$event;C.event.target.responseText.trim().split(`
`).slice(o).map(h=>{try{const F=lib.parse(h);_.map((i,d)=>{i(F).then(()=>{d===_.length-1&&(o+=1)}).catch(a=>{C.progress===1&&console.log("Error processing chunk",F,a)})})}catch(F){C.progress===1&&console.log("Error parsing chunk",h,F);return}})},D=new AbortController,y=new FormData;return e.forEach(C=>{y.append("files",C,C.path||C.name)}),refs[s]=D,new Promise((C,_)=>{const p=new XMLHttpRequest;p.onload=function(){p.status>=200&&p.status<300?C({data:p.responseText,status:p.status,statusText:p.statusText,headers:{get:h=>p.getResponseHeader(h)}}):_(new Error(`HTTP error! status: ${p.status}`))},p.onerror=function(){_(new Error("Network error"))},p.onabort=function(){_(new Error("Upload aborted"))},n&&(p.upload.onprogress=function(h){if(h.lengthComputable){const F={loaded:h.loaded,total:h.total,progress:h.loaded/h.total};n(F)}}),p.onprogress=function(h){if(l){const F={event:{target:{responseText:p.responseText}},progress:h.lengthComputable?h.loaded/h.total:0};l(F)}},D.signal.addEventListener("abort",()=>{p.abort()}),p.open("POST",getBackendURL(UPLOADURL)),p.setRequestHeader("Reflex-Client-Token",getToken()),p.setRequestHeader("Reflex-Event-Handler",t);try{p.send(y)}catch(h){_(h)}}).catch(C=>(console.log("Upload error:",C.message),!1)).finally(()=>{delete refs[s]})},Event=(t,e={},r={},n=null)=>({name:t,payload:e,handler:n,event_actions:r}),hydrateClientStorage=t=>{const e={};if(t.cookies)for(const r in t.cookies){const u=t.cookies[r].name||r;cookies.get(u)!==void 0&&(e[r]=cookies.get(u))}if(t.local_storage&&typeof window<"u")for(const r in t.local_storage){const n=t.local_storage[r],u=localStorage.getItem(n.name||r);u!==null&&(e[r]=u)}if(t.session_storage&&typeof window<"u")for(const r in t.session_storage){const n=t.session_storage[r],u=sessionStorage.getItem(n.name||r);u!=null&&(e[r]=u)}return t.cookies||t.local_storage||t.session_storage?e:{}},applyClientStorageDelta=(t,e)=>{const r=Object.keys(e).filter(n=>n.split(".").length===1);if(r.length===1){const n=e[r[0]];if(n.is_hydrated_rx_state_!==void 0&&!n.is_hydrated_rx_state_)return}for(const n in e)for(const u in e[n]){const s=`${n}.${u}`;if(t.cookies&&s in t.cookies){const o={...t.cookies[s]},l=o.name||s;delete o.name,cookies.set(l,e[n][u],o)}else if(t.local_storage&&s in t.local_storage&&typeof window<"u"){const o=t.local_storage[s];localStorage.setItem(o.name||s,e[n][u])}else if(t.session_storage&&s in t.session_storage&&typeof window<"u"){const o=t.session_storage[s];sessionStorage.setItem(o.name||s,e[n][u])}}},useEventLoop=(t,e=()=>[],r={})=>{const n=reactExports.useRef(null),u=useLocation(),s=useNavigate(),o=useParams(),l=reactExports.useRef(u),[D]=useSearchParams(),[y,C]=reactExports.useState([]),_=reactExports.useRef(o);reactExports.useEffect(()=>{const{"*":i,...d}=o;i?_.current={...d,splat:i.split("/")}:_.current=d},[o]);const p=reactExports.useCallback((i,d,a)=>{const f=i.filter(m=>m!=null);d instanceof Array||(d=[d]),a=f.reduce((m,g)=>({...m,...g.event_actions}),a??{});const E=d.filter(m=>m?.preventDefault!==void 0)[0];a?.preventDefault&&E?.preventDefault&&E.preventDefault(),a?.stopPropagation&&E?.stopPropagation&&E.stopPropagation();const A=f.map(m=>m.name).join("+++");a?.temporal&&(!n.current||!n.current.connected)||a?.throttle&&!throttle(A,a.throttle)||(a?.debounce?debounce(A,()=>queueEvents(f,n,!1,s,()=>_.current),a.debounce):queueEvents(f,n,!1,s,()=>_.current))},[]),h=reactExports.useRef(!1);reactExports.useEffect(()=>{h.current||(queueEvents(e().map(i=>({...i,router_data:{pathname:u.pathname,query:{...Object.fromEntries(D.entries()),..._.current},asPath:u.pathname+u.search}})),n,!0,s,()=>_.current),h.current=!0)},[]),reactExports.useEffect(()=>{typeof window>"u"||(window.onerror=function(i,d,a,f,E){return p([Event(`${exception_state_name}.handle_frontend_exception`,{info:E.name+": "+E.message+`
`+E.stack,component_stack:""})]),!1},window.onunhandledrejection=function(i){return p([Event(`${exception_state_name}.handle_frontend_exception`,{info:i.reason?.name+": "+i.reason?.message+`
`+i.reason?.stack,component_stack:""})]),!1})},[]),reactExports.useEffect(()=>(Object.keys(initialState).length>1&&!isBackendDisabled()&&(n.current||connect(n,t,["websocket"],C,r,s,()=>_.current)),()=>{n.current&&n.current.disconnect()}),[]),reactExports.useEffect(()=>{isBackendDisabled()||(async()=>{for(;event_queue.length>0&&!event_processing;)await processEvent(n.current,s,()=>_.current)})()}),reactExports.useEffect(()=>{const i={};if(r.local_storage&&typeof window<"u")for(const a in r.local_storage){const f=r.local_storage[a];if(f.sync){const E=f.name||a;i[E]=a}}const d=a=>{if(i[a.key]){const f={};f[i[a.key]]=a.newValue;const E=Event(`${state_name}.reflex___state____update_vars_internal_state.update_vars_internal`,{vars:f});p([E],a)}};return window.addEventListener("storage",d),()=>window.removeEventListener("storage",d)});const F=reactExports.useRef(!1);return reactExports.useEffect(()=>{if(!F.current){F.current=!0;return}if(u.state?.fromNotFound||u.pathname+u.search===l.current.pathname+l.current.search&&u.hash)return;const i=t.reflex___state____state;i!==void 0&&i({is_hydrated_rx_state_:!1}),p(onLoadInternalEvent()),l.current=u},[u,t,onLoadInternalEvent,p]),[p,y]},isTrue=t=>Array.isArray(t)?t.length>0:t===Object(t)?Object.keys(t).length>0:!!t,isNotNullOrUndefined=t=>(t??void 0)!==void 0,getRefValue=t=>{if(!(!t||!t.current))return t.current.type=="checkbox"?t.current.checked:t.current.className?.includes("rt-CheckboxRoot")||t.current.className?.includes("rt-SwitchRoot")?t.current.ariaChecked=="true":t.current.className?.includes("rt-SliderRoot")?t.current.querySelector(".rt-SliderThumb")?.ariaValueNow:t.current.value||t.current.querySelector&&t.current.querySelector(":checked")&&t.current.querySelector(":checked")?.value},getRefValues=t=>{if(t)return t.map(e=>e.current?e.current.value||e.current.getAttribute("aria-valuenow"):null)},spreadArraysOrObjects=(t,e)=>{if(Array.isArray(t)&&Array.isArray(e))return[...t,...e];if(typeof t=="object"&&typeof e=="object")return{...t,...e};throw new Error("Both parameters must be either arrays or objects.")},utils_state=Object.freeze(Object.defineProperty({__proto__:null,Event,applyDelta,applyEvent,applyRestEvent,connect,evalReactComponent,generateUUID,getBackendURL,getRefValue,getRefValues,getToken,hydrateClientStorage,isBackendDisabled,isNotNullOrUndefined,isStateful,isTrue,processEvent,queueEventIfSocketExists,queueEvents,refs,spreadArraysOrObjects,uploadFiles,useEventLoop},Symbol.toStringTag,{value:"Module"}));export{ColorModeContext as C,EventLoopContext as E,StateProvider as S,utils_context as a,EventLoopProvider as b,Event as c,defaultColorMode as d,env as e,StateContexts as f,getBackendURL as g,isNotNullOrUndefined as h,isTrue as i,refs as r,utils_state as u};
