import{a as t,r as Sl,q as v,S as Ml,s as _l,M as Tl,L as Pl,w as Il,t as kl,O as $l,v as Dl}from"./chunk-QMGIS6GS-suYYFPSk.js";import{d as nt,C as io,E as cr,g as Al,r as dr,e as Ol,u as jl,a as Ll,S as Bl,b as Hl,c as Xo}from"./state-B1hYtTsq.js";import{j as x,k as Vl,e as Fl}from"./emotion-react.browser.esm-BNSIgtcs.js";import{R as ur,P,d as Qo,e as wt,t as re,r as Et,a as Rt,s as zl,p as Gl,n as Kl,u as Wl,b as Ul,c as xe,o as mr,f as Zl,g as Yl,h as ql,i as Xl}from"./button-Ccwu8jNm.js";import{j as u}from"./jsx-runtime-D_zvdyIk.js";import{u as B,c as fr,a as Ql,b as pr,o as te,v as I,y,p as K,d as he,s as lo,r as V,S as Ce,e as Te,f as Nt,g as St,t as Jl,h as co,i as ec,j as tc}from"./text-DCkbNTq3.js";import{c as X,a as oc,b as S,P as ce,d as uo,u as ae,e as je,R as gr,h as vr,f as hr,F as Cr,D as Mt,g as we,i as We,j as br,C as rc,I as ac,k as nc,l as sc,G as ic,m as lc,L as cc,n as dc,o as uc,p as mc,S as fc,q as pc,r as gc,s as vc,A as hc,t as Cc,v as _t,w as yr,x as mo,y as xr,z as wr,B as st,E as fo,H as Pe,J as Tt,K as Pt,M as po,N as Er,O as Ee,Q as me,T as bc,U as vt,V as it,W as Ne,X as yc,Y as go,Z as tt,_ as lt,$ as xc,a0 as wc,a1 as Rr,a2 as Ec,a3 as Ge,a4 as ve,a5 as Rc,a6 as Nc,a7 as Sc,a8 as Mc,a9 as _c,aa as Tc,ab as Pc,ac as Ic,ad as kc,ae as $c,af as Dc,ag as Ac,ah as Oc}from"./createLucideIcon-BOfs0RvG.js";import{d as Nr,p as jc}from"./container-Do-LxYxS.js";import{o as Lc}from"./card-BFhh40Od.js";import{c as Sr,C as Bc,a as Hc,t as Mr,b as Vc,s as Fc,r as zc}from"./text-area-DvQa0X7q.js";import{P as Gc,t as Kc}from"./text-field-UsP3CXyu.js";import{u as It,s as Wc}from"./select-hLIrycZp.js";import{o as Uc}from"./separator-CPIfs_Op.js";import{R as _r}from"./index-XOwJfM4g.js";const Zc="/assets/__reflex_global_styles-tb2FFvNE.css",Yc=t.createContext(null),Kt={didCatch:!1,error:null};class qc extends t.Component{constructor(o){super(o),this.resetErrorBoundary=this.resetErrorBoundary.bind(this),this.state=Kt}static getDerivedStateFromError(o){return{didCatch:!0,error:o}}resetErrorBoundary(){const{error:o}=this.state;if(o!==null){for(var r,a,n=arguments.length,s=new Array(n),i=0;i<n;i++)s[i]=arguments[i];(r=(a=this.props).onReset)===null||r===void 0||r.call(a,{args:s,reason:"imperative-api"}),this.setState(Kt)}}componentDidCatch(o,r){var a,n;(a=(n=this.props).onError)===null||a===void 0||a.call(n,o,r)}componentDidUpdate(o,r){const{didCatch:a}=this.state,{resetKeys:n}=this.props;if(a&&r.error!==null&&Xc(o.resetKeys,n)){var s,i;(s=(i=this.props).onReset)===null||s===void 0||s.call(i,{next:n,prev:o.resetKeys,reason:"keys"}),this.setState(Kt)}}render(){const{children:o,fallbackRender:r,FallbackComponent:a,fallback:n}=this.props,{didCatch:s,error:i}=this.state;let c=o;if(s){const d={error:i,resetErrorBoundary:this.resetErrorBoundary};if(typeof r=="function")c=r(d);else if(a)c=t.createElement(a,d);else if(n!==void 0)c=n;else throw i}return t.createElement(Yc.Provider,{value:{didCatch:s,error:i,resetErrorBoundary:this.resetErrorBoundary}},c)}}function Xc(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],o=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[];return e.length!==o.length||e.some((r,a)=>!Object.is(r,o[a]))}const Tr=t.createContext({theme:nt,resolvedTheme:nt,setTheme:()=>{}});function Qc({children:e,defaultTheme:o="system"}){const[r,a]=t.useState(o),[n,s]=t.useState(o!=="system"?o:"light"),i=t.useRef(!0);t.useEffect(()=>{if(!i.current)return;i.current=!1;const d=localStorage.getItem("theme")||o;a(d)});const c=t.useMemo(()=>r==="system"?n:r,[r,n]);return t.useEffect(()=>{const d=window.matchMedia("(prefers-color-scheme: dark)"),m=()=>{s(d.matches?"dark":"light")};return m(),d.addEventListener("change",m),()=>{d.removeEventListener("change",m)}}),t.useEffect(()=>{localStorage.setItem("theme",r)},[r]),t.useEffect(()=>{const d=window.document.documentElement;d.classList.remove("light","dark"),d.classList.add(c),d.style.colorScheme=c},[c]),t.createElement(Tr.Provider,{value:{theme:r,resolvedTheme:c,setTheme:a}},e)}function Jc(){return t.useContext(Tr)}function ed({children:e}){const{theme:o,resolvedTheme:r,setTheme:a}=Jc(),n=()=>{a(r==="light"?"dark":"light")},s=i=>{["light","dark","system"].includes(i)||(console.error(`Invalid color mode "${i}". Defaulting to "${nt}".`),i=nt),a(i)};return t.createElement(io.Provider,{value:{rawColorMode:o,resolvedColorMode:r,toggleColorMode:n,setColorMode:s}},e)}var td="AccessibleIcon",Pr=({children:e,label:o})=>{const r=t.Children.only(e);return u.jsxs(u.Fragment,{children:[t.cloneElement(r,{"aria-hidden":"true",focusable:"false"}),u.jsx(ur,{children:o})]})};Pr.displayName=td;var od=Pr,kt="Dialog",[Ir,kr]=X(kt),[rd,be]=Ir(kt),$r=e=>{const{__scopeDialog:o,children:r,open:a,defaultOpen:n,onOpenChange:s,modal:i=!0}=e,c=t.useRef(null),d=t.useRef(null),[m,f]=ae({prop:a,defaultProp:n??!1,onChange:s,caller:kt});return u.jsx(rd,{scope:o,triggerRef:c,contentRef:d,contentId:je(),titleId:je(),descriptionId:je(),open:m,onOpenChange:f,onOpenToggle:t.useCallback(()=>f(l=>!l),[f]),modal:i,children:r})};$r.displayName=kt;var Dr="DialogTrigger",Ar=t.forwardRef((e,o)=>{const{__scopeDialog:r,...a}=e,n=be(Dr,r),s=B(o,n.triggerRef);return u.jsx(P.button,{type:"button","aria-haspopup":"dialog","aria-expanded":n.open,"aria-controls":n.contentId,"data-state":Co(n.open),...a,ref:s,onClick:S(e.onClick,n.onOpenToggle)})});Ar.displayName=Dr;var vo="DialogPortal",[ad,Or]=Ir(vo,{forceMount:void 0}),jr=e=>{const{__scopeDialog:o,forceMount:r,children:a,container:n}=e,s=be(vo,o);return u.jsx(ad,{scope:o,forceMount:r,children:t.Children.map(a,i=>u.jsx(ce,{present:r||s.open,children:u.jsx(uo,{asChild:!0,container:n,children:i})}))})};jr.displayName=vo;var ht="DialogOverlay",Lr=t.forwardRef((e,o)=>{const r=Or(ht,e.__scopeDialog),{forceMount:a=r.forceMount,...n}=e,s=be(ht,e.__scopeDialog);return s.modal?u.jsx(ce,{present:a||s.open,children:u.jsx(sd,{...n,ref:o})}):null});Lr.displayName=ht;var nd=fr("DialogOverlay.RemoveScroll"),sd=t.forwardRef((e,o)=>{const{__scopeDialog:r,...a}=e,n=be(ht,r);return u.jsx(gr,{as:nd,allowPinchZoom:!0,shards:[n.contentRef],children:u.jsx(P.div,{"data-state":Co(n.open),...a,ref:o,style:{pointerEvents:"auto",...a.style}})})}),Le="DialogContent",Br=t.forwardRef((e,o)=>{const r=Or(Le,e.__scopeDialog),{forceMount:a=r.forceMount,...n}=e,s=be(Le,e.__scopeDialog);return u.jsx(ce,{present:a||s.open,children:s.modal?u.jsx(id,{...n,ref:o}):u.jsx(ld,{...n,ref:o})})});Br.displayName=Le;var id=t.forwardRef((e,o)=>{const r=be(Le,e.__scopeDialog),a=t.useRef(null),n=B(o,r.contentRef,a);return t.useEffect(()=>{const s=a.current;if(s)return vr(s)},[]),u.jsx(Hr,{...e,ref:n,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:S(e.onCloseAutoFocus,s=>{s.preventDefault(),r.triggerRef.current?.focus()}),onPointerDownOutside:S(e.onPointerDownOutside,s=>{const i=s.detail.originalEvent,c=i.button===0&&i.ctrlKey===!0;(i.button===2||c)&&s.preventDefault()}),onFocusOutside:S(e.onFocusOutside,s=>s.preventDefault())})}),ld=t.forwardRef((e,o)=>{const r=be(Le,e.__scopeDialog),a=t.useRef(!1),n=t.useRef(!1);return u.jsx(Hr,{...e,ref:o,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:s=>{e.onCloseAutoFocus?.(s),s.defaultPrevented||(a.current||r.triggerRef.current?.focus(),s.preventDefault()),a.current=!1,n.current=!1},onInteractOutside:s=>{e.onInteractOutside?.(s),s.defaultPrevented||(a.current=!0,s.detail.originalEvent.type==="pointerdown"&&(n.current=!0));const i=s.target;r.triggerRef.current?.contains(i)&&s.preventDefault(),s.detail.originalEvent.type==="focusin"&&n.current&&s.preventDefault()}})}),Hr=t.forwardRef((e,o)=>{const{__scopeDialog:r,trapFocus:a,onOpenAutoFocus:n,onCloseAutoFocus:s,...i}=e,c=be(Le,r),d=t.useRef(null),m=B(o,d);return hr(),u.jsxs(u.Fragment,{children:[u.jsx(Cr,{asChild:!0,loop:!0,trapped:a,onMountAutoFocus:n,onUnmountAutoFocus:s,children:u.jsx(Mt,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":Co(c.open),...i,ref:m,onDismiss:()=>c.onOpenChange(!1)})}),u.jsxs(u.Fragment,{children:[u.jsx(dd,{titleId:c.titleId}),u.jsx(md,{contentRef:d,descriptionId:c.descriptionId})]})]})}),ho="DialogTitle",Vr=t.forwardRef((e,o)=>{const{__scopeDialog:r,...a}=e,n=be(ho,r);return u.jsx(P.h2,{id:n.titleId,...a,ref:o})});Vr.displayName=ho;var Fr="DialogDescription",zr=t.forwardRef((e,o)=>{const{__scopeDialog:r,...a}=e,n=be(Fr,r);return u.jsx(P.p,{id:n.descriptionId,...a,ref:o})});zr.displayName=Fr;var Gr="DialogClose",Kr=t.forwardRef((e,o)=>{const{__scopeDialog:r,...a}=e,n=be(Gr,r);return u.jsx(P.button,{type:"button",...a,ref:o,onClick:S(e.onClick,()=>n.onOpenChange(!1))})});Kr.displayName=Gr;function Co(e){return e?"open":"closed"}var Wr="DialogTitleWarning",[cd,Ur]=oc(Wr,{contentName:Le,titleName:ho,docsSlug:"dialog"}),dd=({titleId:e})=>{const o=Ur(Wr),r=`\`${o.contentName}\` requires a \`${o.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${o.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${o.docsSlug}`;return t.useEffect(()=>{e&&(document.getElementById(e)||console.error(r))},[r,e]),null},ud="DialogDescriptionWarning",md=({contentRef:e,descriptionId:o})=>{const a=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${Ur(ud).contentName}}.`;return t.useEffect(()=>{const n=e.current?.getAttribute("aria-describedby");o&&n&&(document.getElementById(o)||console.warn(a))},[a,e,o]),null},Zr=$r,Yr=Ar,qr=jr,Xr=Lr,Qr=Br,Jr=Vr,ea=zr,bo=Kr,ta="AlertDialog",[fd,Fg]=X(ta,[kr]),Se=kr(),oa=e=>{const{__scopeAlertDialog:o,...r}=e,a=Se(o);return u.jsx(Zr,{...a,...r,modal:!0})};oa.displayName=ta;var pd="AlertDialogTrigger",ra=t.forwardRef((e,o)=>{const{__scopeAlertDialog:r,...a}=e,n=Se(r);return u.jsx(Yr,{...n,...a,ref:o})});ra.displayName=pd;var gd="AlertDialogPortal",aa=e=>{const{__scopeAlertDialog:o,...r}=e,a=Se(o);return u.jsx(qr,{...a,...r})};aa.displayName=gd;var vd="AlertDialogOverlay",na=t.forwardRef((e,o)=>{const{__scopeAlertDialog:r,...a}=e,n=Se(r);return u.jsx(Xr,{...n,...a,ref:o})});na.displayName=vd;var Ke="AlertDialogContent",[hd,Cd]=fd(Ke),bd=Ql("AlertDialogContent"),sa=t.forwardRef((e,o)=>{const{__scopeAlertDialog:r,children:a,...n}=e,s=Se(r),i=t.useRef(null),c=B(o,i),d=t.useRef(null);return u.jsx(cd,{contentName:Ke,titleName:ia,docsSlug:"alert-dialog",children:u.jsx(hd,{scope:r,cancelRef:d,children:u.jsxs(Qr,{role:"alertdialog",...s,...n,ref:c,onOpenAutoFocus:S(n.onOpenAutoFocus,m=>{m.preventDefault(),d.current?.focus({preventScroll:!0})}),onPointerDownOutside:m=>m.preventDefault(),onInteractOutside:m=>m.preventDefault(),children:[u.jsx(bd,{children:a}),u.jsx(xd,{contentRef:i})]})})})});sa.displayName=Ke;var ia="AlertDialogTitle",la=t.forwardRef((e,o)=>{const{__scopeAlertDialog:r,...a}=e,n=Se(r);return u.jsx(Jr,{...n,...a,ref:o})});la.displayName=ia;var ca="AlertDialogDescription",da=t.forwardRef((e,o)=>{const{__scopeAlertDialog:r,...a}=e,n=Se(r);return u.jsx(ea,{...n,...a,ref:o})});da.displayName=ca;var yd="AlertDialogAction",ua=t.forwardRef((e,o)=>{const{__scopeAlertDialog:r,...a}=e,n=Se(r);return u.jsx(bo,{...n,...a,ref:o})});ua.displayName=yd;var ma="AlertDialogCancel",fa=t.forwardRef((e,o)=>{const{__scopeAlertDialog:r,...a}=e,{cancelRef:n}=Cd(ma,r),s=Se(r),i=B(o,n);return u.jsx(bo,{...s,...a,ref:i})});fa.displayName=ma;var xd=({contentRef:e})=>{const o=`\`${Ke}\` requires a description for the component to be accessible for screen reader users.

You can add a description to the \`${Ke}\` by passing a \`${ca}\` component as a child, which also benefits sighted users by adding visible context to the dialog.

Alternatively, you can use your own component as a description by assigning it an \`id\` and passing the same value to the \`aria-describedby\` prop in \`${Ke}\`. If the description is confusing or duplicative for sighted users, you can use the \`@radix-ui/react-visually-hidden\` primitive as a wrapper around your description component.

For more information, see https://radix-ui.com/primitives/docs/components/alert-dialog`;return t.useEffect(()=>{document.getElementById(e.current?.getAttribute("aria-describedby"))||console.warn(o)},[o,e]),null},wd=oa,Ed=ra,Rd=aa,Nd=na,Sd=sa,Md=ua,_d=fa,Td=la,Pd=da,Id="AspectRatio",pa=t.forwardRef((e,o)=>{const{ratio:r=1/1,style:a,...n}=e;return u.jsx("div",{style:{position:"relative",width:"100%",paddingBottom:`${100/r}%`},"data-radix-aspect-ratio-wrapper":"",children:u.jsx(P.div,{...n,ref:o,style:{...a,position:"absolute",top:0,right:0,bottom:0,left:0}})})});pa.displayName=Id;var kd=pa,Wt={exports:{}},Ut={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Jo;function $d(){if(Jo)return Ut;Jo=1;var e=Sl();function o(l,p){return l===p&&(l!==0||1/l===1/p)||l!==l&&p!==p}var r=typeof Object.is=="function"?Object.is:o,a=e.useState,n=e.useEffect,s=e.useLayoutEffect,i=e.useDebugValue;function c(l,p){var h=p(),g=a({inst:{value:h,getSnapshot:p}}),b=g[0].inst,C=g[1];return s(function(){b.value=h,b.getSnapshot=p,d(b)&&C({inst:b})},[l,h,p]),n(function(){return d(b)&&C({inst:b}),l(function(){d(b)&&C({inst:b})})},[l]),i(h),h}function d(l){var p=l.getSnapshot;l=l.value;try{var h=p();return!r(l,h)}catch{return!0}}function m(l,p){return p()}var f=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?m:c;return Ut.useSyncExternalStore=e.useSyncExternalStore!==void 0?e.useSyncExternalStore:f,Ut}var er;function Dd(){return er||(er=1,Wt.exports=$d()),Wt.exports}var Ad=Dd();function Od(){return Ad.useSyncExternalStore(jd,()=>!0,()=>!1)}function jd(){return()=>{}}var yo="Avatar",[Ld,zg]=X(yo),[Bd,ga]=Ld(yo),va=t.forwardRef((e,o)=>{const{__scopeAvatar:r,...a}=e,[n,s]=t.useState("idle");return u.jsx(Bd,{scope:r,imageLoadingStatus:n,onImageLoadingStatusChange:s,children:u.jsx(P.span,{...a,ref:o})})});va.displayName=yo;var ha="AvatarImage",Ca=t.forwardRef((e,o)=>{const{__scopeAvatar:r,src:a,onLoadingStatusChange:n=()=>{},...s}=e,i=ga(ha,r),c=Hd(a,s),d=we(m=>{n(m),i.onImageLoadingStatusChange(m)});return We(()=>{c!=="idle"&&d(c)},[c,d]),c==="loaded"?u.jsx(P.img,{...s,ref:o,src:a}):null});Ca.displayName=ha;var ba="AvatarFallback",ya=t.forwardRef((e,o)=>{const{__scopeAvatar:r,delayMs:a,...n}=e,s=ga(ba,r),[i,c]=t.useState(a===void 0);return t.useEffect(()=>{if(a!==void 0){const d=window.setTimeout(()=>c(!0),a);return()=>window.clearTimeout(d)}},[a]),i&&s.imageLoadingStatus!=="loaded"?u.jsx(P.span,{...n,ref:o}):null});ya.displayName=ba;function tr(e,o){return e?o?(e.src!==o&&(e.src=o),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}function Hd(e,{referrerPolicy:o,crossOrigin:r}){const a=Od(),n=t.useRef(null),s=a?(n.current||(n.current=new window.Image),n.current):null,[i,c]=t.useState(()=>tr(s,e));return We(()=>{c(tr(s,e))},[s,e]),We(()=>{const d=l=>()=>{c(l)};if(!s)return;const m=d("loaded"),f=d("error");return s.addEventListener("load",m),s.addEventListener("error",f),o&&(s.referrerPolicy=o),typeof r=="string"&&(s.crossOrigin=r),()=>{s.removeEventListener("load",m),s.removeEventListener("error",f)}},[s,r,o]),i}var Vd=va,Fd=Ca,zd=ya,xo="ContextMenu",[Gd,Gg]=X(xo,[br]),oe=br(),[Kd,xa]=Gd(xo),wa=e=>{const{__scopeContextMenu:o,children:r,onOpenChange:a,dir:n,modal:s=!0}=e,[i,c]=t.useState(!1),d=oe(o),m=we(a),f=t.useCallback(l=>{c(l),m(l)},[m]);return u.jsx(Kd,{scope:o,open:i,onOpenChange:f,modal:s,children:u.jsx(mc,{...d,dir:n,open:i,onOpenChange:f,modal:s,children:r})})};wa.displayName=xo;var Ea="ContextMenuTrigger",Ra=t.forwardRef((e,o)=>{const{__scopeContextMenu:r,disabled:a=!1,...n}=e,s=xa(Ea,r),i=oe(r),c=t.useRef({x:0,y:0}),d=t.useRef({getBoundingClientRect:()=>DOMRect.fromRect({width:0,height:0,...c.current})}),m=t.useRef(0),f=t.useCallback(()=>window.clearTimeout(m.current),[]),l=p=>{c.current={x:p.clientX,y:p.clientY},s.onOpenChange(!0)};return t.useEffect(()=>f,[f]),t.useEffect(()=>void(a&&f()),[a,f]),u.jsxs(u.Fragment,{children:[u.jsx(hc,{...i,virtualRef:d}),u.jsx(P.span,{"data-state":s.open?"open":"closed","data-disabled":a?"":void 0,...n,ref:o,style:{WebkitTouchCallout:"none",...e.style},onContextMenu:a?e.onContextMenu:S(e.onContextMenu,p=>{f(),l(p),p.preventDefault()}),onPointerDown:a?e.onPointerDown:S(e.onPointerDown,ft(p=>{f(),m.current=window.setTimeout(()=>l(p),700)})),onPointerMove:a?e.onPointerMove:S(e.onPointerMove,ft(f)),onPointerCancel:a?e.onPointerCancel:S(e.onPointerCancel,ft(f)),onPointerUp:a?e.onPointerUp:S(e.onPointerUp,ft(f))})]})});Ra.displayName=Ea;var Wd="ContextMenuPortal",Na=e=>{const{__scopeContextMenu:o,...r}=e,a=oe(o);return u.jsx(nc,{...a,...r})};Na.displayName=Wd;var Sa="ContextMenuContent",Ma=t.forwardRef((e,o)=>{const{__scopeContextMenu:r,...a}=e,n=xa(Sa,r),s=oe(r),i=t.useRef(!1);return u.jsx(sc,{...s,...a,ref:o,side:"right",sideOffset:2,align:"start",onCloseAutoFocus:c=>{e.onCloseAutoFocus?.(c),!c.defaultPrevented&&i.current&&c.preventDefault(),i.current=!1},onInteractOutside:c=>{e.onInteractOutside?.(c),!c.defaultPrevented&&!n.modal&&(i.current=!0)},style:{...e.style,"--radix-context-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-context-menu-content-available-width":"var(--radix-popper-available-width)","--radix-context-menu-content-available-height":"var(--radix-popper-available-height)","--radix-context-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-context-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});Ma.displayName=Sa;var Ud="ContextMenuGroup",_a=t.forwardRef((e,o)=>{const{__scopeContextMenu:r,...a}=e,n=oe(r);return u.jsx(ic,{...n,...a,ref:o})});_a.displayName=Ud;var Zd="ContextMenuLabel",Ta=t.forwardRef((e,o)=>{const{__scopeContextMenu:r,...a}=e,n=oe(r);return u.jsx(cc,{...n,...a,ref:o})});Ta.displayName=Zd;var Yd="ContextMenuItem",Pa=t.forwardRef((e,o)=>{const{__scopeContextMenu:r,...a}=e,n=oe(r);return u.jsx(lc,{...n,...a,ref:o})});Pa.displayName=Yd;var qd="ContextMenuCheckboxItem",Ia=t.forwardRef((e,o)=>{const{__scopeContextMenu:r,...a}=e,n=oe(r);return u.jsx(rc,{...n,...a,ref:o})});Ia.displayName=qd;var Xd="ContextMenuRadioGroup",ka=t.forwardRef((e,o)=>{const{__scopeContextMenu:r,...a}=e,n=oe(r);return u.jsx(dc,{...n,...a,ref:o})});ka.displayName=Xd;var Qd="ContextMenuRadioItem",$a=t.forwardRef((e,o)=>{const{__scopeContextMenu:r,...a}=e,n=oe(r);return u.jsx(uc,{...n,...a,ref:o})});$a.displayName=Qd;var Jd="ContextMenuItemIndicator",Da=t.forwardRef((e,o)=>{const{__scopeContextMenu:r,...a}=e,n=oe(r);return u.jsx(ac,{...n,...a,ref:o})});Da.displayName=Jd;var eu="ContextMenuSeparator",Aa=t.forwardRef((e,o)=>{const{__scopeContextMenu:r,...a}=e,n=oe(r);return u.jsx(fc,{...n,...a,ref:o})});Aa.displayName=eu;var tu="ContextMenuArrow",ou=t.forwardRef((e,o)=>{const{__scopeContextMenu:r,...a}=e,n=oe(r);return u.jsx(Cc,{...n,...a,ref:o})});ou.displayName=tu;var Oa="ContextMenuSub",ja=e=>{const{__scopeContextMenu:o,children:r,onOpenChange:a,open:n,defaultOpen:s}=e,i=oe(o),[c,d]=ae({prop:n,defaultProp:s??!1,onChange:a,caller:Oa});return u.jsx(pc,{...i,open:c,onOpenChange:d,children:r})};ja.displayName=Oa;var ru="ContextMenuSubTrigger",La=t.forwardRef((e,o)=>{const{__scopeContextMenu:r,...a}=e,n=oe(r);return u.jsx(vc,{...n,...a,ref:o})});La.displayName=ru;var au="ContextMenuSubContent",Ba=t.forwardRef((e,o)=>{const{__scopeContextMenu:r,...a}=e,n=oe(r);return u.jsx(gc,{...n,...a,ref:o,style:{...e.style,"--radix-context-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-context-menu-content-available-width":"var(--radix-popper-available-width)","--radix-context-menu-content-available-height":"var(--radix-popper-available-height)","--radix-context-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-context-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});Ba.displayName=au;function ft(e){return o=>o.pointerType!=="mouse"?e(o):void 0}var nu=wa,su=Ra,Ha=Na,iu=Ma,lu=_a,cu=Ta,du=Pa,uu=Ia,mu=ka,fu=$a,Va=Da,pu=Aa,gu=ja,vu=La,hu=Ba,Zt,$t="HoverCard",[Fa,Kg]=X($t,[_t]),Dt=_t(),[Cu,At]=Fa($t),za=e=>{const{__scopeHoverCard:o,children:r,open:a,defaultOpen:n,onOpenChange:s,openDelay:i=700,closeDelay:c=300}=e,d=Dt(o),m=t.useRef(0),f=t.useRef(0),l=t.useRef(!1),p=t.useRef(!1),[h,g]=ae({prop:a,defaultProp:n??!1,onChange:s,caller:$t}),b=t.useCallback(()=>{clearTimeout(f.current),m.current=window.setTimeout(()=>g(!0),i)},[i,g]),C=t.useCallback(()=>{clearTimeout(m.current),!l.current&&!p.current&&(f.current=window.setTimeout(()=>g(!1),c))},[c,g]),R=t.useCallback(()=>g(!1),[g]);return t.useEffect(()=>()=>{clearTimeout(m.current),clearTimeout(f.current)},[]),u.jsx(Cu,{scope:o,open:h,onOpenChange:g,onOpen:b,onClose:C,onDismiss:R,hasSelectionRef:l,isPointerDownOnContentRef:p,children:u.jsx(yr,{...d,children:r})})};za.displayName=$t;var Ga="HoverCardTrigger",Ka=t.forwardRef((e,o)=>{const{__scopeHoverCard:r,...a}=e,n=At(Ga,r),s=Dt(r);return u.jsx(mo,{asChild:!0,...s,children:u.jsx(P.a,{"data-state":n.open?"open":"closed",...a,ref:o,onPointerEnter:S(e.onPointerEnter,bt(n.onOpen)),onPointerLeave:S(e.onPointerLeave,bt(n.onClose)),onFocus:S(e.onFocus,n.onOpen),onBlur:S(e.onBlur,n.onClose),onTouchStart:S(e.onTouchStart,i=>i.preventDefault())})})});Ka.displayName=Ga;var wo="HoverCardPortal",[bu,yu]=Fa(wo,{forceMount:void 0}),Wa=e=>{const{__scopeHoverCard:o,forceMount:r,children:a,container:n}=e,s=At(wo,o);return u.jsx(bu,{scope:o,forceMount:r,children:u.jsx(ce,{present:r||s.open,children:u.jsx(uo,{asChild:!0,container:n,children:a})})})};Wa.displayName=wo;var Ct="HoverCardContent",Ua=t.forwardRef((e,o)=>{const r=yu(Ct,e.__scopeHoverCard),{forceMount:a=r.forceMount,...n}=e,s=At(Ct,e.__scopeHoverCard);return u.jsx(ce,{present:a||s.open,children:u.jsx(xu,{"data-state":s.open?"open":"closed",...n,onPointerEnter:S(e.onPointerEnter,bt(s.onOpen)),onPointerLeave:S(e.onPointerLeave,bt(s.onClose)),ref:o})})});Ua.displayName=Ct;var xu=t.forwardRef((e,o)=>{const{__scopeHoverCard:r,onEscapeKeyDown:a,onPointerDownOutside:n,onFocusOutside:s,onInteractOutside:i,...c}=e,d=At(Ct,r),m=Dt(r),f=t.useRef(null),l=B(o,f),[p,h]=t.useState(!1);return t.useEffect(()=>{if(p){const g=document.body;return Zt=g.style.userSelect||g.style.webkitUserSelect,g.style.userSelect="none",g.style.webkitUserSelect="none",()=>{g.style.userSelect=Zt,g.style.webkitUserSelect=Zt}}},[p]),t.useEffect(()=>{if(f.current){const g=()=>{h(!1),d.isPointerDownOnContentRef.current=!1,setTimeout(()=>{document.getSelection()?.toString()!==""&&(d.hasSelectionRef.current=!0)})};return document.addEventListener("pointerup",g),()=>{document.removeEventListener("pointerup",g),d.hasSelectionRef.current=!1,d.isPointerDownOnContentRef.current=!1}}},[d.isPointerDownOnContentRef,d.hasSelectionRef]),t.useEffect(()=>{f.current&&Ru(f.current).forEach(b=>b.setAttribute("tabindex","-1"))}),u.jsx(Mt,{asChild:!0,disableOutsidePointerEvents:!1,onInteractOutside:i,onEscapeKeyDown:a,onPointerDownOutside:n,onFocusOutside:S(s,g=>{g.preventDefault()}),onDismiss:d.onDismiss,children:u.jsx(xr,{...m,...c,onPointerDown:S(c.onPointerDown,g=>{g.currentTarget.contains(g.target)&&h(!0),d.hasSelectionRef.current=!1,d.isPointerDownOnContentRef.current=!0}),ref:l,style:{...c.style,userSelect:p?"text":void 0,WebkitUserSelect:p?"text":void 0,"--radix-hover-card-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-hover-card-content-available-width":"var(--radix-popper-available-width)","--radix-hover-card-content-available-height":"var(--radix-popper-available-height)","--radix-hover-card-trigger-width":"var(--radix-popper-anchor-width)","--radix-hover-card-trigger-height":"var(--radix-popper-anchor-height)"}})})}),wu="HoverCardArrow",Eu=t.forwardRef((e,o)=>{const{__scopeHoverCard:r,...a}=e,n=Dt(r);return u.jsx(wr,{...n,...a,ref:o})});Eu.displayName=wu;function bt(e){return o=>o.pointerType==="touch"?void 0:e()}function Ru(e){const o=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:a=>a.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP});for(;r.nextNode();)o.push(r.currentNode);return o}var Nu=za,Su=Ka,Mu=Wa,_u=Ua,Be="NavigationMenu",[Eo,Za,Tu]=fo(Be),[qt,Pu,Iu]=fo(Be),[Ro,Wg]=X(Be,[Tu,Iu]),[ku,fe]=Ro(Be),[$u,Du]=Ro(Be),Ya=t.forwardRef((e,o)=>{const{__scopeNavigationMenu:r,value:a,onValueChange:n,defaultValue:s,delayDuration:i=200,skipDelayDuration:c=300,orientation:d="horizontal",dir:m,...f}=e,[l,p]=t.useState(null),h=B(o,O=>p(O)),g=st(m),b=t.useRef(0),C=t.useRef(0),R=t.useRef(0),[j,E]=t.useState(!0),[N,M]=ae({prop:a,onChange:O=>{const W=O!=="",D=c>0;W?(window.clearTimeout(R.current),D&&E(!1)):(window.clearTimeout(R.current),R.current=window.setTimeout(()=>E(!0),c)),n?.(O)},defaultProp:s??"",caller:Be}),$=t.useCallback(()=>{window.clearTimeout(C.current),C.current=window.setTimeout(()=>M(""),150)},[M]),U=t.useCallback(O=>{window.clearTimeout(C.current),M(O)},[M]),Q=t.useCallback(O=>{N===O?window.clearTimeout(C.current):b.current=window.setTimeout(()=>{window.clearTimeout(C.current),M(O)},i)},[N,M,i]);return t.useEffect(()=>()=>{window.clearTimeout(b.current),window.clearTimeout(C.current),window.clearTimeout(R.current)},[]),u.jsx(qa,{scope:r,isRootMenu:!0,value:N,dir:g,orientation:d,rootNavigationMenu:l,onTriggerEnter:O=>{window.clearTimeout(b.current),j?Q(O):U(O)},onTriggerLeave:()=>{window.clearTimeout(b.current),$()},onContentEnter:()=>window.clearTimeout(C.current),onContentLeave:$,onItemSelect:O=>{M(W=>W===O?"":O)},onItemDismiss:()=>M(""),children:u.jsx(P.nav,{"aria-label":"Main","data-orientation":d,dir:g,...f,ref:h})})});Ya.displayName=Be;var Xt="NavigationMenuSub",Au=t.forwardRef((e,o)=>{const{__scopeNavigationMenu:r,value:a,onValueChange:n,defaultValue:s,orientation:i="horizontal",...c}=e,d=fe(Xt,r),[m,f]=ae({prop:a,onChange:n,defaultProp:s??"",caller:Xt});return u.jsx(qa,{scope:r,isRootMenu:!1,value:m,dir:d.dir,orientation:i,rootNavigationMenu:d.rootNavigationMenu,onTriggerEnter:l=>f(l),onItemSelect:l=>f(l),onItemDismiss:()=>f(""),children:u.jsx(P.div,{"data-orientation":i,...c,ref:o})})});Au.displayName=Xt;var qa=e=>{const{scope:o,isRootMenu:r,rootNavigationMenu:a,dir:n,orientation:s,children:i,value:c,onItemSelect:d,onItemDismiss:m,onTriggerEnter:f,onTriggerLeave:l,onContentEnter:p,onContentLeave:h}=e,[g,b]=t.useState(null),[C,R]=t.useState(new Map),[j,E]=t.useState(null);return u.jsx(ku,{scope:o,isRootMenu:r,rootNavigationMenu:a,value:c,previousValue:It(c),baseId:je(),dir:n,orientation:s,viewport:g,onViewportChange:b,indicatorTrack:j,onIndicatorTrackChange:E,onTriggerEnter:we(f),onTriggerLeave:we(l),onContentEnter:we(p),onContentLeave:we(h),onItemSelect:we(d),onItemDismiss:we(m),onViewportContentChange:t.useCallback((N,M)=>{R($=>($.set(N,M),new Map($)))},[]),onViewportContentRemove:t.useCallback(N=>{R(M=>M.has(N)?(M.delete(N),new Map(M)):M)},[]),children:u.jsx(Eo.Provider,{scope:o,children:u.jsx($u,{scope:o,items:C,children:i})})})},Xa="NavigationMenuList",Qa=t.forwardRef((e,o)=>{const{__scopeNavigationMenu:r,...a}=e,n=fe(Xa,r),s=u.jsx(P.ul,{"data-orientation":n.orientation,...a,ref:o});return u.jsx(P.div,{style:{position:"relative"},ref:n.onIndicatorTrackChange,children:u.jsx(Eo.Slot,{scope:r,children:n.isRootMenu?u.jsx(an,{asChild:!0,children:s}):s})})});Qa.displayName=Xa;var Ja="NavigationMenuItem",[Ou,en]=Ro(Ja),tn=t.forwardRef((e,o)=>{const{__scopeNavigationMenu:r,value:a,...n}=e,s=je(),i=a||s||"LEGACY_REACT_AUTO_VALUE",c=t.useRef(null),d=t.useRef(null),m=t.useRef(null),f=t.useRef(()=>{}),l=t.useRef(!1),p=t.useCallback((g="start")=>{if(c.current){f.current();const b=Jt(c.current);b.length&&Mo(g==="start"?b:b.reverse())}},[]),h=t.useCallback(()=>{if(c.current){const g=Jt(c.current);g.length&&(f.current=Uu(g))}},[]);return u.jsx(Ou,{scope:r,value:i,triggerRef:d,contentRef:c,focusProxyRef:m,wasEscapeCloseRef:l,onEntryKeyDown:p,onFocusProxyEnter:p,onRootContentClose:h,onContentFocusOutside:h,children:u.jsx(P.li,{...n,ref:o})})});tn.displayName=Ja;var Qt="NavigationMenuTrigger",ju=t.forwardRef((e,o)=>{const{__scopeNavigationMenu:r,disabled:a,...n}=e,s=fe(Qt,e.__scopeNavigationMenu),i=en(Qt,e.__scopeNavigationMenu),c=t.useRef(null),d=B(c,i.triggerRef,o),m=sn(s.baseId,i.value),f=ln(s.baseId,i.value),l=t.useRef(!1),p=t.useRef(!1),h=i.value===s.value;return u.jsxs(u.Fragment,{children:[u.jsx(Eo.ItemSlot,{scope:r,value:i.value,children:u.jsx(nn,{asChild:!0,children:u.jsx(P.button,{id:m,disabled:a,"data-disabled":a?"":void 0,"data-state":_o(h),"aria-expanded":h,"aria-controls":f,...n,ref:d,onPointerEnter:S(e.onPointerEnter,()=>{p.current=!1,i.wasEscapeCloseRef.current=!1}),onPointerMove:S(e.onPointerMove,yt(()=>{a||p.current||i.wasEscapeCloseRef.current||l.current||(s.onTriggerEnter(i.value),l.current=!0)})),onPointerLeave:S(e.onPointerLeave,yt(()=>{a||(s.onTriggerLeave(),l.current=!1)})),onClick:S(e.onClick,()=>{s.onItemSelect(i.value),p.current=h}),onKeyDown:S(e.onKeyDown,g=>{const C={horizontal:"ArrowDown",vertical:s.dir==="rtl"?"ArrowLeft":"ArrowRight"}[s.orientation];h&&g.key===C&&(i.onEntryKeyDown(),g.preventDefault())})})})}),h&&u.jsxs(u.Fragment,{children:[u.jsx(ur,{"aria-hidden":!0,tabIndex:0,ref:i.focusProxyRef,onFocus:g=>{const b=i.contentRef.current,C=g.relatedTarget,R=C===c.current,j=b?.contains(C);(R||!j)&&i.onFocusProxyEnter(R?"start":"end")}}),s.viewport&&u.jsx("span",{"aria-owns":f})]})]})});ju.displayName=Qt;var Lu="NavigationMenuLink",or="navigationMenu.linkSelect",on=t.forwardRef((e,o)=>{const{__scopeNavigationMenu:r,active:a,onSelect:n,...s}=e;return u.jsx(nn,{asChild:!0,children:u.jsx(P.a,{"data-active":a?"":void 0,"aria-current":a?"page":void 0,...s,ref:o,onClick:S(e.onClick,i=>{const c=i.target,d=new CustomEvent(or,{bubbles:!0,cancelable:!0});if(c.addEventListener(or,m=>n?.(m),{once:!0}),Qo(c,d),!d.defaultPrevented&&!i.metaKey){const m=new CustomEvent(gt,{bubbles:!0,cancelable:!0});Qo(c,m)}},{checkForDefaultPrevented:!1})})})});on.displayName=Lu;var No="NavigationMenuIndicator",Bu=t.forwardRef((e,o)=>{const{forceMount:r,...a}=e,n=fe(No,e.__scopeNavigationMenu),s=!!n.value;return n.indicatorTrack?_r.createPortal(u.jsx(ce,{present:r||s,children:u.jsx(Hu,{...a,ref:o})}),n.indicatorTrack):null});Bu.displayName=No;var Hu=t.forwardRef((e,o)=>{const{__scopeNavigationMenu:r,...a}=e,n=fe(No,r),s=Za(r),[i,c]=t.useState(null),[d,m]=t.useState(null),f=n.orientation==="horizontal",l=!!n.value;t.useEffect(()=>{const g=s().find(b=>b.value===n.value)?.ref.current;g&&c(g)},[s,n.value]);const p=()=>{i&&m({size:f?i.offsetWidth:i.offsetHeight,offset:f?i.offsetLeft:i.offsetTop})};return eo(i,p),eo(n.indicatorTrack,p),d?u.jsx(P.div,{"aria-hidden":!0,"data-state":l?"visible":"hidden","data-orientation":n.orientation,...a,ref:o,style:{position:"absolute",...f?{left:0,width:d.size+"px",transform:`translateX(${d.offset}px)`}:{top:0,height:d.size+"px",transform:`translateY(${d.offset}px)`},...a.style}}):null}),Ue="NavigationMenuContent",Vu=t.forwardRef((e,o)=>{const{forceMount:r,...a}=e,n=fe(Ue,e.__scopeNavigationMenu),s=en(Ue,e.__scopeNavigationMenu),i=B(s.contentRef,o),c=s.value===n.value,d={value:s.value,triggerRef:s.triggerRef,focusProxyRef:s.focusProxyRef,wasEscapeCloseRef:s.wasEscapeCloseRef,onContentFocusOutside:s.onContentFocusOutside,onRootContentClose:s.onRootContentClose,...a};return n.viewport?u.jsx(Fu,{forceMount:r,...d,ref:i}):u.jsx(ce,{present:r||c,children:u.jsx(rn,{"data-state":_o(c),...d,ref:i,onPointerEnter:S(e.onPointerEnter,n.onContentEnter),onPointerLeave:S(e.onPointerLeave,yt(n.onContentLeave)),style:{pointerEvents:!c&&n.isRootMenu?"none":void 0,...d.style}})})});Vu.displayName=Ue;var Fu=t.forwardRef((e,o)=>{const r=fe(Ue,e.__scopeNavigationMenu),{onViewportContentChange:a,onViewportContentRemove:n}=r;return We(()=>{a(e.value,{ref:o,...e})},[e,o,a]),We(()=>()=>n(e.value),[e.value,n]),null}),gt="navigationMenu.rootContentDismiss",rn=t.forwardRef((e,o)=>{const{__scopeNavigationMenu:r,value:a,triggerRef:n,focusProxyRef:s,wasEscapeCloseRef:i,onRootContentClose:c,onContentFocusOutside:d,...m}=e,f=fe(Ue,r),l=t.useRef(null),p=B(l,o),h=sn(f.baseId,a),g=ln(f.baseId,a),b=Za(r),C=t.useRef(null),{onItemDismiss:R}=f;t.useEffect(()=>{const E=l.current;if(f.isRootMenu&&E){const N=()=>{R(),c(),E.contains(document.activeElement)&&n.current?.focus()};return E.addEventListener(gt,N),()=>E.removeEventListener(gt,N)}},[f.isRootMenu,e.value,n,R,c]);const j=t.useMemo(()=>{const N=b().map(W=>W.value);f.dir==="rtl"&&N.reverse();const M=N.indexOf(f.value),$=N.indexOf(f.previousValue),U=a===f.value,Q=$===N.indexOf(a);if(!U&&!Q)return C.current;const O=(()=>{if(M!==$){if(U&&$!==-1)return M>$?"from-end":"from-start";if(Q&&M!==-1)return M>$?"to-start":"to-end"}return null})();return C.current=O,O},[f.previousValue,f.value,f.dir,b,a]);return u.jsx(an,{asChild:!0,children:u.jsx(Mt,{id:g,"aria-labelledby":h,"data-motion":j,"data-orientation":f.orientation,...m,ref:p,disableOutsidePointerEvents:!1,onDismiss:()=>{const E=new Event(gt,{bubbles:!0,cancelable:!0});l.current?.dispatchEvent(E)},onFocusOutside:S(e.onFocusOutside,E=>{d();const N=E.target;f.rootNavigationMenu?.contains(N)&&E.preventDefault()}),onPointerDownOutside:S(e.onPointerDownOutside,E=>{const N=E.target,M=b().some(U=>U.ref.current?.contains(N)),$=f.isRootMenu&&f.viewport?.contains(N);(M||$||!f.isRootMenu)&&E.preventDefault()}),onKeyDown:S(e.onKeyDown,E=>{const N=E.altKey||E.ctrlKey||E.metaKey;if(E.key==="Tab"&&!N){const $=Jt(E.currentTarget),U=document.activeElement,Q=$.findIndex(D=>D===U),W=E.shiftKey?$.slice(0,Q).reverse():$.slice(Q+1,$.length);Mo(W)?E.preventDefault():s.current?.focus()}}),onEscapeKeyDown:S(e.onEscapeKeyDown,E=>{i.current=!0})})})}),So="NavigationMenuViewport",zu=t.forwardRef((e,o)=>{const{forceMount:r,...a}=e,s=!!fe(So,e.__scopeNavigationMenu).value;return u.jsx(ce,{present:r||s,children:u.jsx(Gu,{...a,ref:o})})});zu.displayName=So;var Gu=t.forwardRef((e,o)=>{const{__scopeNavigationMenu:r,children:a,...n}=e,s=fe(So,r),i=B(o,s.onViewportChange),c=Du(Ue,e.__scopeNavigationMenu),[d,m]=t.useState(null),[f,l]=t.useState(null),p=d?d?.width+"px":void 0,h=d?d?.height+"px":void 0,g=!!s.value,b=g?s.value:s.previousValue;return eo(f,()=>{f&&m({width:f.offsetWidth,height:f.offsetHeight})}),u.jsx(P.div,{"data-state":_o(g),"data-orientation":s.orientation,...n,ref:i,style:{pointerEvents:!g&&s.isRootMenu?"none":void 0,"--radix-navigation-menu-viewport-width":p,"--radix-navigation-menu-viewport-height":h,...n.style},onPointerEnter:S(e.onPointerEnter,s.onContentEnter),onPointerLeave:S(e.onPointerLeave,yt(s.onContentLeave)),children:Array.from(c.items).map(([R,{ref:j,forceMount:E,...N}])=>{const M=b===R;return u.jsx(ce,{present:E||M,children:u.jsx(rn,{...N,ref:pr(j,$=>{M&&$&&l($)})})},R)})})}),Ku="FocusGroup",an=t.forwardRef((e,o)=>{const{__scopeNavigationMenu:r,...a}=e,n=fe(Ku,r);return u.jsx(qt.Provider,{scope:r,children:u.jsx(qt.Slot,{scope:r,children:u.jsx(P.div,{dir:n.dir,...a,ref:o})})})}),rr=["ArrowRight","ArrowLeft","ArrowUp","ArrowDown"],Wu="FocusGroupItem",nn=t.forwardRef((e,o)=>{const{__scopeNavigationMenu:r,...a}=e,n=Pu(r),s=fe(Wu,r);return u.jsx(qt.ItemSlot,{scope:r,children:u.jsx(P.button,{...a,ref:o,onKeyDown:S(e.onKeyDown,i=>{if(["Home","End",...rr].includes(i.key)){let d=n().map(l=>l.ref.current);if([s.dir==="rtl"?"ArrowRight":"ArrowLeft","ArrowUp","End"].includes(i.key)&&d.reverse(),rr.includes(i.key)){const l=d.indexOf(i.currentTarget);d=d.slice(l+1)}setTimeout(()=>Mo(d)),i.preventDefault()}})})})});function Jt(e){const o=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:a=>{const n=a.tagName==="INPUT"&&a.type==="hidden";return a.disabled||a.hidden||n?NodeFilter.FILTER_SKIP:a.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)o.push(r.currentNode);return o}function Mo(e){const o=document.activeElement;return e.some(r=>r===o?!0:(r.focus(),document.activeElement!==o))}function Uu(e){return e.forEach(o=>{o.dataset.tabindex=o.getAttribute("tabindex")||"",o.setAttribute("tabindex","-1")}),()=>{e.forEach(o=>{const r=o.dataset.tabindex;o.setAttribute("tabindex",r)})}}function eo(e,o){const r=we(o);We(()=>{let a=0;if(e){const n=new ResizeObserver(()=>{cancelAnimationFrame(a),a=window.requestAnimationFrame(r)});return n.observe(e),()=>{window.cancelAnimationFrame(a),n.unobserve(e)}}},[e,r])}function _o(e){return e?"open":"closed"}function sn(e,o){return`${e}-trigger-${o}`}function ln(e,o){return`${e}-content-${o}`}function yt(e){return o=>o.pointerType==="mouse"?e(o):void 0}var Zu=Ya,Yu=Qa,qu=tn,Xu=on,Ot="Popover",[cn,Ug]=X(Ot,[_t]),ct=_t(),[Qu,Ie]=cn(Ot),dn=e=>{const{__scopePopover:o,children:r,open:a,defaultOpen:n,onOpenChange:s,modal:i=!1}=e,c=ct(o),d=t.useRef(null),[m,f]=t.useState(!1),[l,p]=ae({prop:a,defaultProp:n??!1,onChange:s,caller:Ot});return u.jsx(yr,{...c,children:u.jsx(Qu,{scope:o,contentId:je(),triggerRef:d,open:l,onOpenChange:p,onOpenToggle:t.useCallback(()=>p(h=>!h),[p]),hasCustomAnchor:m,onCustomAnchorAdd:t.useCallback(()=>f(!0),[]),onCustomAnchorRemove:t.useCallback(()=>f(!1),[]),modal:i,children:r})})};dn.displayName=Ot;var un="PopoverAnchor",mn=t.forwardRef((e,o)=>{const{__scopePopover:r,...a}=e,n=Ie(un,r),s=ct(r),{onCustomAnchorAdd:i,onCustomAnchorRemove:c}=n;return t.useEffect(()=>(i(),()=>c()),[i,c]),u.jsx(mo,{...s,...a,ref:o})});mn.displayName=un;var fn="PopoverTrigger",pn=t.forwardRef((e,o)=>{const{__scopePopover:r,...a}=e,n=Ie(fn,r),s=ct(r),i=B(o,n.triggerRef),c=u.jsx(P.button,{type:"button","aria-haspopup":"dialog","aria-expanded":n.open,"aria-controls":n.contentId,"data-state":yn(n.open),...a,ref:i,onClick:S(e.onClick,n.onOpenToggle)});return n.hasCustomAnchor?c:u.jsx(mo,{asChild:!0,...s,children:c})});pn.displayName=fn;var To="PopoverPortal",[Ju,em]=cn(To,{forceMount:void 0}),gn=e=>{const{__scopePopover:o,forceMount:r,children:a,container:n}=e,s=Ie(To,o);return u.jsx(Ju,{scope:o,forceMount:r,children:u.jsx(ce,{present:r||s.open,children:u.jsx(uo,{asChild:!0,container:n,children:a})})})};gn.displayName=To;var Ze="PopoverContent",vn=t.forwardRef((e,o)=>{const r=em(Ze,e.__scopePopover),{forceMount:a=r.forceMount,...n}=e,s=Ie(Ze,e.__scopePopover);return u.jsx(ce,{present:a||s.open,children:s.modal?u.jsx(om,{...n,ref:o}):u.jsx(rm,{...n,ref:o})})});vn.displayName=Ze;var tm=fr("PopoverContent.RemoveScroll"),om=t.forwardRef((e,o)=>{const r=Ie(Ze,e.__scopePopover),a=t.useRef(null),n=B(o,a),s=t.useRef(!1);return t.useEffect(()=>{const i=a.current;if(i)return vr(i)},[]),u.jsx(gr,{as:tm,allowPinchZoom:!0,children:u.jsx(hn,{...e,ref:n,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:S(e.onCloseAutoFocus,i=>{i.preventDefault(),s.current||r.triggerRef.current?.focus()}),onPointerDownOutside:S(e.onPointerDownOutside,i=>{const c=i.detail.originalEvent,d=c.button===0&&c.ctrlKey===!0,m=c.button===2||d;s.current=m},{checkForDefaultPrevented:!1}),onFocusOutside:S(e.onFocusOutside,i=>i.preventDefault(),{checkForDefaultPrevented:!1})})})}),rm=t.forwardRef((e,o)=>{const r=Ie(Ze,e.__scopePopover),a=t.useRef(!1),n=t.useRef(!1);return u.jsx(hn,{...e,ref:o,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:s=>{e.onCloseAutoFocus?.(s),s.defaultPrevented||(a.current||r.triggerRef.current?.focus(),s.preventDefault()),a.current=!1,n.current=!1},onInteractOutside:s=>{e.onInteractOutside?.(s),s.defaultPrevented||(a.current=!0,s.detail.originalEvent.type==="pointerdown"&&(n.current=!0));const i=s.target;r.triggerRef.current?.contains(i)&&s.preventDefault(),s.detail.originalEvent.type==="focusin"&&n.current&&s.preventDefault()}})}),hn=t.forwardRef((e,o)=>{const{__scopePopover:r,trapFocus:a,onOpenAutoFocus:n,onCloseAutoFocus:s,disableOutsidePointerEvents:i,onEscapeKeyDown:c,onPointerDownOutside:d,onFocusOutside:m,onInteractOutside:f,...l}=e,p=Ie(Ze,r),h=ct(r);return hr(),u.jsx(Cr,{asChild:!0,loop:!0,trapped:a,onMountAutoFocus:n,onUnmountAutoFocus:s,children:u.jsx(Mt,{asChild:!0,disableOutsidePointerEvents:i,onInteractOutside:f,onEscapeKeyDown:c,onPointerDownOutside:d,onFocusOutside:m,onDismiss:()=>p.onOpenChange(!1),children:u.jsx(xr,{"data-state":yn(p.open),role:"dialog",id:p.contentId,...h,...l,ref:o,style:{...l.style,"--radix-popover-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-popover-content-available-width":"var(--radix-popper-available-width)","--radix-popover-content-available-height":"var(--radix-popper-available-height)","--radix-popover-trigger-width":"var(--radix-popper-anchor-width)","--radix-popover-trigger-height":"var(--radix-popper-anchor-height)"}})})})}),Cn="PopoverClose",bn=t.forwardRef((e,o)=>{const{__scopePopover:r,...a}=e,n=Ie(Cn,r);return u.jsx(P.button,{type:"button",...a,ref:o,onClick:S(e.onClick,()=>n.onOpenChange(!1))})});bn.displayName=Cn;var am="PopoverArrow",nm=t.forwardRef((e,o)=>{const{__scopePopover:r,...a}=e,n=ct(r);return u.jsx(wr,{...n,...a,ref:o})});nm.displayName=am;function yn(e){return e?"open":"closed"}var sm=dn,im=mn,lm=pn,cm=gn,dm=vn,um=bn,Po="Radio",[mm,xn]=X(Po),[fm,pm]=mm(Po),wn=t.forwardRef((e,o)=>{const{__scopeRadio:r,name:a,checked:n=!1,required:s,disabled:i,value:c="on",onCheck:d,form:m,...f}=e,[l,p]=t.useState(null),h=B(o,C=>p(C)),g=t.useRef(!1),b=l?m||!!l.closest("form"):!0;return u.jsxs(fm,{scope:r,checked:n,disabled:i,children:[u.jsx(P.button,{type:"button",role:"radio","aria-checked":n,"data-state":Sn(n),"data-disabled":i?"":void 0,disabled:i,value:c,...f,ref:h,onClick:S(e.onClick,C=>{n||d?.(),b&&(g.current=C.isPropagationStopped(),g.current||C.stopPropagation())})}),b&&u.jsx(Nn,{control:l,bubbles:!g.current,name:a,value:c,checked:n,required:s,disabled:i,form:m,style:{transform:"translateX(-100%)"}})]})});wn.displayName=Po;var En="RadioIndicator",Rn=t.forwardRef((e,o)=>{const{__scopeRadio:r,forceMount:a,...n}=e,s=pm(En,r);return u.jsx(ce,{present:a||s.checked,children:u.jsx(P.span,{"data-state":Sn(s.checked),"data-disabled":s.disabled?"":void 0,...n,ref:o})})});Rn.displayName=En;var gm="RadioBubbleInput",Nn=t.forwardRef(({__scopeRadio:e,control:o,checked:r,bubbles:a=!0,...n},s)=>{const i=t.useRef(null),c=B(i,s),d=It(r),m=po(o);return t.useEffect(()=>{const f=i.current;if(!f)return;const l=window.HTMLInputElement.prototype,h=Object.getOwnPropertyDescriptor(l,"checked").set;if(d!==r&&h){const g=new Event("click",{bubbles:a});h.call(f,r),f.dispatchEvent(g)}},[d,r,a]),u.jsx(P.input,{type:"radio","aria-hidden":!0,defaultChecked:r,...n,tabIndex:-1,ref:c,style:{...n.style,...m,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});Nn.displayName=gm;function Sn(e){return e?"checked":"unchecked"}var vm=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],jt="RadioGroup",[hm,Mn]=X(jt,[Pe,xn]),_n=Pe(),Tn=xn(),[Cm,bm]=hm(jt),Pn=t.forwardRef((e,o)=>{const{__scopeRadioGroup:r,name:a,defaultValue:n,value:s,required:i=!1,disabled:c=!1,orientation:d,dir:m,loop:f=!0,onValueChange:l,...p}=e,h=_n(r),g=st(m),[b,C]=ae({prop:s,defaultProp:n??null,onChange:l,caller:jt});return u.jsx(Cm,{scope:r,name:a,required:i,disabled:c,value:b,onValueChange:C,children:u.jsx(Pt,{asChild:!0,...h,orientation:d,dir:g,loop:f,children:u.jsx(P.div,{role:"radiogroup","aria-required":i,"aria-orientation":d,"data-disabled":c?"":void 0,dir:g,...p,ref:o})})})});Pn.displayName=jt;var In="RadioGroupItem",kn=t.forwardRef((e,o)=>{const{__scopeRadioGroup:r,disabled:a,...n}=e,s=bm(In,r),i=s.disabled||a,c=_n(r),d=Tn(r),m=t.useRef(null),f=B(o,m),l=s.value===n.value,p=t.useRef(!1);return t.useEffect(()=>{const h=b=>{vm.includes(b.key)&&(p.current=!0)},g=()=>p.current=!1;return document.addEventListener("keydown",h),document.addEventListener("keyup",g),()=>{document.removeEventListener("keydown",h),document.removeEventListener("keyup",g)}},[]),u.jsx(Tt,{asChild:!0,...c,focusable:!i,active:l,children:u.jsx(wn,{disabled:i,required:s.required,checked:l,...d,...n,name:s.name,ref:f,onCheck:()=>s.onValueChange(n.value),onKeyDown:S(h=>{h.key==="Enter"&&h.preventDefault()}),onFocus:S(n.onFocus,()=>{p.current&&m.current?.click()})})})});kn.displayName=In;var ym="RadioGroupIndicator",xm=t.forwardRef((e,o)=>{const{__scopeRadioGroup:r,...a}=e,n=Tn(r);return u.jsx(Rn,{...n,...a,ref:o})});xm.displayName=ym;var $n=Pn,Dn=kn,An=["PageUp","PageDown"],On=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],jn={"from-left":["Home","PageDown","ArrowDown","ArrowLeft"],"from-right":["Home","PageDown","ArrowDown","ArrowRight"],"from-bottom":["Home","PageDown","ArrowDown","ArrowLeft"],"from-top":["Home","PageDown","ArrowUp","ArrowLeft"]},Ye="Slider",[to,wm,Em]=fo(Ye),[Ln,Zg]=X(Ye,[Em]),[Rm,Lt]=Ln(Ye),Bn=t.forwardRef((e,o)=>{const{name:r,min:a=0,max:n=100,step:s=1,orientation:i="horizontal",disabled:c=!1,minStepsBetweenThumbs:d=0,defaultValue:m=[a],value:f,onValueChange:l=()=>{},onValueCommit:p=()=>{},inverted:h=!1,form:g,...b}=e,C=t.useRef(new Set),R=t.useRef(0),E=i==="horizontal"?Nm:Sm,[N=[],M]=ae({prop:f,defaultProp:m,onChange:D=>{[...C.current][R.current]?.focus(),l(D)}}),$=t.useRef(N);function U(D){const Y=Im(N,D);W(D,Y)}function Q(D){W(D,R.current)}function O(){const D=$.current[R.current];N[R.current]!==D&&p(N)}function W(D,Y,{commit:ne}={commit:!1}){const w=Am(s),_=Om(Math.round((D-a)/s)*s+a,w),T=Er(_,[a,n]);M((A=[])=>{const G=Tm(A,T,Y);if(Dm(G,d*s)){R.current=G.indexOf(T);const J=String(G)!==String(A);return J&&ne&&p(G),J?G:A}else return A})}return u.jsx(Rm,{scope:e.__scopeSlider,name:r,disabled:c,min:a,max:n,valueIndexToChangeRef:R,thumbs:C.current,values:N,orientation:i,form:g,children:u.jsx(to.Provider,{scope:e.__scopeSlider,children:u.jsx(to.Slot,{scope:e.__scopeSlider,children:u.jsx(E,{"aria-disabled":c,"data-disabled":c?"":void 0,...b,ref:o,onPointerDown:S(b.onPointerDown,()=>{c||($.current=N)}),min:a,max:n,inverted:h,onSlideStart:c?void 0:U,onSlideMove:c?void 0:Q,onSlideEnd:c?void 0:O,onHomeKeyDown:()=>!c&&W(a,0,{commit:!0}),onEndKeyDown:()=>!c&&W(n,N.length-1,{commit:!0}),onStepKeyDown:({event:D,direction:Y})=>{if(!c){const _=An.includes(D.key)||D.shiftKey&&On.includes(D.key)?10:1,T=R.current,A=N[T],G=s*_*Y;W(A+G,T,{commit:!0})}}})})})})});Bn.displayName=Ye;var[Hn,Vn]=Ln(Ye,{startEdge:"left",endEdge:"right",size:"width",direction:1}),Nm=t.forwardRef((e,o)=>{const{min:r,max:a,dir:n,inverted:s,onSlideStart:i,onSlideMove:c,onSlideEnd:d,onStepKeyDown:m,...f}=e,[l,p]=t.useState(null),h=B(o,E=>p(E)),g=t.useRef(void 0),b=st(n),C=b==="ltr",R=C&&!s||!C&&s;function j(E){const N=g.current||l.getBoundingClientRect(),M=[0,N.width],U=Io(M,R?[r,a]:[a,r]);return g.current=N,U(E-N.left)}return u.jsx(Hn,{scope:e.__scopeSlider,startEdge:R?"left":"right",endEdge:R?"right":"left",direction:R?1:-1,size:"width",children:u.jsx(Fn,{dir:b,"data-orientation":"horizontal",...f,ref:h,style:{...f.style,"--radix-slider-thumb-transform":"translateX(-50%)"},onSlideStart:E=>{const N=j(E.clientX);i?.(N)},onSlideMove:E=>{const N=j(E.clientX);c?.(N)},onSlideEnd:()=>{g.current=void 0,d?.()},onStepKeyDown:E=>{const M=jn[R?"from-left":"from-right"].includes(E.key);m?.({event:E,direction:M?-1:1})}})})}),Sm=t.forwardRef((e,o)=>{const{min:r,max:a,inverted:n,onSlideStart:s,onSlideMove:i,onSlideEnd:c,onStepKeyDown:d,...m}=e,f=t.useRef(null),l=B(o,f),p=t.useRef(void 0),h=!n;function g(b){const C=p.current||f.current.getBoundingClientRect(),R=[0,C.height],E=Io(R,h?[a,r]:[r,a]);return p.current=C,E(b-C.top)}return u.jsx(Hn,{scope:e.__scopeSlider,startEdge:h?"bottom":"top",endEdge:h?"top":"bottom",size:"height",direction:h?1:-1,children:u.jsx(Fn,{"data-orientation":"vertical",...m,ref:l,style:{...m.style,"--radix-slider-thumb-transform":"translateY(50%)"},onSlideStart:b=>{const C=g(b.clientY);s?.(C)},onSlideMove:b=>{const C=g(b.clientY);i?.(C)},onSlideEnd:()=>{p.current=void 0,c?.()},onStepKeyDown:b=>{const R=jn[h?"from-bottom":"from-top"].includes(b.key);d?.({event:b,direction:R?-1:1})}})})}),Fn=t.forwardRef((e,o)=>{const{__scopeSlider:r,onSlideStart:a,onSlideMove:n,onSlideEnd:s,onHomeKeyDown:i,onEndKeyDown:c,onStepKeyDown:d,...m}=e,f=Lt(Ye,r);return u.jsx(P.span,{...m,ref:o,onKeyDown:S(e.onKeyDown,l=>{l.key==="Home"?(i(l),l.preventDefault()):l.key==="End"?(c(l),l.preventDefault()):An.concat(On).includes(l.key)&&(d(l),l.preventDefault())}),onPointerDown:S(e.onPointerDown,l=>{const p=l.target;p.setPointerCapture(l.pointerId),l.preventDefault(),f.thumbs.has(p)?p.focus():a(l)}),onPointerMove:S(e.onPointerMove,l=>{l.target.hasPointerCapture(l.pointerId)&&n(l)}),onPointerUp:S(e.onPointerUp,l=>{const p=l.target;p.hasPointerCapture(l.pointerId)&&(p.releasePointerCapture(l.pointerId),s(l))})})}),zn="SliderTrack",Gn=t.forwardRef((e,o)=>{const{__scopeSlider:r,...a}=e,n=Lt(zn,r);return u.jsx(P.span,{"data-disabled":n.disabled?"":void 0,"data-orientation":n.orientation,...a,ref:o})});Gn.displayName=zn;var oo="SliderRange",Kn=t.forwardRef((e,o)=>{const{__scopeSlider:r,...a}=e,n=Lt(oo,r),s=Vn(oo,r),i=t.useRef(null),c=B(o,i),d=n.values.length,m=n.values.map(p=>Zn(p,n.min,n.max)),f=d>1?Math.min(...m):0,l=100-Math.max(...m);return u.jsx(P.span,{"data-orientation":n.orientation,"data-disabled":n.disabled?"":void 0,...a,ref:c,style:{...e.style,[s.startEdge]:f+"%",[s.endEdge]:l+"%"}})});Kn.displayName=oo;var ro="SliderThumb",Wn=t.forwardRef((e,o)=>{const r=wm(e.__scopeSlider),[a,n]=t.useState(null),s=B(o,c=>n(c)),i=t.useMemo(()=>a?r().findIndex(c=>c.ref.current===a):-1,[r,a]);return u.jsx(Mm,{...e,ref:s,index:i})}),Mm=t.forwardRef((e,o)=>{const{__scopeSlider:r,index:a,name:n,...s}=e,i=Lt(ro,r),c=Vn(ro,r),[d,m]=t.useState(null),f=B(o,j=>m(j)),l=d?i.form||!!d.closest("form"):!0,p=po(d),h=i.values[a],g=h===void 0?0:Zn(h,i.min,i.max),b=Pm(a,i.values.length),C=p?.[c.size],R=C?km(C,g,c.direction):0;return t.useEffect(()=>{if(d)return i.thumbs.add(d),()=>{i.thumbs.delete(d)}},[d,i.thumbs]),u.jsxs("span",{style:{transform:"var(--radix-slider-thumb-transform)",position:"absolute",[c.startEdge]:`calc(${g}% + ${R}px)`},children:[u.jsx(to.ItemSlot,{scope:e.__scopeSlider,children:u.jsx(P.span,{role:"slider","aria-label":e["aria-label"]||b,"aria-valuemin":i.min,"aria-valuenow":h,"aria-valuemax":i.max,"aria-orientation":i.orientation,"data-orientation":i.orientation,"data-disabled":i.disabled?"":void 0,tabIndex:i.disabled?void 0:0,...s,ref:f,style:h===void 0?{display:"none"}:e.style,onFocus:S(e.onFocus,()=>{i.valueIndexToChangeRef.current=a})})}),l&&u.jsx(Un,{name:n??(i.name?i.name+(i.values.length>1?"[]":""):void 0),form:i.form,value:h},a)]})});Wn.displayName=ro;var _m="RadioBubbleInput",Un=t.forwardRef(({__scopeSlider:e,value:o,...r},a)=>{const n=t.useRef(null),s=B(n,a),i=It(o);return t.useEffect(()=>{const c=n.current;if(!c)return;const d=window.HTMLInputElement.prototype,f=Object.getOwnPropertyDescriptor(d,"value").set;if(i!==o&&f){const l=new Event("input",{bubbles:!0});f.call(c,o),c.dispatchEvent(l)}},[i,o]),u.jsx(P.input,{style:{display:"none"},...r,ref:s,defaultValue:o})});Un.displayName=_m;function Tm(e=[],o,r){const a=[...e];return a[r]=o,a.sort((n,s)=>n-s)}function Zn(e,o,r){const s=100/(r-o)*(e-o);return Er(s,[0,100])}function Pm(e,o){return o>2?`Value ${e+1} of ${o}`:o===2?["Minimum","Maximum"][e]:void 0}function Im(e,o){if(e.length===1)return 0;const r=e.map(n=>Math.abs(n-o)),a=Math.min(...r);return r.indexOf(a)}function km(e,o,r){const a=e/2,s=Io([0,50],[0,a]);return(a-s(o)*r)*r}function $m(e){return e.slice(0,-1).map((o,r)=>e[r+1]-o)}function Dm(e,o){if(o>0){const r=$m(e);return Math.min(...r)>=o}return!0}function Io(e,o){return r=>{if(e[0]===e[1]||o[0]===o[1])return o[0];const a=(o[1]-o[0])/(e[1]-e[0]);return o[0]+a*(r-e[0])}}function Am(e){return(String(e).split(".")[1]||"").length}function Om(e,o){const r=Math.pow(10,o);return Math.round(e*r)/r}var jm=Bn,Lm=Gn,Bm=Kn,Hm=Wn,Bt="Switch",[Vm,Yg]=X(Bt),[Fm,zm]=Vm(Bt),Yn=t.forwardRef((e,o)=>{const{__scopeSwitch:r,name:a,checked:n,defaultChecked:s,required:i,disabled:c,value:d="on",onCheckedChange:m,form:f,...l}=e,[p,h]=t.useState(null),g=B(o,E=>h(E)),b=t.useRef(!1),C=p?f||!!p.closest("form"):!0,[R,j]=ae({prop:n,defaultProp:s??!1,onChange:m,caller:Bt});return u.jsxs(Fm,{scope:r,checked:R,disabled:c,children:[u.jsx(P.button,{type:"button",role:"switch","aria-checked":R,"aria-required":i,"data-state":Jn(R),"data-disabled":c?"":void 0,disabled:c,value:d,...l,ref:g,onClick:S(e.onClick,E=>{j(N=>!N),C&&(b.current=E.isPropagationStopped(),b.current||E.stopPropagation())})}),C&&u.jsx(Qn,{control:p,bubbles:!b.current,name:a,value:d,checked:R,required:i,disabled:c,form:f,style:{transform:"translateX(-100%)"}})]})});Yn.displayName=Bt;var qn="SwitchThumb",Xn=t.forwardRef((e,o)=>{const{__scopeSwitch:r,...a}=e,n=zm(qn,r);return u.jsx(P.span,{"data-state":Jn(n.checked),"data-disabled":n.disabled?"":void 0,...a,ref:o})});Xn.displayName=qn;var Gm="SwitchBubbleInput",Qn=t.forwardRef(({__scopeSwitch:e,control:o,checked:r,bubbles:a=!0,...n},s)=>{const i=t.useRef(null),c=B(i,s),d=It(r),m=po(o);return t.useEffect(()=>{const f=i.current;if(!f)return;const l=window.HTMLInputElement.prototype,h=Object.getOwnPropertyDescriptor(l,"checked").set;if(d!==r&&h){const g=new Event("click",{bubbles:a});h.call(f,r),f.dispatchEvent(g)}},[d,r,a]),u.jsx("input",{type:"checkbox","aria-hidden":!0,defaultChecked:r,...n,tabIndex:-1,ref:c,style:{...n.style,...m,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});Qn.displayName=Gm;function Jn(e){return e?"checked":"unchecked"}var Km=Yn,Wm=Xn,Ht="Tabs",[Um,qg]=X(Ht,[Pe]),es=Pe(),[Zm,ko]=Um(Ht),ts=t.forwardRef((e,o)=>{const{__scopeTabs:r,value:a,onValueChange:n,defaultValue:s,orientation:i="horizontal",dir:c,activationMode:d="automatic",...m}=e,f=st(c),[l,p]=ae({prop:a,onChange:n,defaultProp:s??"",caller:Ht});return u.jsx(Zm,{scope:r,baseId:je(),value:l,onValueChange:p,orientation:i,dir:f,activationMode:d,children:u.jsx(P.div,{dir:f,"data-orientation":i,...m,ref:o})})});ts.displayName=Ht;var os="TabsList",rs=t.forwardRef((e,o)=>{const{__scopeTabs:r,loop:a=!0,...n}=e,s=ko(os,r),i=es(r);return u.jsx(Pt,{asChild:!0,...i,orientation:s.orientation,dir:s.dir,loop:a,children:u.jsx(P.div,{role:"tablist","aria-orientation":s.orientation,...n,ref:o})})});rs.displayName=os;var as="TabsTrigger",ns=t.forwardRef((e,o)=>{const{__scopeTabs:r,value:a,disabled:n=!1,...s}=e,i=ko(as,r),c=es(r),d=ls(i.baseId,a),m=cs(i.baseId,a),f=a===i.value;return u.jsx(Tt,{asChild:!0,...c,focusable:!n,active:f,children:u.jsx(P.button,{type:"button",role:"tab","aria-selected":f,"aria-controls":m,"data-state":f?"active":"inactive","data-disabled":n?"":void 0,disabled:n,id:d,...s,ref:o,onMouseDown:S(e.onMouseDown,l=>{!n&&l.button===0&&l.ctrlKey===!1?i.onValueChange(a):l.preventDefault()}),onKeyDown:S(e.onKeyDown,l=>{[" ","Enter"].includes(l.key)&&i.onValueChange(a)}),onFocus:S(e.onFocus,()=>{const l=i.activationMode!=="manual";!f&&!n&&l&&i.onValueChange(a)})})})});ns.displayName=as;var ss="TabsContent",is=t.forwardRef((e,o)=>{const{__scopeTabs:r,value:a,forceMount:n,children:s,...i}=e,c=ko(ss,r),d=ls(c.baseId,a),m=cs(c.baseId,a),f=a===c.value,l=t.useRef(f);return t.useEffect(()=>{const p=requestAnimationFrame(()=>l.current=!1);return()=>cancelAnimationFrame(p)},[]),u.jsx(ce,{present:n||f,children:({present:p})=>u.jsx(P.div,{"data-state":f?"active":"inactive","data-orientation":c.orientation,role:"tabpanel","aria-labelledby":d,hidden:!p,id:m,tabIndex:0,...i,ref:o,style:{...e.style,animationDuration:l.current?"0s":void 0},children:p&&s})})});is.displayName=ss;function ls(e,o){return`${e}-trigger-${o}`}function cs(e,o){return`${e}-content-${o}`}var Ym=ts,qm=rs,Xm=ns,Qm=is,ds="Toggle",us=t.forwardRef((e,o)=>{const{pressed:r,defaultPressed:a,onPressedChange:n,...s}=e,[i,c]=ae({prop:r,onChange:n,defaultProp:a??!1,caller:ds});return u.jsx(P.button,{type:"button","aria-pressed":i,"data-state":i?"on":"off","data-disabled":e.disabled?"":void 0,...s,ref:o,onClick:S(e.onClick,()=>{e.disabled||c(!i)})})});us.displayName=ds;var ke="ToggleGroup",[ms,Xg]=X(ke,[Pe]),fs=Pe(),$o=v.forwardRef((e,o)=>{const{type:r,...a}=e;if(r==="single"){const n=a;return u.jsx(Jm,{...n,ref:o})}if(r==="multiple"){const n=a;return u.jsx(ef,{...n,ref:o})}throw new Error(`Missing prop \`type\` expected on \`${ke}\``)});$o.displayName=ke;var[ps,gs]=ms(ke),Jm=v.forwardRef((e,o)=>{const{value:r,defaultValue:a,onValueChange:n=()=>{},...s}=e,[i,c]=ae({prop:r,defaultProp:a??"",onChange:n,caller:ke});return u.jsx(ps,{scope:e.__scopeToggleGroup,type:"single",value:v.useMemo(()=>i?[i]:[],[i]),onItemActivate:c,onItemDeactivate:v.useCallback(()=>c(""),[c]),children:u.jsx(vs,{...s,ref:o})})}),ef=v.forwardRef((e,o)=>{const{value:r,defaultValue:a,onValueChange:n=()=>{},...s}=e,[i,c]=ae({prop:r,defaultProp:a??[],onChange:n,caller:ke}),d=v.useCallback(f=>c((l=[])=>[...l,f]),[c]),m=v.useCallback(f=>c((l=[])=>l.filter(p=>p!==f)),[c]);return u.jsx(ps,{scope:e.__scopeToggleGroup,type:"multiple",value:i,onItemActivate:d,onItemDeactivate:m,children:u.jsx(vs,{...s,ref:o})})});$o.displayName=ke;var[tf,of]=ms(ke),vs=v.forwardRef((e,o)=>{const{__scopeToggleGroup:r,disabled:a=!1,rovingFocus:n=!0,orientation:s,dir:i,loop:c=!0,...d}=e,m=fs(r),f=st(i),l={role:"group",dir:f,...d};return u.jsx(tf,{scope:r,rovingFocus:n,disabled:a,children:n?u.jsx(Pt,{asChild:!0,...m,orientation:s,dir:f,loop:c,children:u.jsx(P.div,{...l,ref:o})}):u.jsx(P.div,{...l,ref:o})})}),xt="ToggleGroupItem",hs=v.forwardRef((e,o)=>{const r=gs(xt,e.__scopeToggleGroup),a=of(xt,e.__scopeToggleGroup),n=fs(e.__scopeToggleGroup),s=r.value.includes(e.value),i=a.disabled||e.disabled,c={...e,pressed:s,disabled:i},d=v.useRef(null);return a.rovingFocus?u.jsx(Tt,{asChild:!0,...n,focusable:!i,active:s,ref:d,children:u.jsx(ar,{...c,ref:o})}):u.jsx(ar,{...c,ref:o})});hs.displayName=xt;var ar=v.forwardRef((e,o)=>{const{__scopeToggleGroup:r,value:a,...n}=e,s=gs(xt,r),i={role:"radio","aria-checked":e.pressed,"aria-pressed":void 0},c=s.type==="single"?i:void 0;return u.jsx(us,{...c,...n,ref:o,onPressedChange:d=>{d?s.onItemActivate(a):s.onItemDeactivate(a)}})}),rf=$o,af=hs;const Cs=od,nf=["1","2","3","4"],bs={...te,align:{type:"enum",className:"rt-r-align",values:["start","center"],default:"center"},size:{type:"enum",className:"rt-r-size",values:nf,default:"3",responsive:!0},width:re.width,minWidth:re.minWidth,maxWidth:{...re.maxWidth,default:"600px"},...wt},ys=e=>t.createElement(wd,{...e});ys.displayName="AlertDialog.Root";const xs=t.forwardRef(({children:e,...o},r)=>t.createElement(Ed,{...o,ref:r,asChild:!0},Ee(e)));xs.displayName="AlertDialog.Trigger";const ws=t.forwardRef(({align:e,...o},r)=>{const{align:a,...n}=bs,{className:s}=I({align:e},{align:a}),{className:i,forceMount:c,container:d,...m}=I(o,n);return t.createElement(Rd,{container:d,forceMount:c},t.createElement(me,{asChild:!0},t.createElement(Nd,{className:"rt-BaseDialogOverlay rt-AlertDialogOverlay"},t.createElement("div",{className:"rt-BaseDialogScroll rt-AlertDialogScroll"},t.createElement("div",{className:`rt-BaseDialogScrollPadding rt-AlertDialogScrollPadding ${s}`},t.createElement(Sd,{...m,ref:r,className:y("rt-BaseDialogContent","rt-AlertDialogContent",i)}))))))});ws.displayName="AlertDialog.Content";const Es=t.forwardRef((e,o)=>t.createElement(Td,{asChild:!0},t.createElement(Et,{size:"5",mb:"3",trim:"start",...e,asChild:!1,ref:o})));Es.displayName="AlertDialog.Title";const Rs=t.forwardRef((e,o)=>t.createElement(Pd,{asChild:!0},t.createElement(K,{as:"p",size:"3",...e,asChild:!1,ref:o})));Rs.displayName="AlertDialog.Description";const Ns=t.forwardRef(({children:e,...o},r)=>t.createElement(Md,{...o,ref:r,asChild:!0},Ee(e)));Ns.displayName="AlertDialog.Action";const Ss=t.forwardRef(({children:e,...o},r)=>t.createElement(_d,{...o,ref:r,asChild:!0},Ee(e)));Ss.displayName="AlertDialog.Cancel";const sf=Object.freeze(Object.defineProperty({__proto__:null,Action:Ns,Cancel:Ss,Content:ws,Description:Rs,Root:ys,Title:Es,Trigger:xs},Symbol.toStringTag,{value:"Module"})),lf=kd,cf=["1","2","3","4","5","6","7","8","9"],df=["solid","soft"],uf={...te,size:{type:"enum",className:"rt-r-size",values:cf,default:"3",responsive:!0},variant:{type:"enum",className:"rt-variant",values:df,default:"soft"},...lo,...he,...Rt,fallback:{type:"ReactNode",required:!0}},Ms=t.forwardRef((e,o)=>{const{asChild:r,children:a,className:n,style:s,color:i,radius:c,...d}=I(e,uf,V);return t.createElement(Vd,{"data-accent-color":i,"data-radius":c,className:y("rt-reset","rt-AvatarRoot",n),style:s,asChild:r},Nr({asChild:r,children:a},t.createElement(_s,{ref:o,...d})))});Ms.displayName="Avatar";const _s=t.forwardRef(({fallback:e,...o},r)=>{const[a,n]=t.useState("idle");return t.createElement(t.Fragment,null,a==="idle"||a==="loading"?t.createElement("span",{className:"rt-AvatarFallback"}):null,a==="error"?t.createElement(zd,{className:y("rt-AvatarFallback",{"rt-one-letter":typeof e=="string"&&e.length===1,"rt-two-letters":typeof e=="string"&&e.length===2}),delayMs:0},e):null,t.createElement(Fd,{ref:r,className:"rt-AvatarImage",...o,onLoadingStatusChange:s=>{o.onLoadingStatusChange?.(s),n(s)}}))});_s.displayName="AvatarImpl";const Ts=t.forwardRef((e,o)=>{const{asChild:r,children:a,className:n,...s}=e,i=r?Ce:"blockquote";return t.createElement(K,{asChild:!0,...s,ref:o,className:y("rt-Blockquote",n)},t.createElement(i,null,a))});Ts.displayName="Blockquote";const mf=["1","2","3"],ff=["soft","surface","outline"],nr={...te,size:{type:"enum",className:"rt-r-size",values:mf,default:"2",responsive:!0},variant:{type:"enum",className:"rt-variant",values:ff,default:"soft"},...lo,...he},Ps=t.createContext({}),Is=t.forwardRef((e,o)=>{const{size:r=nr.size.default}=e,{asChild:a,children:n,className:s,color:i,...c}=I(e,nr,V),d=a?Ce:"div";return t.createElement(d,{"data-accent-color":i,...c,className:y("rt-CalloutRoot",s),ref:o},t.createElement(Ps.Provider,{value:t.useMemo(()=>({size:r}),[r])},n))});Is.displayName="Callout.Root";const ks=t.forwardRef(({className:e,...o},r)=>t.createElement("div",{...o,className:y("rt-CalloutIcon",e),ref:r}));ks.displayName="Callout.Icon";const $s=t.forwardRef(({className:e,...o},r)=>{const{size:a}=t.useContext(Ps);return t.createElement(K,{as:"p",size:zl(a,Gl),...o,asChild:!1,ref:r,className:y("rt-CalloutText",e)})});$s.displayName="Callout.Text";const pf=Object.freeze(Object.defineProperty({__proto__:null,Icon:ks,Root:Is,Text:$s},Symbol.toStringTag,{value:"Module"})),{useDirection:gf}=bc,Do="CheckboxGroup",[vf,Vt]=X(Do,[Pe,Sr]),Ds=Pe(),As=Sr(),[hf,Cf]=vf(Do),Os=t.forwardRef((e,o)=>{const{__scopeCheckboxGroup:r,name:a,defaultValue:n,value:s,required:i=!1,disabled:c=!1,orientation:d,dir:m,loop:f=!0,onValueChange:l,...p}=e,h=Ds(r),g=gf(m),[b=[],C]=ae({prop:s,defaultProp:n,onChange:l}),R=t.useCallback(E=>C((N=[])=>[...N,E]),[C]),j=t.useCallback(E=>C((N=[])=>N.filter(M=>M!==E)),[C]);return t.createElement(hf,{scope:r,name:a,required:i,disabled:c,value:b,onItemCheck:R,onItemUncheck:j},t.createElement(Pt,{asChild:!0,...h,orientation:d,dir:g,loop:f},t.createElement(Gc.div,{role:"group","data-disabled":c?"":void 0,dir:g,...p,ref:o})))});Os.displayName=Do;const js="CheckboxGroupItem",Ls=t.forwardRef((e,o)=>{const{__scopeCheckboxGroup:r,disabled:a,...n}=e,s=Cf(js,r),i=s.disabled||a,c=Ds(r),d=As(r),m=t.useRef(null),f=B(o,m),l=s.value?.includes(n.value);return t.createElement(Tt,{asChild:!0,...c,focusable:!i,active:l},t.createElement(Bc,{name:s.name,disabled:i,required:s.required,checked:l,...d,...n,ref:f,onCheckedChange:p=>{p?s.onItemCheck(e.value):s.onItemUncheck(e.value)}}))});Ls.displayName=js;const bf="CheckboxGroupIndicator",Bs=t.forwardRef((e,o)=>{const{__scopeCheckboxGroup:r,...a}=e,n=As(r);return t.createElement(Hc,{...n,...a,ref:o})});Bs.displayName=bf;const Hs=Os,Vs=Ls,Fs=Bs,yf=["1","2","3"],xf=["surface","classic"],wf={...te,size:{type:"enum",className:"rt-r-size",values:yf,default:"2",responsive:!0},variant:{type:"enum",className:"rt-variant",values:xf,default:"surface"},...Te,...he,columns:{...vt.columns,default:"repeat(auto-fit, minmax(200px, 1fr))"},gap:{...vt.gap,default:"4"}},zs="CheckboxCards",[Ef]=X(zs,[Vt]),Gs=Vt(),[Rf,Nf]=Ef(zs),Ks=t.forwardRef((e,o)=>{const{__scopeCheckboxCards:r,className:a,color:n,...s}=I(e,wf,V),i=Gs(r);return t.createElement(Rf,{scope:r,size:e.size,highContrast:e.highContrast},t.createElement(Ne,{asChild:!0},t.createElement(Hs,{...i,"data-accent-color":n,...s,ref:o,className:y("rt-CheckboxCardsRoot",a)})))});Ks.displayName="CheckboxCards.Root";const Ws=t.forwardRef(({__scopeCheckboxCards:e,children:o,className:r,style:a,...n},s)=>{const i=Nf("CheckboxCardsItem",e),c=Gs(e),{className:d}=I({size:i?.size,variant:"surface",highContrast:i?.highContrast},Mr);return t.createElement("label",{className:y("rt-BaseCard","rt-CheckboxCardsItem",r),style:a},o,t.createElement(Vs,{...c,...n,ref:s,className:y("rt-reset","rt-BaseCheckboxRoot","rt-CheckboxCardCheckbox",d)},t.createElement(Fs,{...c,asChild:!0,className:"rt-BaseCheckboxIndicator"},t.createElement(it,null))))});Ws.displayName="CheckboxCards.Item";const Sf=Object.freeze(Object.defineProperty({__proto__:null,Item:Ws,Root:Ks},Symbol.toStringTag,{value:"Module"})),ot={...te,...Mr},Us="CheckboxGroup",[Mf]=X(Us,[Vt]),Zs=Vt(),[_f,Ys]=Mf(Us),qs=t.forwardRef(({color:e=ot.color.default,highContrast:o=ot.highContrast.default,size:r=ot.size.default,variant:a=ot.variant.default,...n},s)=>{const{__scopeCheckboxGroup:i,className:c,...d}=I(n,V),m=Zs(i);return t.createElement(_f,{scope:i,color:e,size:r,highContrast:o,variant:a},t.createElement(Hs,{...m,...d,ref:s,className:y("rt-CheckboxGroupRoot",c)}))});qs.displayName="CheckboxGroup.Root";const Xs=t.forwardRef((e,o)=>{const{__scopeCheckboxGroup:r,children:a,className:n,style:s,...i}=e,{size:c}=Ys("CheckboxGroupItem",r);return a?t.createElement(K,{as:"label",size:c,className:y("rt-CheckboxGroupItem",n),style:s},t.createElement(ao,{__scopeCheckboxGroup:r,...i,ref:o}),a&&t.createElement("span",{className:"rt-CheckboxGroupItemInner"},a)):t.createElement(ao,{__scopeCheckboxGroup:r,...i,ref:o,className:n,style:s})});Xs.displayName="CheckboxGroup.Item";const ao=t.forwardRef(({__scopeCheckboxGroup:e,...o},r)=>{const a=Ys("CheckboxGroupItemCheckbox",e),n=Zs(e),{color:s,className:i}=I({...o,...a},ot,V);return t.createElement(Vs,{...n,"data-accent-color":s,...o,ref:r,className:y("rt-reset","rt-BaseCheckboxRoot","rt-CheckboxGroupItemCheckbox",i)},t.createElement(Fs,{...n,asChild:!0,className:"rt-BaseCheckboxIndicator"},t.createElement(it,null)))});ao.displayName="CheckboxGroup.ItemCheckbox";const Tf=Object.freeze(Object.defineProperty({__proto__:null,Item:Xs,Root:qs},Symbol.toStringTag,{value:"Module"})),Pf=["1","2","3","4","5","6","7","8","9"],If=["solid","soft","outline","ghost"],kf={...te,size:{type:"enum",className:"rt-r-size",values:Pf,responsive:!0},variant:{type:"enum",className:"rt-variant",values:If,default:"soft"},...Jl,...lo,...he,...St,...Nt},Qs=t.forwardRef((e,o)=>{const{asChild:r,className:a,color:n,...s}=I(e,kf,V),i=e.variant==="ghost"?n||void 0:n,c=r?Ce:"code";return t.createElement(c,{"data-accent-color":i,...s,ref:o,className:y("rt-reset","rt-Code",a)})});Qs.displayName="Code";const Js=e=>t.createElement(nu,{...e});Js.displayName="ContextMenu.Root";const ei=t.forwardRef(({children:e,...o},r)=>t.createElement(su,{...o,ref:r,asChild:!0},Ee(e)));ei.displayName="ContextMenu.Trigger";const ti=t.createContext({}),oi=t.forwardRef((e,o)=>{const r=go(),{size:a=tt.size.default,variant:n=tt.variant.default,highContrast:s=tt.highContrast.default}=e,{className:i,children:c,color:d,container:m,forceMount:f,...l}=I(e,tt),p=d||r.accentColor;return t.createElement(Ha,{container:m,forceMount:f},t.createElement(me,{asChild:!0},t.createElement(iu,{"data-accent-color":p,alignOffset:-Number(a)*4,collisionPadding:10,...l,asChild:!1,ref:o,className:y("rt-PopperContent","rt-BaseMenuContent","rt-ContextMenuContent",i)},t.createElement(lt,{type:"auto"},t.createElement("div",{className:y("rt-BaseMenuViewport","rt-ContextMenuViewport")},t.createElement(ti.Provider,{value:t.useMemo(()=>({size:a,variant:n,color:p,highContrast:s}),[a,n,p,s])},c))))))});oi.displayName="ContextMenu.Content";const ri=t.forwardRef(({className:e,...o},r)=>t.createElement(cu,{...o,asChild:!1,ref:r,className:y("rt-BaseMenuLabel","rt-ContextMenuLabel",e)}));ri.displayName="ContextMenu.Label";const ai=t.forwardRef((e,o)=>{const{className:r,children:a,color:n=xc.color.default,shortcut:s,...i}=e;return t.createElement(du,{"data-accent-color":n,...i,ref:o,className:y("rt-reset","rt-BaseMenuItem","rt-ContextMenuItem",r)},t.createElement(co,null,a),s&&t.createElement("div",{className:"rt-BaseMenuShortcut rt-ContextMenuShortcut"},s))});ai.displayName="ContextMenu.Item";const ni=t.forwardRef(({className:e,...o},r)=>t.createElement(lu,{...o,asChild:!1,ref:r,className:y("rt-BaseMenuGroup","rt-ContextMenuGroup",e)}));ni.displayName="ContextMenu.Group";const si=t.forwardRef(({className:e,...o},r)=>t.createElement(mu,{...o,asChild:!1,ref:r,className:y("rt-BaseMenuRadioGroup","rt-ContextMenuRadioGroup",e)}));si.displayName="ContextMenu.RadioGroup";const ii=t.forwardRef((e,o)=>{const{children:r,className:a,color:n=wc.color.default,...s}=e;return t.createElement(fu,{...s,asChild:!1,ref:o,"data-accent-color":n,className:y("rt-BaseMenuItem","rt-BaseMenuRadioItem","rt-ContextMenuItem","rt-ContextMenuRadioItem",a)},t.createElement(co,null,r),t.createElement(Va,{className:"rt-BaseMenuItemIndicator rt-ContextMenuItemIndicator"},t.createElement(it,{className:"rt-BaseMenuItemIndicatorIcon rt-ContextMenuItemIndicatorIcon"})))});ii.displayName="ContextMenu.RadioItem";const li=t.forwardRef((e,o)=>{const{children:r,className:a,shortcut:n,color:s=yc.color.default,...i}=e;return t.createElement(uu,{...i,asChild:!1,ref:o,"data-accent-color":s,className:y("rt-BaseMenuItem","rt-BaseMenuCheckboxItem","rt-ContextMenuItem","rt-ContextMenuCheckboxItem",a)},t.createElement(co,null,r),t.createElement(Va,{className:"rt-BaseMenuItemIndicator rt-ContextMenuItemIndicator"},t.createElement(it,{className:"rt-BaseMenuItemIndicatorIcon rt-ContextMenuItemIndicatorIcon"})),n&&t.createElement("div",{className:"rt-BaseMenuShortcut rt-ContextMenuShortcut"},n))});li.displayName="ContextMenu.CheckboxItem";const ci=e=>t.createElement(gu,{...e});ci.displayName="ContextMenu.Sub";const di=t.forwardRef((e,o)=>{const{className:r,children:a,...n}=e;return t.createElement(vu,{...n,asChild:!1,ref:o,className:y("rt-BaseMenuItem","rt-BaseMenuSubTrigger","rt-ContextMenuItem","rt-ContextMenuSubTrigger",r)},a,t.createElement("div",{className:"rt-BaseMenuShortcut rt-ContextMenuShortcut"},t.createElement(Rr,{className:"rt-BaseMenuSubTriggerIcon rt-ContextMenuSubTriggerIcon"})))});di.displayName="ContextMenu.SubTrigger";const ui=t.forwardRef((e,o)=>{const{size:r,variant:a,color:n,highContrast:s}=t.useContext(ti),{className:i,children:c,container:d,forceMount:m,...f}=I({size:r,variant:a,color:n,highContrast:s,...e},tt);return t.createElement(Ha,{container:d,forceMount:m},t.createElement(me,{asChild:!0},t.createElement(hu,{"data-accent-color":n,alignOffset:-Number(r)*4,sideOffset:1,collisionPadding:10,...f,asChild:!1,ref:o,className:y("rt-PopperContent","rt-BaseMenuContent","rt-BaseMenuSubContent","rt-ContextMenuContent","rt-ContextMenuSubContent",i)},t.createElement(lt,{type:"auto"},t.createElement("div",{className:y("rt-BaseMenuViewport","rt-ContextMenuViewport")},c)))))});ui.displayName="ContextMenu.SubContent";const mi=t.forwardRef(({className:e,...o},r)=>t.createElement(pu,{...o,asChild:!1,ref:r,className:y("rt-BaseMenuSeparator","rt-ContextMenuSeparator",e)}));mi.displayName="ContextMenu.Separator";const $f=Object.freeze(Object.defineProperty({__proto__:null,CheckboxItem:li,Content:oi,Group:ni,Item:ai,Label:ri,RadioGroup:si,RadioItem:ii,Root:Js,Separator:mi,Sub:ci,SubContent:ui,SubTrigger:di,Trigger:ei},Symbol.toStringTag,{value:"Module"})),Df=["start","center","end","baseline","stretch"],Af=["horizontal","vertical"],Of=["1","2","3"],jf={orientation:{type:"enum",className:"rt-r-orientation",values:Af,default:"horizontal",responsive:!0},size:{type:"enum",className:"rt-r-size",values:Of,default:"2",responsive:!0},trim:{...ec.trim,className:"rt-r-trim"}},Lf={align:{type:"enum",className:"rt-r-ai",values:Df,responsive:!0}},Bf={...re,...Te,...he},fi=t.forwardRef((e,o)=>{const{className:r,...a}=I(e,jf,V);return t.createElement(K,{asChild:!0},t.createElement("dl",{...a,ref:o,className:y("rt-DataListRoot",r)}))});fi.displayName="DataList.Root";const pi=t.forwardRef((e,o)=>{const{className:r,...a}=I(e,Lf);return t.createElement("div",{...a,ref:o,className:y("rt-DataListItem",r)})});pi.displayName="DataList.Item";const gi=t.forwardRef((e,o)=>{const{className:r,color:a,...n}=I(e,Bf);return t.createElement("dt",{...n,"data-accent-color":a,ref:o,className:y("rt-DataListLabel",r)})});gi.displayName="DataList.Label";const vi=t.forwardRef(({children:e,className:o,...r},a)=>t.createElement("dd",{...r,ref:a,className:y(o,"rt-DataListValue")},e));vi.displayName="DataList.Value";const Hf=Object.freeze(Object.defineProperty({__proto__:null,Item:pi,Label:gi,Root:fi,Value:vi},Symbol.toStringTag,{value:"Module"})),hi=e=>t.createElement(Zr,{...e,modal:!0});hi.displayName="Dialog.Root";const Ci=t.forwardRef(({children:e,...o},r)=>t.createElement(Yr,{...o,ref:r,asChild:!0},Ee(e)));Ci.displayName="Dialog.Trigger";const bi=t.forwardRef(({align:e,...o},r)=>{const{align:a,...n}=bs,{className:s}=I({align:e},{align:a}),{className:i,forceMount:c,container:d,...m}=I(o,n);return t.createElement(qr,{container:d,forceMount:c},t.createElement(me,{asChild:!0},t.createElement(Xr,{className:"rt-BaseDialogOverlay rt-DialogOverlay"},t.createElement("div",{className:"rt-BaseDialogScroll rt-DialogScroll"},t.createElement("div",{className:`rt-BaseDialogScrollPadding rt-DialogScrollPadding ${s}`},t.createElement(Qr,{...m,ref:r,className:y("rt-BaseDialogContent","rt-DialogContent",i)}))))))});bi.displayName="Dialog.Content";const yi=t.forwardRef((e,o)=>t.createElement(Jr,{asChild:!0},t.createElement(Et,{size:"5",mb:"3",trim:"start",...e,asChild:!1,ref:o})));yi.displayName="Dialog.Title";const xi=t.forwardRef((e,o)=>t.createElement(ea,{asChild:!0},t.createElement(K,{as:"p",size:"3",...e,asChild:!1,ref:o})));xi.displayName="Dialog.Description";const wi=t.forwardRef(({children:e,...o},r)=>t.createElement(bo,{...o,ref:r,asChild:!0},Ee(e)));wi.displayName="Dialog.Close";const Vf=Object.freeze(Object.defineProperty({__proto__:null,Close:wi,Content:bi,Description:xi,Root:hi,Title:yi,Trigger:Ci},Symbol.toStringTag,{value:"Module"})),Ff={...te,...St,...Nt},Ei=t.forwardRef((e,o)=>{const{asChild:r,className:a,...n}=I(e,Ff),s=r?Ce:"em";return t.createElement(s,{...n,ref:o,className:y("rt-Em",a)})});Ei.displayName="Em";const zf=["1","2","3"],Gf={...te,size:{type:"enum",className:"rt-r-size",values:zf,default:"2",responsive:!0},width:re.width,minWidth:re.minWidth,maxWidth:{...re.maxWidth,default:"480px"},...wt},Ri=e=>t.createElement(Nu,{closeDelay:150,openDelay:200,...e});Ri.displayName="HoverCard.Root";const Ni=t.forwardRef(({children:e,className:o,...r},a)=>t.createElement(Su,{ref:a,className:y("rt-HoverCardTrigger",o),...r,asChild:!0},Ee(e)));Ni.displayName="HoverCard.Trigger";const Si=t.forwardRef((e,o)=>{const{className:r,forceMount:a,container:n,...s}=I(e,Gf);return t.createElement(Mu,{container:n,forceMount:a},t.createElement(me,{asChild:!0},t.createElement(_u,{align:"start",sideOffset:8,collisionPadding:10,...s,ref:o,className:y("rt-PopperContent","rt-HoverCardContent",r)})))});Si.displayName="HoverCard.Content";const Kf=Object.freeze(Object.defineProperty({__proto__:null,Content:Si,Root:Ri,Trigger:Ni},Symbol.toStringTag,{value:"Module"})),Ao=t.forwardRef(({className:e,...o},r)=>t.createElement(Kl,{...o,ref:r,className:y("rt-IconButton",e)}));Ao.displayName="IconButton";const Wf=["all","x","y","top","bottom","left","right"],Uf=["border-box","padding-box"],Ae=["current","0"],Zf={...te,side:{type:"enum",className:"rt-r-side",values:Wf,default:"all",responsive:!0},clip:{type:"enum",className:"rt-r-clip",values:Uf,default:"border-box",responsive:!0},p:{type:"enum",className:"rt-r-p",values:Ae,parseValue:Oe,responsive:!0},px:{type:"enum",className:"rt-r-px",values:Ae,parseValue:Oe,responsive:!0},py:{type:"enum",className:"rt-r-py",values:Ae,parseValue:Oe,responsive:!0},pt:{type:"enum",className:"rt-r-pt",values:Ae,parseValue:Oe,responsive:!0},pr:{type:"enum",className:"rt-r-pr",values:Ae,parseValue:Oe,responsive:!0},pb:{type:"enum",className:"rt-r-pb",values:Ae,parseValue:Oe,responsive:!0},pl:{type:"enum",className:"rt-r-pl",values:Ae,parseValue:Oe,responsive:!0}};function Oe(e){return e==="current"?"inset":e}const Mi=t.forwardRef((e,o)=>{const{asChild:r,className:a,...n}=I(e,Zf,V),s=r?Ce:"div";return t.createElement(s,{...n,ref:o,className:y("rt-Inset",a)})});Mi.displayName="Inset";const Yf=["1","2","3","4","5","6","7","8","9"],qf={...te,size:{type:"enum",className:"rt-r-size",values:Yf,responsive:!0}},Oo=t.forwardRef((e,o)=>{const{asChild:r,className:a,...n}=I(e,qf,V),s=r?Ce:"kbd";return t.createElement(s,{...n,ref:o,className:y("rt-reset","rt-Kbd",a)})});Oo.displayName="Kbd";const Xf=["1","2","3","4"],Qf={...te,size:{type:"enum",className:"rt-r-size",values:Xf,default:"2",responsive:!0},width:re.width,minWidth:re.minWidth,maxWidth:{...re.maxWidth,default:"480px"},...wt},jo=e=>t.createElement(sm,{...e});jo.displayName="Popover.Root";const Lo=t.forwardRef(({children:e,...o},r)=>t.createElement(lm,{...o,ref:r,asChild:!0},Ee(e)));Lo.displayName="Popover.Trigger";const Bo=t.forwardRef((e,o)=>{const{className:r,forceMount:a,container:n,...s}=I(e,Qf);return t.createElement(cm,{container:n,forceMount:a},t.createElement(me,{asChild:!0},t.createElement(dm,{align:"start",sideOffset:8,collisionPadding:10,...s,ref:o,className:y("rt-PopperContent","rt-PopoverContent",r)})))});Bo.displayName="Popover.Content";const _i=t.forwardRef(({children:e,...o},r)=>t.createElement(um,{...o,ref:r,asChild:!0},Ee(e)));_i.displayName="Popover.Close";const Ti=t.forwardRef(({children:e,...o},r)=>t.createElement(im,{...o,ref:r}));Ti.displayName="Popover.Anchor";const Jf=Object.freeze(Object.defineProperty({__proto__:null,Anchor:Ti,Close:_i,Content:Bo,Root:jo,Trigger:Lo},Symbol.toStringTag,{value:"Module"})),ep=Ec,tp={...te,...St,...Nt},Pi=t.forwardRef((e,o)=>{const{asChild:r,className:a,...n}=I(e,tp),s=r?Ce:"q";return t.createElement(s,{...n,ref:o,className:y("rt-Quote",a)})});Pi.displayName="Quote";const op=["1","2","3"],rp=["surface","classic"],ap={...te,size:{type:"enum",className:"rt-r-size",values:op,default:"2",responsive:!0},variant:{type:"enum",className:"rt-variant",values:rp,default:"surface"},...Te,...he,columns:{...vt.columns,default:"repeat(auto-fit, minmax(160px, 1fr))"},gap:{...vt.gap,default:"4"}},Ii=t.forwardRef((e,o)=>{const{className:r,color:a,...n}=I(e,ap,V);return t.createElement(Ne,{asChild:!0},t.createElement($n,{"data-accent-color":a,...n,ref:o,className:y("rt-RadioCardsRoot",r)}))});Ii.displayName="RadioCards.Root";const ki=t.forwardRef(({className:e,...o},r)=>t.createElement(Dn,{...o,asChild:!1,ref:r,className:y("rt-reset","rt-BaseCard","rt-RadioCardsItem",e)}));ki.displayName="RadioCards.Item";const np=Object.freeze(Object.defineProperty({__proto__:null,Item:ki,Root:Ii},Symbol.toStringTag,{value:"Module"})),sp=["1","2","3"],ip=["classic","surface","soft"],rt={...te,size:{type:"enum",className:"rt-r-size",values:sp,default:"2",responsive:!0},variant:{type:"enum",className:"rt-variant",values:ip,default:"surface"},...Te,...he},$i="RadioGroup",[lp]=X($i,[Mn]),Di=Mn(),[cp,Ai]=lp($i),Oi=t.forwardRef(({color:e=rt.color.default,highContrast:o=rt.highContrast.default,size:r=rt.size.default,variant:a=rt.variant.default,...n},s)=>{const{__scopeRadioGroup:i,className:c,...d}=I(n,V),m=Di(i);return t.createElement(cp,{scope:i,color:e,highContrast:o,size:r,variant:a},t.createElement($n,{...m,...d,ref:s,className:y("rt-RadioGroupRoot",c)}))});Oi.displayName="RadioGroup.Root";const ji=t.forwardRef((e,o)=>{const{__scopeRadioGroup:r,children:a,className:n,style:s,...i}=e,{size:c}=Ai("RadioGroupItem",r);return a?t.createElement(K,{as:"label",size:c,className:y("rt-RadioGroupItem",n),style:s},t.createElement(no,{__scopeRadioGroup:r,...i,ref:o}),a&&t.createElement("span",{className:"rt-RadioGroupItemInner"},a)):t.createElement(no,{__scopeRadioGroup:r,...i,ref:o,className:n,style:s})});ji.displayName="RadioGroup.Item";const no=t.forwardRef(({__scopeRadioGroup:e,...o},r)=>{const a=Ai("RadioGroupItemRadio",e),n=Di(e),{color:s,className:i}=I({...o,...a},rt,V);return t.createElement(Dn,{...n,"data-accent-color":s,...o,asChild:!1,ref:r,className:y("rt-reset","rt-BaseRadioRoot",i)})});no.displayName="RadioGroup.ItemRadio";const dp=Object.freeze(Object.defineProperty({__proto__:null,Item:ji,Root:Oi},Symbol.toStringTag,{value:"Module"})),up=["1","2","3"],mp=["classic","surface","soft"],fp={size:{type:"enum",className:"rt-r-size",values:up,default:"2",responsive:!0},variant:{type:"enum",className:"rt-variant",values:mp,default:"surface"},...Te,...he},Li=t.forwardRef((e,o)=>{const r=t.useRef(null),{className:a,color:n,onChange:s,onValueChange:i,...c}=I(e,fp,V);return t.createElement("input",{type:"radio","data-accent-color":n,...c,onChange:S(s,d=>i?.(d.currentTarget.value)),ref:pr(r,o),className:y("rt-reset","rt-BaseRadioRoot","rt-RadioRoot",a)})});Li.displayName="Radio";const Bi=t.forwardRef(({className:e,children:o,...r},a)=>t.createElement(Ce,{...r,ref:a,className:y("rt-reset",e)},Ee(o)));Bi.displayName="Reset";const pp=["1","2","3"],gp=["surface","classic"],vp={disabled:{type:"boolean",className:"disabled",default:!1},size:{type:"enum",className:"rt-r-size",values:pp,default:"2",responsive:!0},variant:{type:"enum",className:"rt-variant",values:gp,default:"surface"},...Rt},Hi=t.forwardRef((e,o)=>{const{className:r,children:a,radius:n,value:s,defaultValue:i,onValueChange:c,...d}=I(e,vp,V),[m,f]=ae({prop:s,onChange:c,defaultProp:i});return t.createElement(rf,{"data-disabled":e.disabled||void 0,"data-radius":n,ref:o,className:y("rt-SegmentedControlRoot",r),onValueChange:l=>{l&&f(l)},...d,type:"single",value:m,asChild:!1,disabled:!!e.disabled},a,t.createElement("div",{className:"rt-SegmentedControlIndicator"}))});Hi.displayName="SegmentedControl.Root";const Vi=t.forwardRef(({children:e,className:o,...r},a)=>t.createElement(af,{ref:a,className:y("rt-reset","rt-SegmentedControlItem",o),...r,disabled:!1,asChild:!1},t.createElement("span",{className:"rt-SegmentedControlItemSeparator"}),t.createElement("span",{className:"rt-SegmentedControlItemLabel"},t.createElement("span",{className:"rt-SegmentedControlItemLabelActive"},e),t.createElement("span",{className:"rt-SegmentedControlItemLabelInactive","aria-hidden":!0},e))));Vi.displayName="SegmentedControl.Item";const hp=Object.freeze(Object.defineProperty({__proto__:null,Item:Vi,Root:Hi},Symbol.toStringTag,{value:"Module"})),Cp=["1","2","3","4"],bp=["none","initial"],yp={...te,size:{type:"enum",className:"rt-r-size",values:Cp,default:"3",responsive:!0},display:{type:"enum",className:"rt-r-display",values:bp,parseValue:xp,responsive:!0}};function xp(e){return e==="initial"?"block":e}const Fi=t.forwardRef((e,o)=>{const{asChild:r,className:a,...n}=I(e,yp,Wl,V),s=r?Ce:"section";return t.createElement(s,{...n,ref:o,className:y("rt-Section",a)})});Fi.displayName="Section";const zi=parseFloat(t.version)>=19||"",wp={loading:{type:"boolean",default:!0},...re,...wt},Gi=t.forwardRef((e,o)=>{const{children:r,className:a,loading:n,...s}=I(e,wp,V);if(!n)return r;const i=t.isValidElement(r)?Ce:"span";return t.createElement(i,{ref:o,"aria-hidden":!0,className:y("rt-Skeleton",a),"data-inline-skeleton":t.isValidElement(r)?void 0:!0,tabIndex:-1,inert:zi,...s},r)});Gi.displayName="Skeleton";const Ep=["1","2","3"],Rp=["classic","surface","soft"],Np={size:{type:"enum",className:"rt-r-size",values:Ep,default:"2",responsive:!0},variant:{type:"enum",className:"rt-variant",values:Rp,default:"surface"},...Te,...he,...Rt},Ki=t.forwardRef((e,o)=>{const{className:r,color:a,radius:n,tabIndex:s,...i}=I(e,Np,V);return t.createElement(jm,{"data-accent-color":a,"data-radius":n,ref:o,...i,asChild:!1,className:y("rt-SliderRoot",r)},t.createElement(Lm,{className:"rt-SliderTrack"},t.createElement(Bm,{className:y("rt-SliderRange",{"rt-high-contrast":e.highContrast}),"data-inverted":i.inverted?"":void 0})),(i.value??i.defaultValue??[]).map((c,d)=>t.createElement(Hm,{key:d,className:"rt-SliderThumb",...s!==void 0?{tabIndex:s}:void 0})))});Ki.displayName="Slider";const Sp={...te,...St,...Nt},Wi=t.forwardRef((e,o)=>{const{asChild:r,className:a,...n}=I(e,Sp),s=r?Ce:"strong";return t.createElement(s,{...n,ref:o,className:y("rt-Strong",a)})});Wi.displayName="Strong";const Mp=["1","2","3"],_p=["classic","surface","soft"],Tp={size:{type:"enum",className:"rt-r-size",values:Mp,default:"2",responsive:!0},variant:{type:"enum",className:"rt-variant",values:_p,default:"surface"},...Te,...he,...Rt},Ui=t.forwardRef((e,o)=>{const{className:r,color:a,radius:n,...s}=I(e,Tp,V);return t.createElement(Km,{"data-accent-color":a,"data-radius":n,...s,asChild:!1,ref:o,className:y("rt-reset","rt-SwitchRoot",r)},t.createElement(Wm,{className:y("rt-SwitchThumb",{"rt-high-contrast":e.highContrast})}))});Ui.displayName="Switch";const Pp=["1","2"],Ip=["nowrap","wrap","wrap-reverse"],kp=["start","center","end"],Zi={size:{type:"enum",className:"rt-r-size",values:Pp,default:"2",responsive:!0},wrap:{type:"enum",className:"rt-r-fw",values:Ip,responsive:!0},justify:{type:"enum",className:"rt-r-jc",values:kp,responsive:!0},...Te,...he},Yi=t.forwardRef((e,o)=>{const{children:r,className:a,color:n,...s}=I(e,Zi,V);return t.createElement(Zu,{className:"rt-TabNavRoot","data-accent-color":n,...s,asChild:!1,ref:o},t.createElement(Yu,{className:y("rt-reset","rt-BaseTabList","rt-TabNavList",a)},r))});Yi.displayName="TabNav.Root";const qi=t.forwardRef((e,o)=>{const{asChild:r,children:a,className:n,...s}=e;return t.createElement(qu,{className:"rt-TabNavItem"},t.createElement(Xu,{...s,ref:o,className:y("rt-reset","rt-BaseTabListTrigger","rt-TabNavLink",n),onSelect:void 0,asChild:r},Nr({asChild:r,children:a},i=>t.createElement(t.Fragment,null,t.createElement("span",{className:"rt-BaseTabListTriggerInner rt-TabNavLinkInner"},i),t.createElement("span",{className:"rt-BaseTabListTriggerInnerHidden rt-TabNavLinkInnerHidden"},i)))))});qi.displayName="TabNav.Link";const $p=Object.freeze(Object.defineProperty({__proto__:null,Link:qi,Root:Yi},Symbol.toStringTag,{value:"Module"})),Dp=["1","2","3"],Ap=["surface","ghost"],Op=["auto","fixed"],Yt={size:{type:"enum",className:"rt-r-size",values:Dp,default:"2",responsive:!0},variant:{type:"enum",className:"rt-variant",values:Ap,default:"ghost"},layout:{type:"enum",className:"rt-r-tl",values:Op,responsive:!0}},jp=["start","center","end","baseline"],Lp={align:{type:"enum",className:"rt-r-va",values:jp,parseValue:Bp,responsive:!0}};function Bp(e){return{baseline:"baseline",start:"top",center:"middle",end:"bottom"}[e]}const Hp=["start","center","end"],Ho={justify:{type:"enum",className:"rt-r-ta",values:Hp,parseValue:Vp,responsive:!0},...re,...Ul};function Vp(e){return{start:"left",center:"center",end:"right"}[e]}const Xi=t.forwardRef((e,o)=>{const{layout:r,...a}=Yt,{className:n,children:s,layout:i,...c}=I(e,a,V),d=tc({value:i,className:Yt.layout.className,propValues:Yt.layout.values});return t.createElement("div",{ref:o,className:y("rt-TableRoot",n),...c},t.createElement(lt,null,t.createElement("table",{className:y("rt-TableRootTable",d)},s)))});Xi.displayName="Table.Root";const Qi=t.forwardRef(({className:e,...o},r)=>t.createElement("thead",{...o,ref:r,className:y("rt-TableHeader",e)}));Qi.displayName="Table.Header";const Ji=t.forwardRef(({className:e,...o},r)=>t.createElement("tbody",{...o,ref:r,className:y("rt-TableBody",e)}));Ji.displayName="Table.Body";const el=t.forwardRef((e,o)=>{const{className:r,...a}=I(e,Lp);return t.createElement("tr",{...a,ref:o,className:y("rt-TableRow",r)})});el.displayName="Table.Row";const tl=t.forwardRef((e,o)=>{const{className:r,...a}=I(e,Ho);return t.createElement("td",{className:y("rt-TableCell",r),ref:o,...a})});tl.displayName="Table.Cell";const ol=t.forwardRef((e,o)=>{const{className:r,...a}=I(e,Ho);return t.createElement("th",{className:y("rt-TableCell","rt-TableColumnHeaderCell",r),scope:"col",ref:o,...a})});ol.displayName="Table.ColumnHeaderCell";const rl=t.forwardRef((e,o)=>{const{className:r,...a}=I(e,Ho);return t.createElement("th",{className:y("rt-TableCell","rt-TableRowHeaderCell",r),scope:"row",ref:o,...a})});rl.displayName="Table.RowHeaderCell";const Fp=Object.freeze(Object.defineProperty({__proto__:null,Body:Ji,Cell:tl,ColumnHeaderCell:ol,Header:Qi,Root:Xi,Row:el,RowHeaderCell:rl},Symbol.toStringTag,{value:"Module"})),al=t.forwardRef((e,o)=>{const{className:r,...a}=I(e,V);return t.createElement(Ym,{...a,ref:o,className:y("rt-TabsRoot",r)})});al.displayName="Tabs.Root";const nl=t.forwardRef((e,o)=>{const{className:r,color:a,...n}=I(e,Zi,V);return t.createElement(qm,{"data-accent-color":a,...n,asChild:!1,ref:o,className:y("rt-BaseTabList","rt-TabsList",r)})});nl.displayName="Tabs.List";const sl=t.forwardRef((e,o)=>{const{className:r,children:a,...n}=e;return t.createElement(Xm,{...n,asChild:!1,ref:o,className:y("rt-reset","rt-BaseTabListTrigger","rt-TabsTrigger",r)},t.createElement("span",{className:"rt-BaseTabListTriggerInner rt-TabsTriggerInner"},a),t.createElement("span",{className:"rt-BaseTabListTriggerInnerHidden rt-TabsTriggerInnerHidden"},a))});sl.displayName="Tabs.Trigger";const il=t.forwardRef((e,o)=>{const{className:r,...a}=I(e,V);return t.createElement(Qm,{...a,ref:o,className:y("rt-TabsContent",r)})});il.displayName="Tabs.Content";const zp=Object.freeze(Object.defineProperty({__proto__:null,Content:il,List:nl,Root:al,Trigger:sl},Symbol.toStringTag,{value:"Module"})),ll=t.forwardRef(({defaultOpen:e=!0,...o},r)=>{const[a,n]=t.useState(e);return t.createElement(cl,{...o,ref:r,open:a,onOpenChange:n})});ll.displayName="ThemePanel";const cl=t.forwardRef((e,o)=>{const{open:r,onOpenChange:a,onAppearanceChange:n,...s}=e,i=go(),{appearance:c,onAppearanceChange:d,accentColor:m,onAccentColorChange:f,grayColor:l,onGrayColorChange:p,panelBackground:h,onPanelBackgroundChange:g,radius:b,onRadiusChange:C,scaling:R,onScalingChange:j}=i,E=n!==void 0,N=we(n),M=t.useCallback(w=>{const _=Gp();if(c!=="inherit"){d(w);return}E?N(w):(Y(w),Kp(w)),_()},[c,d,E,N]),$=Rc(m),U=l==="auto"?$:l,[Q,O]=t.useState("idle");async function W(){const w={appearance:c===ve.appearance.default?void 0:c,accentColor:m===ve.accentColor.default?void 0:m,grayColor:l===ve.grayColor.default?void 0:l,panelBackground:h===ve.panelBackground.default?void 0:h,radius:b===ve.radius.default?void 0:b,scaling:R===ve.scaling.default?void 0:R},_=Object.keys(w).filter(A=>w[A]!==void 0).map(A=>`${A}="${w[A]}"`).join(" "),T=_?`<Theme ${_}>`:"<Theme>";O("copying"),await navigator.clipboard.writeText(T),O("copied"),setTimeout(()=>O("idle"),2e3)}const[D,Y]=t.useState(c==="inherit"?null:c),ne=`
      [contenteditable],
      [role="combobox"],
      [role="listbox"],
      [role="menu"],
      input:not([type="radio"], [type="checkbox"]),
      select,
      textarea
    `;return t.useEffect(()=>{function w(_){const T=_.altKey||_.ctrlKey||_.shiftKey||_.metaKey,A=document.activeElement?.closest(ne);_.key?.toUpperCase()==="T"&&!T&&!A&&a(!r)}return document.addEventListener("keydown",w),()=>document.removeEventListener("keydown",w)},[a,r,ne]),t.useEffect(()=>{function w(_){const T=_.altKey||_.ctrlKey||_.shiftKey||_.metaKey,A=document.activeElement?.closest(ne);_.key?.toUpperCase()==="D"&&!T&&!A&&M(D==="light"?"dark":"light")}return document.addEventListener("keydown",w),()=>document.removeEventListener("keydown",w)},[M,D,ne]),t.useEffect(()=>{const w=document.documentElement,_=document.body;function T(){const G=w.classList.contains("dark")||w.classList.contains("dark-theme")||_.classList.contains("dark")||_.classList.contains("dark-theme");Y(c==="inherit"?G?"dark":"light":c)}const A=new MutationObserver(function(G){G.forEach(function(J){J.attributeName==="class"&&T()})});return T(),c==="inherit"&&(A.observe(w,{attributes:!0}),A.observe(_,{attributes:!0})),()=>A.disconnect()},[c]),t.createElement(me,{asChild:!0,radius:"medium",scaling:"100%"},t.createElement(xe,{direction:"column",position:"fixed",top:"0",right:"0",mr:"4",mt:"4",inert:r?void 0:zi,...s,ref:o,style:{zIndex:9999,overflow:"hidden",maxHeight:"calc(100vh - var(--space-4) - var(--space-4))",borderRadius:"var(--radius-4)",backgroundColor:"var(--color-panel-solid)",transformOrigin:"top center",transitionProperty:"transform, box-shadow",transitionDuration:"200ms",transitionTimingFunction:r?"ease-out":"ease-in",transform:r?"none":"translateX(105%)",boxShadow:r?"var(--shadow-5)":"var(--shadow-2)",...e.style}},t.createElement(lt,null,t.createElement(Ge,{flexGrow:"1",p:"5",position:"relative"},t.createElement(Ge,{position:"absolute",top:"0",right:"0",m:"2"},t.createElement(at,{content:"Press T to show/hide the Theme Panel",side:"bottom",sideOffset:6},t.createElement(Oo,{asChild:!0,size:"3",tabIndex:0,className:"rt-ThemePanelShortcut"},t.createElement("button",{onClick:()=>a(!r)},"T")))),t.createElement(Et,{size:"5",trim:"both",as:"h3",mb:"5"},"Theme"),t.createElement(K,{id:"accent-color-title",as:"p",size:"2",weight:"medium",mt:"5"},"Accent color"),t.createElement(Ne,{columns:"10",gap:"2",mt:"3",role:"group","aria-labelledby":"accent-color-title"},ve.accentColor.values.map(w=>t.createElement("label",{key:w,className:"rt-ThemePanelSwatch",style:{backgroundColor:`var(--${w}-9)`}},t.createElement(at,{content:`${_e(w)}${m==="gray"&&U!=="gray"?` (${_e(U)})`:""}`},t.createElement("input",{className:"rt-ThemePanelSwatchInput",type:"radio",name:"accentColor",value:w,checked:m===w,onChange:_=>f(_.target.value)}))))),t.createElement(xe,{asChild:!0,align:"center",justify:"between"},t.createElement(K,{as:"p",id:"gray-color-title",size:"2",weight:"medium",mt:"5"},"Gray color")),t.createElement(Ne,{columns:"10",gap:"2",mt:"3",role:"group","aria-labelledby":"gray-color-title"},ve.grayColor.values.map(w=>t.createElement(xe,{key:w,asChild:!0,align:"center",justify:"center"},t.createElement("label",{className:"rt-ThemePanelSwatch",style:{backgroundColor:w==="auto"?`var(--${$}-9)`:w==="gray"?"var(--gray-9)":`var(--${w}-9)`,filter:w==="gray"?"saturate(0)":void 0}},t.createElement(at,{content:`${_e(w)}${w==="auto"?` (${_e($)})`:""}`},t.createElement("input",{className:"rt-ThemePanelSwatchInput",type:"radio",name:"grayColor",value:w,checked:l===w,onChange:_=>p(_.target.value)})))))),t.createElement(K,{id:"appearance-title",as:"p",size:"2",weight:"medium",mt:"5"},"Appearance"),t.createElement(Ne,{columns:"2",gap:"2",mt:"3",role:"group","aria-labelledby":"appearance-title"},["light","dark"].map(w=>t.createElement("label",{key:w,className:"rt-ThemePanelRadioCard"},t.createElement("input",{className:"rt-ThemePanelRadioCardInput",type:"radio",name:"appearance",value:w,checked:D===w,onChange:_=>M(_.target.value)}),t.createElement(xe,{align:"center",justify:"center",height:"32px",gap:"2"},w==="light"?t.createElement("svg",{width:"15",height:"15",viewBox:"0 0 15 15",fill:"none",xmlns:"http://www.w3.org/2000/svg",style:{margin:"0 -1px"}},t.createElement("path",{d:"M7.5 0C7.77614 0 8 0.223858 8 0.5V2.5C8 2.77614 7.77614 3 7.5 3C7.22386 3 7 2.77614 7 2.5V0.5C7 0.223858 7.22386 0 7.5 0ZM2.1967 2.1967C2.39196 2.00144 2.70854 2.00144 2.90381 2.1967L4.31802 3.61091C4.51328 3.80617 4.51328 4.12276 4.31802 4.31802C4.12276 4.51328 3.80617 4.51328 3.61091 4.31802L2.1967 2.90381C2.00144 2.70854 2.00144 2.39196 2.1967 2.1967ZM0.5 7C0.223858 7 0 7.22386 0 7.5C0 7.77614 0.223858 8 0.5 8H2.5C2.77614 8 3 7.77614 3 7.5C3 7.22386 2.77614 7 2.5 7H0.5ZM2.1967 12.8033C2.00144 12.608 2.00144 12.2915 2.1967 12.0962L3.61091 10.682C3.80617 10.4867 4.12276 10.4867 4.31802 10.682C4.51328 10.8772 4.51328 11.1938 4.31802 11.3891L2.90381 12.8033C2.70854 12.9986 2.39196 12.9986 2.1967 12.8033ZM12.5 7C12.2239 7 12 7.22386 12 7.5C12 7.77614 12.2239 8 12.5 8H14.5C14.7761 8 15 7.77614 15 7.5C15 7.22386 14.7761 7 14.5 7H12.5ZM10.682 4.31802C10.4867 4.12276 10.4867 3.80617 10.682 3.61091L12.0962 2.1967C12.2915 2.00144 12.608 2.00144 12.8033 2.1967C12.9986 2.39196 12.9986 2.70854 12.8033 2.90381L11.3891 4.31802C11.1938 4.51328 10.8772 4.51328 10.682 4.31802ZM8 12.5C8 12.2239 7.77614 12 7.5 12C7.22386 12 7 12.2239 7 12.5V14.5C7 14.7761 7.22386 15 7.5 15C7.77614 15 8 14.7761 8 14.5V12.5ZM10.682 10.682C10.8772 10.4867 11.1938 10.4867 11.3891 10.682L12.8033 12.0962C12.9986 12.2915 12.9986 12.608 12.8033 12.8033C12.608 12.9986 12.2915 12.9986 12.0962 12.8033L10.682 11.3891C10.4867 11.1938 10.4867 10.8772 10.682 10.682ZM5.5 7.5C5.5 6.39543 6.39543 5.5 7.5 5.5C8.60457 5.5 9.5 6.39543 9.5 7.5C9.5 8.60457 8.60457 9.5 7.5 9.5C6.39543 9.5 5.5 8.60457 5.5 7.5ZM7.5 4.5C5.84315 4.5 4.5 5.84315 4.5 7.5C4.5 9.15685 5.84315 10.5 7.5 10.5C9.15685 10.5 10.5 9.15685 10.5 7.5C10.5 5.84315 9.15685 4.5 7.5 4.5Z",fill:"currentColor",fillRule:"evenodd",clipRule:"evenodd"})):t.createElement("svg",{width:"15",height:"15",viewBox:"0 0 15 15",fill:"none",xmlns:"http://www.w3.org/2000/svg",style:{margin:"0 -1px"}},t.createElement("path",{d:"M2.89998 0.499976C2.89998 0.279062 2.72089 0.0999756 2.49998 0.0999756C2.27906 0.0999756 2.09998 0.279062 2.09998 0.499976V1.09998H1.49998C1.27906 1.09998 1.09998 1.27906 1.09998 1.49998C1.09998 1.72089 1.27906 1.89998 1.49998 1.89998H2.09998V2.49998C2.09998 2.72089 2.27906 2.89998 2.49998 2.89998C2.72089 2.89998 2.89998 2.72089 2.89998 2.49998V1.89998H3.49998C3.72089 1.89998 3.89998 1.72089 3.89998 1.49998C3.89998 1.27906 3.72089 1.09998 3.49998 1.09998H2.89998V0.499976ZM5.89998 3.49998C5.89998 3.27906 5.72089 3.09998 5.49998 3.09998C5.27906 3.09998 5.09998 3.27906 5.09998 3.49998V4.09998H4.49998C4.27906 4.09998 4.09998 4.27906 4.09998 4.49998C4.09998 4.72089 4.27906 4.89998 4.49998 4.89998H5.09998V5.49998C5.09998 5.72089 5.27906 5.89998 5.49998 5.89998C5.72089 5.89998 5.89998 5.72089 5.89998 5.49998V4.89998H6.49998C6.72089 4.89998 6.89998 4.72089 6.89998 4.49998C6.89998 4.27906 6.72089 4.09998 6.49998 4.09998H5.89998V3.49998ZM1.89998 6.49998C1.89998 6.27906 1.72089 6.09998 1.49998 6.09998C1.27906 6.09998 1.09998 6.27906 1.09998 6.49998V7.09998H0.499976C0.279062 7.09998 0.0999756 7.27906 0.0999756 7.49998C0.0999756 7.72089 0.279062 7.89998 0.499976 7.89998H1.09998V8.49998C1.09998 8.72089 1.27906 8.89997 1.49998 8.89997C1.72089 8.89997 1.89998 8.72089 1.89998 8.49998V7.89998H2.49998C2.72089 7.89998 2.89998 7.72089 2.89998 7.49998C2.89998 7.27906 2.72089 7.09998 2.49998 7.09998H1.89998V6.49998ZM8.54406 0.98184L8.24618 0.941586C8.03275 0.917676 7.90692 1.1655 8.02936 1.34194C8.17013 1.54479 8.29981 1.75592 8.41754 1.97445C8.91878 2.90485 9.20322 3.96932 9.20322 5.10022C9.20322 8.37201 6.82247 11.0878 3.69887 11.6097C3.45736 11.65 3.20988 11.6772 2.96008 11.6906C2.74563 11.702 2.62729 11.9535 2.77721 12.1072C2.84551 12.1773 2.91535 12.2458 2.98667 12.3128L3.05883 12.3795L3.31883 12.6045L3.50684 12.7532L3.62796 12.8433L3.81491 12.9742L3.99079 13.089C4.11175 13.1651 4.23536 13.2375 4.36157 13.3059L4.62496 13.4412L4.88553 13.5607L5.18837 13.6828L5.43169 13.7686C5.56564 13.8128 5.70149 13.8529 5.83857 13.8885C5.94262 13.9155 6.04767 13.9401 6.15405 13.9622C6.27993 13.9883 6.40713 14.0109 6.53544 14.0298L6.85241 14.0685L7.11934 14.0892C7.24637 14.0965 7.37436 14.1002 7.50322 14.1002C11.1483 14.1002 14.1032 11.1453 14.1032 7.50023C14.1032 7.25044 14.0893 7.00389 14.0623 6.76131L14.0255 6.48407C13.991 6.26083 13.9453 6.04129 13.8891 5.82642C13.8213 5.56709 13.7382 5.31398 13.6409 5.06881L13.5279 4.80132L13.4507 4.63542L13.3766 4.48666C13.2178 4.17773 13.0353 3.88295 12.8312 3.60423L12.6782 3.40352L12.4793 3.16432L12.3157 2.98361L12.1961 2.85951L12.0355 2.70246L11.8134 2.50184L11.4925 2.24191L11.2483 2.06498L10.9562 1.87446L10.6346 1.68894L10.3073 1.52378L10.1938 1.47176L9.95488 1.3706L9.67791 1.2669L9.42566 1.1846L9.10075 1.09489L8.83599 1.03486L8.54406 0.98184ZM10.4032 5.30023C10.4032 4.27588 10.2002 3.29829 9.83244 2.40604C11.7623 3.28995 13.1032 5.23862 13.1032 7.50023C13.1032 10.593 10.596 13.1002 7.50322 13.1002C6.63646 13.1002 5.81597 12.9036 5.08355 12.5522C6.5419 12.0941 7.81081 11.2082 8.74322 10.0416C8.87963 10.2284 9.10028 10.3497 9.34928 10.3497C9.76349 10.3497 10.0993 10.0139 10.0993 9.59971C10.0993 9.24256 9.84965 8.94373 9.51535 8.86816C9.57741 8.75165 9.63653 8.63334 9.6926 8.51332C9.88358 8.63163 10.1088 8.69993 10.35 8.69993C11.0403 8.69993 11.6 8.14028 11.6 7.44993C11.6 6.75976 11.0406 6.20024 10.3505 6.19993C10.3853 5.90487 10.4032 5.60464 10.4032 5.30023Z",fill:"currentColor",fillRule:"evenodd",clipRule:"evenodd"})),t.createElement(K,{size:"1",weight:"medium"},_e(w)))))),t.createElement(K,{id:"radius-title",as:"p",size:"2",weight:"medium",mt:"5"},"Radius"),t.createElement(Ne,{columns:"5",gap:"2",mt:"3",role:"group","aria-labelledby":"radius-title"},ve.radius.values.map(w=>t.createElement(xe,{key:w,direction:"column",align:"center"},t.createElement("label",{className:"rt-ThemePanelRadioCard"},t.createElement("input",{className:"rt-ThemePanelRadioCardInput",type:"radio",name:"radius",id:`theme-panel-radius-${w}`,value:w,checked:b===w,onChange:_=>C(_.target.value)}),t.createElement(me,{asChild:!0,radius:w},t.createElement(Ge,{m:"3",width:"32px",height:"32px",style:{borderTopLeftRadius:w==="full"?"80%":"var(--radius-5)",backgroundImage:"linear-gradient(to bottom right, var(--accent-3), var(--accent-4))",borderTop:"2px solid var(--accent-a8)",borderLeft:"2px solid var(--accent-a8)"}}))),t.createElement(Ge,{asChild:!0,pt:"2"},t.createElement(K,{asChild:!0,size:"1",color:"gray"},t.createElement("label",{htmlFor:`theme-panel-radius-${w}`},_e(w))))))),t.createElement(K,{id:"scaling-title",as:"p",size:"2",weight:"medium",mt:"5"},"Scaling"),t.createElement(Ne,{columns:"5",gap:"2",mt:"3",role:"group","aria-labelledby":"scaling-title"},ve.scaling.values.map(w=>t.createElement("label",{key:w,className:"rt-ThemePanelRadioCard"},t.createElement("input",{className:"rt-ThemePanelRadioCardInput",type:"radio",name:"scaling",value:w,checked:R===w,onChange:_=>j(_.target.value)}),t.createElement(xe,{align:"center",justify:"center",height:"32px"},t.createElement(me,{asChild:!0,scaling:w},t.createElement(xe,{align:"center",justify:"center"},t.createElement(K,{size:"1",weight:"medium"},_e(w)))))))),t.createElement(xe,{mt:"5",align:"center",gap:"2"},t.createElement(K,{id:"panel-background-title",as:"p",size:"2",weight:"medium"},"Panel background"),t.createElement(jo,null,t.createElement(Lo,null,t.createElement(Ao,{size:"1",variant:"ghost",color:"gray"},t.createElement(Cs,{label:"Learn more about panel background options"},t.createElement("svg",{width:"15",height:"15",viewBox:"0 0 15 15",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"},t.createElement("path",{d:"M7.49991 0.876892C3.84222 0.876892 0.877075 3.84204 0.877075 7.49972C0.877075 11.1574 3.84222 14.1226 7.49991 14.1226C11.1576 14.1226 14.1227 11.1574 14.1227 7.49972C14.1227 3.84204 11.1576 0.876892 7.49991 0.876892ZM1.82707 7.49972C1.82707 4.36671 4.36689 1.82689 7.49991 1.82689C10.6329 1.82689 13.1727 4.36671 13.1727 7.49972C13.1727 10.6327 10.6329 13.1726 7.49991 13.1726C4.36689 13.1726 1.82707 10.6327 1.82707 7.49972ZM8.24992 4.49999C8.24992 4.9142 7.91413 5.24999 7.49992 5.24999C7.08571 5.24999 6.74992 4.9142 6.74992 4.49999C6.74992 4.08577 7.08571 3.74999 7.49992 3.74999C7.91413 3.74999 8.24992 4.08577 8.24992 4.49999ZM6.00003 5.99999H6.50003H7.50003C7.77618 5.99999 8.00003 6.22384 8.00003 6.49999V9.99999H8.50003H9.00003V11H8.50003H7.50003H6.50003H6.00003V9.99999H6.50003H7.00003V6.99999H6.50003H6.00003V5.99999Z",fillRule:"evenodd",clipRule:"evenodd"}))))),t.createElement(Bo,{size:"1",style:{maxWidth:220},side:"top",align:"center"},t.createElement(K,{as:"p",size:"2"},"Whether Card and Table panels are translucent, showing some of the background behind them.")))),t.createElement(Ne,{columns:"2",gap:"2",mt:"3",role:"group","aria-labelledby":"panel-background-title"},ve.panelBackground.values.map(w=>t.createElement("label",{key:w,className:"rt-ThemePanelRadioCard"},t.createElement("input",{className:"rt-ThemePanelRadioCardInput",type:"radio",name:"panelBackground",value:w,checked:h===w,onChange:_=>g(_.target.value)}),t.createElement(xe,{align:"center",justify:"center",height:"32px",gap:"2"},w==="solid"?t.createElement("svg",{width:"15",height:"15",viewBox:"0 0 15 15",fill:"none",xmlns:"http://www.w3.org/2000/svg",style:{margin:"0 -2px"}},t.createElement("path",{d:"M0.877075 7.49988C0.877075 3.84219 3.84222 0.877045 7.49991 0.877045C11.1576 0.877045 14.1227 3.84219 14.1227 7.49988C14.1227 11.1575 11.1576 14.1227 7.49991 14.1227C3.84222 14.1227 0.877075 11.1575 0.877075 7.49988ZM7.49991 1.82704C4.36689 1.82704 1.82708 4.36686 1.82708 7.49988C1.82708 10.6329 4.36689 13.1727 7.49991 13.1727C10.6329 13.1727 13.1727 10.6329 13.1727 7.49988C13.1727 4.36686 10.6329 1.82704 7.49991 1.82704Z",fill:"currentColor",fillRule:"evenodd",clipRule:"evenodd"})):t.createElement("svg",{width:"15",height:"15",viewBox:"0 0 15 15",fill:"none",xmlns:"http://www.w3.org/2000/svg",style:{margin:"0 -2px"}},t.createElement("path",{opacity:".05",d:"M6.78296 13.376C8.73904 9.95284 8.73904 5.04719 6.78296 1.62405L7.21708 1.37598C9.261 4.95283 9.261 10.0472 7.21708 13.624L6.78296 13.376Z",fill:"currentColor",fillRule:"evenodd",clipRule:"evenodd"}),t.createElement("path",{opacity:".1",d:"M7.28204 13.4775C9.23929 9.99523 9.23929 5.00475 7.28204 1.52248L7.71791 1.2775C9.76067 4.9119 9.76067 10.0881 7.71791 13.7225L7.28204 13.4775Z",fill:"currentColor",fillRule:"evenodd",clipRule:"evenodd"}),t.createElement("path",{opacity:".15",d:"M7.82098 13.5064C9.72502 9.99523 9.72636 5.01411 7.82492 1.50084L8.26465 1.26285C10.2465 4.92466 10.2451 10.085 8.26052 13.7448L7.82098 13.5064Z",fill:"currentColor",fillRule:"evenodd",clipRule:"evenodd"}),t.createElement("path",{opacity:".2",d:"M8.41284 13.429C10.1952 9.92842 10.1957 5.07537 8.41435 1.57402L8.85999 1.34729C10.7139 4.99113 10.7133 10.0128 8.85841 13.6559L8.41284 13.429Z",fill:"currentColor",fillRule:"evenodd",clipRule:"evenodd"}),t.createElement("path",{opacity:".25",d:"M9.02441 13.2956C10.6567 9.8379 10.6586 5.17715 9.03005 1.71656L9.48245 1.50366C11.1745 5.09919 11.1726 9.91629 9.47657 13.5091L9.02441 13.2956Z",fill:"currentColor",fillRule:"evenodd",clipRule:"evenodd"}),t.createElement("path",{opacity:".3",d:"M9.66809 13.0655C11.1097 9.69572 11.1107 5.3121 9.67088 1.94095L10.1307 1.74457C11.6241 5.24121 11.6231 9.76683 10.1278 13.2622L9.66809 13.0655Z",fill:"currentColor",fillRule:"evenodd",clipRule:"evenodd"}),t.createElement("path",{opacity:".35",d:"M10.331 12.7456C11.5551 9.52073 11.5564 5.49103 10.3347 2.26444L10.8024 2.0874C12.0672 5.42815 12.0659 9.58394 10.7985 12.9231L10.331 12.7456Z",fill:"currentColor",fillRule:"evenodd",clipRule:"evenodd"}),t.createElement("path",{opacity:".4",d:"M11.0155 12.2986C11.9938 9.29744 11.9948 5.71296 11.0184 2.71067L11.4939 2.55603C12.503 5.6589 12.502 9.35178 11.4909 12.4535L11.0155 12.2986Z",fill:"currentColor",fillRule:"evenodd",clipRule:"evenodd"}),t.createElement("path",{opacity:".45",d:"M11.7214 11.668C12.4254 9.01303 12.4262 5.99691 11.7237 3.34116L12.2071 3.21329C12.9318 5.95292 12.931 9.05728 12.2047 11.7961L11.7214 11.668Z",fill:"currentColor",fillRule:"evenodd",clipRule:"evenodd"}),t.createElement("path",{opacity:".5",d:"M12.4432 10.752C12.8524 8.63762 12.8523 6.36089 12.4429 4.2466L12.9338 4.15155C13.3553 6.32861 13.3554 8.66985 12.9341 10.847L12.4432 10.752Z",fill:"currentColor",fillRule:"evenodd",clipRule:"evenodd"}),t.createElement("path",{d:"M0.877075 7.49988C0.877075 3.84219 3.84222 0.877045 7.49991 0.877045C11.1576 0.877045 14.1227 3.84219 14.1227 7.49988C14.1227 11.1575 11.1576 14.1227 7.49991 14.1227C3.84222 14.1227 0.877075 11.1575 0.877075 7.49988ZM7.49991 1.82704C4.36689 1.82704 1.82708 4.36686 1.82708 7.49988C1.82708 10.6329 4.36689 13.1727 7.49991 13.1727C10.6329 13.1727 13.1727 10.6329 13.1727 7.49988C13.1727 4.36686 10.6329 1.82704 7.49991 1.82704Z",fill:"currentColor",fillRule:"evenodd",clipRule:"evenodd"})),t.createElement(K,{size:"1",weight:"medium"},_e(w)))))),t.createElement(mr,{mt:"5",style:{width:"100%"},onClick:W},Q==="copied"?"Copied":"Copy Theme")))))});cl.displayName="ThemePanelImpl";function Gp(){const e=document.createElement("style");return e.appendChild(document.createTextNode("*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(e),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(e)},1)}}function _e(e){return e.charAt(0).toUpperCase()+e.slice(1)}function Kp(e){const o=document.documentElement,r=o.classList.contains("light-theme"),a=o.classList.contains("dark-theme"),n=o.classList.contains("light"),s=o.classList.contains("dark");(r||a)&&(o.classList.remove("light-theme","dark-theme"),o.style.colorScheme=e,o.classList.add(`${e}-theme`)),(n||s)&&(o.classList.remove("light","dark"),o.style.colorScheme=e,o.classList.add(e)),!r&&!a&&!n&&!s&&(o.style.colorScheme=e,o.classList.add(e))}const Wp={content:{type:"ReactNode",required:!0},width:re.width,minWidth:re.minWidth,maxWidth:{...re.maxWidth,default:"360px"}},at=t.forwardRef((e,o)=>{const{children:r,className:a,open:n,defaultOpen:s,onOpenChange:i,delayDuration:c,disableHoverableContent:d,content:m,container:f,forceMount:l,...p}=I(e,Wp),h={open:n,defaultOpen:s,onOpenChange:i,delayDuration:c,disableHoverableContent:d};return t.createElement(Nc,{...h},t.createElement(Sc,{asChild:!0},r),t.createElement(Mc,{container:f,forceMount:l},t.createElement(me,{asChild:!0},t.createElement(_c,{sideOffset:4,collisionPadding:10,...p,asChild:!1,ref:o,className:y("rt-TooltipContent",a)},t.createElement(K,{as:"p",className:"rt-TooltipText",size:"1"},m),t.createElement(Tc,{className:"rt-TooltipArrow"})))))});at.displayName="Tooltip";const Up=Object.freeze(Object.defineProperty({__proto__:null,AccessibleIcon:Cs,AlertDialog:sf,AspectRatio:lf,Avatar:Ms,Badge:Pc,Blockquote:Ts,Box:Ge,Button:mr,Callout:pf,Card:Lc,Checkbox:Vc,CheckboxCards:Sf,CheckboxGroup:Tf,ChevronDownIcon:Ic,Code:Qs,Container:jc,ContextMenu:$f,DataList:Hf,Dialog:Vf,DropdownMenu:kc,Em:Ei,Flex:xe,Grid:Ne,Heading:Et,HoverCard:Kf,IconButton:Ao,Inset:Mi,Kbd:Oo,Link:$c,Popover:Jf,Portal:ep,Progress:Fc,Quote:Pi,Radio:Li,RadioCards:np,RadioGroup:dp,Reset:Bi,ScrollArea:lt,Section:Fi,SegmentedControl:hp,Select:Wc,Separator:Uc,Skeleton:Gi,Slider:Ki,Slot:Zl,Slottable:Yl,Spinner:ql,Strong:Wi,Switch:Ui,TabNav:$p,Table:Fp,Tabs:zp,Text:K,TextArea:zc,TextField:Kc,Theme:me,ThemeContext:Dc,ThemePanel:ll,ThickCheckIcon:it,ThickChevronRightIcon:Rr,ThickDividerHorizontalIcon:Ac,Tooltip:at,VisuallyHidden:Xl,useThemeContext:go},Symbol.toStringTag,{value:"Module"})),sr={styles:{global:{":root":{},body:{fontFamily:"Inter, system-ui, sans-serif","--default-font-family":"Inter, system-ui, sans-serif",backgroundColor:"#f8fafc",color:"#1e293b"}}}};function Zp(e){if(typeof document>"u")return;let o=document.head||document.getElementsByTagName("head")[0],r=document.createElement("style");r.type="text/css",o.appendChild(r),r.styleSheet?r.styleSheet.cssText=e:r.appendChild(document.createTextNode(e))}const Yp=e=>{switch(e){case"success":return Qp;case"info":return eg;case"warning":return Jp;case"error":return tg;default:return null}},qp=Array(12).fill(0),Xp=({visible:e,className:o})=>v.createElement("div",{className:["sonner-loading-wrapper",o].filter(Boolean).join(" "),"data-visible":e},v.createElement("div",{className:"sonner-spinner"},qp.map((r,a)=>v.createElement("div",{className:"sonner-loading-bar",key:`spinner-bar-${a}`})))),Qp=v.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},v.createElement("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z",clipRule:"evenodd"})),Jp=v.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",height:"20",width:"20"},v.createElement("path",{fillRule:"evenodd",d:"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z",clipRule:"evenodd"})),eg=v.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},v.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z",clipRule:"evenodd"})),tg=v.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},v.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z",clipRule:"evenodd"})),og=v.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"12",height:"12",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"},v.createElement("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),v.createElement("line",{x1:"6",y1:"6",x2:"18",y2:"18"})),rg=()=>{const[e,o]=v.useState(document.hidden);return v.useEffect(()=>{const r=()=>{o(document.hidden)};return document.addEventListener("visibilitychange",r),()=>window.removeEventListener("visibilitychange",r)},[]),e};let so=1;class ag{constructor(){this.subscribe=o=>(this.subscribers.push(o),()=>{const r=this.subscribers.indexOf(o);this.subscribers.splice(r,1)}),this.publish=o=>{this.subscribers.forEach(r=>r(o))},this.addToast=o=>{this.publish(o),this.toasts=[...this.toasts,o]},this.create=o=>{var r;const{message:a,...n}=o,s=typeof o?.id=="number"||((r=o.id)==null?void 0:r.length)>0?o.id:so++,i=this.toasts.find(d=>d.id===s),c=o.dismissible===void 0?!0:o.dismissible;return this.dismissedToasts.has(s)&&this.dismissedToasts.delete(s),i?this.toasts=this.toasts.map(d=>d.id===s?(this.publish({...d,...o,id:s,title:a}),{...d,...o,id:s,dismissible:c,title:a}):d):this.addToast({title:a,...n,dismissible:c,id:s}),s},this.dismiss=o=>(o?(this.dismissedToasts.add(o),requestAnimationFrame(()=>this.subscribers.forEach(r=>r({id:o,dismiss:!0})))):this.toasts.forEach(r=>{this.subscribers.forEach(a=>a({id:r.id,dismiss:!0}))}),o),this.message=(o,r)=>this.create({...r,message:o}),this.error=(o,r)=>this.create({...r,message:o,type:"error"}),this.success=(o,r)=>this.create({...r,type:"success",message:o}),this.info=(o,r)=>this.create({...r,type:"info",message:o}),this.warning=(o,r)=>this.create({...r,type:"warning",message:o}),this.loading=(o,r)=>this.create({...r,type:"loading",message:o}),this.promise=(o,r)=>{if(!r)return;let a;r.loading!==void 0&&(a=this.create({...r,promise:o,type:"loading",message:r.loading,description:typeof r.description!="function"?r.description:void 0}));const n=Promise.resolve(o instanceof Function?o():o);let s=a!==void 0,i;const c=n.then(async m=>{if(i=["resolve",m],v.isValidElement(m))s=!1,this.create({id:a,type:"default",message:m});else if(sg(m)&&!m.ok){s=!1;const l=typeof r.error=="function"?await r.error(`HTTP error! status: ${m.status}`):r.error,p=typeof r.description=="function"?await r.description(`HTTP error! status: ${m.status}`):r.description,g=typeof l=="object"&&!v.isValidElement(l)?l:{message:l};this.create({id:a,type:"error",description:p,...g})}else if(m instanceof Error){s=!1;const l=typeof r.error=="function"?await r.error(m):r.error,p=typeof r.description=="function"?await r.description(m):r.description,g=typeof l=="object"&&!v.isValidElement(l)?l:{message:l};this.create({id:a,type:"error",description:p,...g})}else if(r.success!==void 0){s=!1;const l=typeof r.success=="function"?await r.success(m):r.success,p=typeof r.description=="function"?await r.description(m):r.description,g=typeof l=="object"&&!v.isValidElement(l)?l:{message:l};this.create({id:a,type:"success",description:p,...g})}}).catch(async m=>{if(i=["reject",m],r.error!==void 0){s=!1;const f=typeof r.error=="function"?await r.error(m):r.error,l=typeof r.description=="function"?await r.description(m):r.description,h=typeof f=="object"&&!v.isValidElement(f)?f:{message:f};this.create({id:a,type:"error",description:l,...h})}}).finally(()=>{s&&(this.dismiss(a),a=void 0),r.finally==null||r.finally.call(r)}),d=()=>new Promise((m,f)=>c.then(()=>i[0]==="reject"?f(i[1]):m(i[1])).catch(f));return typeof a!="string"&&typeof a!="number"?{unwrap:d}:Object.assign(a,{unwrap:d})},this.custom=(o,r)=>{const a=r?.id||so++;return this.create({jsx:o(a),id:a,...r}),a},this.getActiveToasts=()=>this.toasts.filter(o=>!this.dismissedToasts.has(o.id)),this.subscribers=[],this.toasts=[],this.dismissedToasts=new Set}}const se=new ag,ng=(e,o)=>{const r=o?.id||so++;return se.addToast({title:e,...o,id:r}),r},sg=e=>e&&typeof e=="object"&&"ok"in e&&typeof e.ok=="boolean"&&"status"in e&&typeof e.status=="number",ig=ng,lg=()=>se.toasts,cg=()=>se.getActiveToasts(),dg=Object.assign(ig,{success:se.success,info:se.info,warning:se.warning,error:se.error,custom:se.custom,message:se.message,promise:se.promise,dismiss:se.dismiss,loading:se.loading},{getHistory:lg,getToasts:cg});Zp("[data-sonner-toaster][dir=ltr],html[dir=ltr]{--toast-icon-margin-start:-3px;--toast-icon-margin-end:4px;--toast-svg-margin-start:-1px;--toast-svg-margin-end:0px;--toast-button-margin-start:auto;--toast-button-margin-end:0;--toast-close-button-start:0;--toast-close-button-end:unset;--toast-close-button-transform:translate(-35%, -35%)}[data-sonner-toaster][dir=rtl],html[dir=rtl]{--toast-icon-margin-start:4px;--toast-icon-margin-end:-3px;--toast-svg-margin-start:0px;--toast-svg-margin-end:-1px;--toast-button-margin-start:0;--toast-button-margin-end:auto;--toast-close-button-start:unset;--toast-close-button-end:0;--toast-close-button-transform:translate(35%, -35%)}[data-sonner-toaster]{position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1:hsl(0, 0%, 99%);--gray2:hsl(0, 0%, 97.3%);--gray3:hsl(0, 0%, 95.1%);--gray4:hsl(0, 0%, 93%);--gray5:hsl(0, 0%, 90.9%);--gray6:hsl(0, 0%, 88.7%);--gray7:hsl(0, 0%, 85.8%);--gray8:hsl(0, 0%, 78%);--gray9:hsl(0, 0%, 56.1%);--gray10:hsl(0, 0%, 52.3%);--gray11:hsl(0, 0%, 43.5%);--gray12:hsl(0, 0%, 9%);--border-radius:8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:0;z-index:999999999;transition:transform .4s ease}@media (hover:none) and (pointer:coarse){[data-sonner-toaster][data-lifted=true]{transform:none}}[data-sonner-toaster][data-x-position=right]{right:var(--offset-right)}[data-sonner-toaster][data-x-position=left]{left:var(--offset-left)}[data-sonner-toaster][data-x-position=center]{left:50%;transform:translateX(-50%)}[data-sonner-toaster][data-y-position=top]{top:var(--offset-top)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--offset-bottom)}[data-sonner-toast]{--y:translateY(100%);--lift-amount:calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:0;overflow-wrap:anywhere}[data-sonner-toast][data-styled=true]{padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px rgba(0,0,0,.1);width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}[data-sonner-toast]:focus-visible{box-shadow:0 4px 12px rgba(0,0,0,.1),0 0 0 2px rgba(0,0,0,.2)}[data-sonner-toast][data-y-position=top]{top:0;--y:translateY(-100%);--lift:1;--lift-amount:calc(1 * var(--gap))}[data-sonner-toast][data-y-position=bottom]{bottom:0;--y:translateY(100%);--lift:-1;--lift-amount:calc(var(--lift) * var(--gap))}[data-sonner-toast][data-styled=true] [data-description]{font-weight:400;line-height:1.4;color:#3f3f3f}[data-rich-colors=true][data-sonner-toast][data-styled=true] [data-description]{color:inherit}[data-sonner-toaster][data-sonner-theme=dark] [data-description]{color:#e8e8e8}[data-sonner-toast][data-styled=true] [data-title]{font-weight:500;line-height:1.5;color:inherit}[data-sonner-toast][data-styled=true] [data-icon]{display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}[data-sonner-toast][data-promise=true] [data-icon]>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}[data-sonner-toast][data-styled=true] [data-icon]>*{flex-shrink:0}[data-sonner-toast][data-styled=true] [data-icon] svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}[data-sonner-toast][data-styled=true] [data-content]{display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;font-weight:500;cursor:pointer;outline:0;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}[data-sonner-toast][data-styled=true] [data-button]:focus-visible{box-shadow:0 0 0 2px rgba(0,0,0,.4)}[data-sonner-toast][data-styled=true] [data-button]:first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}[data-sonner-toast][data-styled=true] [data-cancel]{color:var(--normal-text);background:rgba(0,0,0,.08)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast][data-styled=true] [data-cancel]{background:rgba(255,255,255,.3)}[data-sonner-toast][data-styled=true] [data-close-button]{position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;color:var(--gray12);background:var(--normal-bg);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}[data-sonner-toast][data-styled=true] [data-close-button]:focus-visible{box-shadow:0 4px 12px rgba(0,0,0,.1),0 0 0 2px rgba(0,0,0,.2)}[data-sonner-toast][data-styled=true] [data-disabled=true]{cursor:not-allowed}[data-sonner-toast][data-styled=true]:hover [data-close-button]:hover{background:var(--gray2);border-color:var(--gray5)}[data-sonner-toast][data-swiping=true]::before{content:'';position:absolute;left:-100%;right:-100%;height:100%;z-index:-1}[data-sonner-toast][data-y-position=top][data-swiping=true]::before{bottom:50%;transform:scaleY(3) translateY(50%)}[data-sonner-toast][data-y-position=bottom][data-swiping=true]::before{top:50%;transform:scaleY(3) translateY(-50%)}[data-sonner-toast][data-swiping=false][data-removed=true]::before{content:'';position:absolute;inset:0;transform:scaleY(2)}[data-sonner-toast][data-expanded=true]::after{content:'';position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}[data-sonner-toast][data-mounted=true]{--y:translateY(0);opacity:1}[data-sonner-toast][data-expanded=false][data-front=false]{--scale:var(--toasts-before) * 0.05 + 1;--y:translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}[data-sonner-toast]>*{transition:opacity .4s}[data-sonner-toast][data-x-position=right]{right:0}[data-sonner-toast][data-x-position=left]{left:0}[data-sonner-toast][data-expanded=false][data-front=false][data-styled=true]>*{opacity:0}[data-sonner-toast][data-visible=false]{opacity:0;pointer-events:none}[data-sonner-toast][data-mounted=true][data-expanded=true]{--y:translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}[data-sonner-toast][data-removed=true][data-front=true][data-swipe-out=false]{--y:translateY(calc(var(--lift) * -100%));opacity:0}[data-sonner-toast][data-removed=true][data-front=false][data-swipe-out=false][data-expanded=true]{--y:translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}[data-sonner-toast][data-removed=true][data-front=false][data-swipe-out=false][data-expanded=false]{--y:translateY(40%);opacity:0;transition:transform .5s,opacity .2s}[data-sonner-toast][data-removed=true][data-front=false]::before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount-y,0)) translateX(var(--swipe-amount-x,0));transition:none}[data-sonner-toast][data-swiped=true]{user-select:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation-duration:.2s;animation-timing-function:ease-out;animation-fill-mode:forwards}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=left]{animation-name:swipe-out-left}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=right]{animation-name:swipe-out-right}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=up]{animation-name:swipe-out-up}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=down]{animation-name:swipe-out-down}@keyframes swipe-out-left{from{transform:var(--y) translateX(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translateX(calc(var(--swipe-amount-x) - 100%));opacity:0}}@keyframes swipe-out-right{from{transform:var(--y) translateX(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translateX(calc(var(--swipe-amount-x) + 100%));opacity:0}}@keyframes swipe-out-up{from{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) - 100%));opacity:0}}@keyframes swipe-out-down{from{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) + 100%));opacity:0}}@media (max-width:600px){[data-sonner-toaster]{position:fixed;right:var(--mobile-offset-right);left:var(--mobile-offset-left);width:100%}[data-sonner-toaster][dir=rtl]{left:calc(var(--mobile-offset-left) * -1)}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset-left) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset-left)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--mobile-offset-bottom)}[data-sonner-toaster][data-y-position=top]{top:var(--mobile-offset-top)}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset-left);right:var(--mobile-offset-right);transform:none}}[data-sonner-toaster][data-sonner-theme=light]{--normal-bg:#fff;--normal-border:var(--gray4);--normal-text:var(--gray12);--success-bg:hsl(143, 85%, 96%);--success-border:hsl(145, 92%, 87%);--success-text:hsl(140, 100%, 27%);--info-bg:hsl(208, 100%, 97%);--info-border:hsl(221, 91%, 93%);--info-text:hsl(210, 92%, 45%);--warning-bg:hsl(49, 100%, 97%);--warning-border:hsl(49, 91%, 84%);--warning-text:hsl(31, 92%, 45%);--error-bg:hsl(359, 100%, 97%);--error-border:hsl(359, 100%, 94%);--error-text:hsl(360, 100%, 45%)}[data-sonner-toaster][data-sonner-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg:#000;--normal-border:hsl(0, 0%, 20%);--normal-text:var(--gray1)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg:#fff;--normal-border:var(--gray3);--normal-text:var(--gray12)}[data-sonner-toaster][data-sonner-theme=dark]{--normal-bg:#000;--normal-bg-hover:hsl(0, 0%, 12%);--normal-border:hsl(0, 0%, 20%);--normal-border-hover:hsl(0, 0%, 25%);--normal-text:var(--gray1);--success-bg:hsl(150, 100%, 6%);--success-border:hsl(147, 100%, 12%);--success-text:hsl(150, 86%, 65%);--info-bg:hsl(215, 100%, 6%);--info-border:hsl(223, 43%, 17%);--info-text:hsl(216, 87%, 65%);--warning-bg:hsl(64, 100%, 6%);--warning-border:hsl(60, 100%, 9%);--warning-text:hsl(46, 87%, 65%);--error-bg:hsl(358, 76%, 10%);--error-border:hsl(357, 89%, 16%);--error-text:hsl(358, 100%, 81%)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast] [data-close-button]{background:var(--normal-bg);border-color:var(--normal-border);color:var(--normal-text)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast] [data-close-button]:hover{background:var(--normal-bg-hover);border-color:var(--normal-border-hover)}[data-rich-colors=true][data-sonner-toast][data-type=success]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size:16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:first-child{animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}100%{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}100%{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}100%{opacity:.15}}@media (prefers-reduced-motion){.sonner-loading-bar,[data-sonner-toast],[data-sonner-toast]>*{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}");function pt(e){return e.label!==void 0}const ug=3,mg="24px",fg="16px",ir=4e3,pg=356,gg=14,vg=45,hg=200;function ye(...e){return e.filter(Boolean).join(" ")}function Cg(e){const[o,r]=e.split("-"),a=[];return o&&a.push(o),r&&a.push(r),a}const bg=e=>{var o,r,a,n,s,i,c,d,m;const{invert:f,toast:l,unstyled:p,interacting:h,setHeights:g,visibleToasts:b,heights:C,index:R,toasts:j,expanded:E,removeToast:N,defaultRichColors:M,closeButton:$,style:U,cancelButtonStyle:Q,actionButtonStyle:O,className:W="",descriptionClassName:D="",duration:Y,position:ne,gap:w,expandByDefault:_,classNames:T,icons:A,closeButtonAriaLabel:G="Close toast"}=e,[J,qe]=v.useState(null),[L,F]=v.useState(null),[H,de]=v.useState(!1),[$e,z]=v.useState(!1),[Xe,He]=v.useState(!1),[Qe,Je]=v.useState(!1),[fl,Vo]=v.useState(!1),[pl,Ft]=v.useState(0),[gl,Fo]=v.useState(0),et=v.useRef(l.duration||Y||ir),zo=v.useRef(null),Re=v.useRef(null),vl=R===0,hl=R+1<=b,ie=l.type,Ve=l.dismissible!==!1,Cl=l.className||"",bl=l.descriptionClassName||"",dt=v.useMemo(()=>C.findIndex(k=>k.toastId===l.id)||0,[C,l.id]),yl=v.useMemo(()=>{var k;return(k=l.closeButton)!=null?k:$},[l.closeButton,$]),Go=v.useMemo(()=>l.duration||Y||ir,[l.duration,Y]),zt=v.useRef(0),Fe=v.useRef(0),Ko=v.useRef(0),ze=v.useRef(null),[xl,wl]=ne.split("-"),Wo=v.useMemo(()=>C.reduce((k,Z,ee)=>ee>=dt?k:k+Z.height,0),[C,dt]),Uo=rg(),El=l.invert||f,Gt=ie==="loading";Fe.current=v.useMemo(()=>dt*w+Wo,[dt,Wo]),v.useEffect(()=>{et.current=Go},[Go]),v.useEffect(()=>{de(!0)},[]),v.useEffect(()=>{const k=Re.current;if(k){const Z=k.getBoundingClientRect().height;return Fo(Z),g(ee=>[{toastId:l.id,height:Z,position:l.position},...ee]),()=>g(ee=>ee.filter(le=>le.toastId!==l.id))}},[g,l.id]),v.useLayoutEffect(()=>{if(!H)return;const k=Re.current,Z=k.style.height;k.style.height="auto";const ee=k.getBoundingClientRect().height;k.style.height=Z,Fo(ee),g(le=>le.find(q=>q.toastId===l.id)?le.map(q=>q.toastId===l.id?{...q,height:ee}:q):[{toastId:l.id,height:ee,position:l.position},...le])},[H,l.title,l.description,g,l.id,l.jsx,l.action,l.cancel]);const Me=v.useCallback(()=>{z(!0),Ft(Fe.current),g(k=>k.filter(Z=>Z.toastId!==l.id)),setTimeout(()=>{N(l)},hg)},[l,N,g,Fe]);v.useEffect(()=>{if(l.promise&&ie==="loading"||l.duration===1/0||l.type==="loading")return;let k;return E||h||Uo?(()=>{if(Ko.current<zt.current){const le=new Date().getTime()-zt.current;et.current=et.current-le}Ko.current=new Date().getTime()})():(()=>{et.current!==1/0&&(zt.current=new Date().getTime(),k=setTimeout(()=>{l.onAutoClose==null||l.onAutoClose.call(l,l),Me()},et.current))})(),()=>clearTimeout(k)},[E,h,l,ie,Uo,Me]),v.useEffect(()=>{l.delete&&(Me(),l.onDismiss==null||l.onDismiss.call(l,l))},[Me,l.delete]);function Rl(){var k;if(A?.loading){var Z;return v.createElement("div",{className:ye(T?.loader,l==null||(Z=l.classNames)==null?void 0:Z.loader,"sonner-loader"),"data-visible":ie==="loading"},A.loading)}return v.createElement(Xp,{className:ye(T?.loader,l==null||(k=l.classNames)==null?void 0:k.loader),visible:ie==="loading"})}const Nl=l.icon||A?.[ie]||Yp(ie);var Zo,Yo;return v.createElement("li",{tabIndex:0,ref:Re,className:ye(W,Cl,T?.toast,l==null||(o=l.classNames)==null?void 0:o.toast,T?.default,T?.[ie],l==null||(r=l.classNames)==null?void 0:r[ie]),"data-sonner-toast":"","data-rich-colors":(Zo=l.richColors)!=null?Zo:M,"data-styled":!(l.jsx||l.unstyled||p),"data-mounted":H,"data-promise":!!l.promise,"data-swiped":fl,"data-removed":$e,"data-visible":hl,"data-y-position":xl,"data-x-position":wl,"data-index":R,"data-front":vl,"data-swiping":Xe,"data-dismissible":Ve,"data-type":ie,"data-invert":El,"data-swipe-out":Qe,"data-swipe-direction":L,"data-expanded":!!(E||_&&H),style:{"--index":R,"--toasts-before":R,"--z-index":j.length-R,"--offset":`${$e?pl:Fe.current}px`,"--initial-height":_?"auto":`${gl}px`,...U,...l.style},onDragEnd:()=>{He(!1),qe(null),ze.current=null},onPointerDown:k=>{k.button!==2&&(Gt||!Ve||(zo.current=new Date,Ft(Fe.current),k.target.setPointerCapture(k.pointerId),k.target.tagName!=="BUTTON"&&(He(!0),ze.current={x:k.clientX,y:k.clientY})))},onPointerUp:()=>{var k,Z,ee;if(Qe||!Ve)return;ze.current=null;const le=Number(((k=Re.current)==null?void 0:k.style.getPropertyValue("--swipe-amount-x").replace("px",""))||0),ut=Number(((Z=Re.current)==null?void 0:Z.style.getPropertyValue("--swipe-amount-y").replace("px",""))||0),q=new Date().getTime()-((ee=zo.current)==null?void 0:ee.getTime()),ue=J==="x"?le:ut,mt=Math.abs(ue)/q;if(Math.abs(ue)>=vg||mt>.11){Ft(Fe.current),l.onDismiss==null||l.onDismiss.call(l,l),F(J==="x"?le>0?"right":"left":ut>0?"down":"up"),Me(),Je(!0);return}else{var pe,ge;(pe=Re.current)==null||pe.style.setProperty("--swipe-amount-x","0px"),(ge=Re.current)==null||ge.style.setProperty("--swipe-amount-y","0px")}Vo(!1),He(!1),qe(null)},onPointerMove:k=>{var Z,ee,le;if(!ze.current||!Ve||((Z=window.getSelection())==null?void 0:Z.toString().length)>0)return;const q=k.clientY-ze.current.y,ue=k.clientX-ze.current.x;var mt;const pe=(mt=e.swipeDirections)!=null?mt:Cg(ne);!J&&(Math.abs(ue)>1||Math.abs(q)>1)&&qe(Math.abs(ue)>Math.abs(q)?"x":"y");let ge={x:0,y:0};const qo=De=>1/(1.5+Math.abs(De)/20);if(J==="y"){if(pe.includes("top")||pe.includes("bottom"))if(pe.includes("top")&&q<0||pe.includes("bottom")&&q>0)ge.y=q;else{const De=q*qo(q);ge.y=Math.abs(De)<Math.abs(q)?De:q}}else if(J==="x"&&(pe.includes("left")||pe.includes("right")))if(pe.includes("left")&&ue<0||pe.includes("right")&&ue>0)ge.x=ue;else{const De=ue*qo(ue);ge.x=Math.abs(De)<Math.abs(ue)?De:ue}(Math.abs(ge.x)>0||Math.abs(ge.y)>0)&&Vo(!0),(ee=Re.current)==null||ee.style.setProperty("--swipe-amount-x",`${ge.x}px`),(le=Re.current)==null||le.style.setProperty("--swipe-amount-y",`${ge.y}px`)}},yl&&!l.jsx&&ie!=="loading"?v.createElement("button",{"aria-label":G,"data-disabled":Gt,"data-close-button":!0,onClick:Gt||!Ve?()=>{}:()=>{Me(),l.onDismiss==null||l.onDismiss.call(l,l)},className:ye(T?.closeButton,l==null||(a=l.classNames)==null?void 0:a.closeButton)},(Yo=A?.close)!=null?Yo:og):null,(ie||l.icon||l.promise)&&l.icon!==null&&(A?.[ie]!==null||l.icon)?v.createElement("div",{"data-icon":"",className:ye(T?.icon,l==null||(n=l.classNames)==null?void 0:n.icon)},l.promise||l.type==="loading"&&!l.icon?l.icon||Rl():null,l.type!=="loading"?Nl:null):null,v.createElement("div",{"data-content":"",className:ye(T?.content,l==null||(s=l.classNames)==null?void 0:s.content)},v.createElement("div",{"data-title":"",className:ye(T?.title,l==null||(i=l.classNames)==null?void 0:i.title)},l.jsx?l.jsx:typeof l.title=="function"?l.title():l.title),l.description?v.createElement("div",{"data-description":"",className:ye(D,bl,T?.description,l==null||(c=l.classNames)==null?void 0:c.description)},typeof l.description=="function"?l.description():l.description):null),v.isValidElement(l.cancel)?l.cancel:l.cancel&&pt(l.cancel)?v.createElement("button",{"data-button":!0,"data-cancel":!0,style:l.cancelButtonStyle||Q,onClick:k=>{pt(l.cancel)&&Ve&&(l.cancel.onClick==null||l.cancel.onClick.call(l.cancel,k),Me())},className:ye(T?.cancelButton,l==null||(d=l.classNames)==null?void 0:d.cancelButton)},l.cancel.label):null,v.isValidElement(l.action)?l.action:l.action&&pt(l.action)?v.createElement("button",{"data-button":!0,"data-action":!0,style:l.actionButtonStyle||O,onClick:k=>{pt(l.action)&&(l.action.onClick==null||l.action.onClick.call(l.action,k),!k.defaultPrevented&&Me())},className:ye(T?.actionButton,l==null||(m=l.classNames)==null?void 0:m.actionButton)},l.action.label):null)};function lr(){if(typeof window>"u"||typeof document>"u")return"ltr";const e=document.documentElement.getAttribute("dir");return e==="auto"||!e?window.getComputedStyle(document.documentElement).direction:e}function yg(e,o){const r={};return[e,o].forEach((a,n)=>{const s=n===1,i=s?"--mobile-offset":"--offset",c=s?fg:mg;function d(m){["top","right","bottom","left"].forEach(f=>{r[`${i}-${f}`]=typeof m=="number"?`${m}px`:m})}typeof a=="number"||typeof a=="string"?d(a):typeof a=="object"?["top","right","bottom","left"].forEach(m=>{a[m]===void 0?r[`${i}-${m}`]=c:r[`${i}-${m}`]=typeof a[m]=="number"?`${a[m]}px`:a[m]}):d(c)}),r}const xg=v.forwardRef(function(o,r){const{invert:a,position:n="bottom-right",hotkey:s=["altKey","KeyT"],expand:i,closeButton:c,className:d,offset:m,mobileOffset:f,theme:l="light",richColors:p,duration:h,style:g,visibleToasts:b=ug,toastOptions:C,dir:R=lr(),gap:j=gg,icons:E,containerAriaLabel:N="Notifications"}=o,[M,$]=v.useState([]),U=v.useMemo(()=>Array.from(new Set([n].concat(M.filter(L=>L.position).map(L=>L.position)))),[M,n]),[Q,O]=v.useState([]),[W,D]=v.useState(!1),[Y,ne]=v.useState(!1),[w,_]=v.useState(l!=="system"?l:typeof window<"u"&&window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"),T=v.useRef(null),A=s.join("+").replace(/Key/g,"").replace(/Digit/g,""),G=v.useRef(null),J=v.useRef(!1),qe=v.useCallback(L=>{$(F=>{var H;return(H=F.find(de=>de.id===L.id))!=null&&H.delete||se.dismiss(L.id),F.filter(({id:de})=>de!==L.id)})},[]);return v.useEffect(()=>se.subscribe(L=>{if(L.dismiss){requestAnimationFrame(()=>{$(F=>F.map(H=>H.id===L.id?{...H,delete:!0}:H))});return}setTimeout(()=>{_r.flushSync(()=>{$(F=>{const H=F.findIndex(de=>de.id===L.id);return H!==-1?[...F.slice(0,H),{...F[H],...L},...F.slice(H+1)]:[L,...F]})})})}),[M]),v.useEffect(()=>{if(l!=="system"){_(l);return}if(l==="system"&&(window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?_("dark"):_("light")),typeof window>"u")return;const L=window.matchMedia("(prefers-color-scheme: dark)");try{L.addEventListener("change",({matches:F})=>{_(F?"dark":"light")})}catch{L.addListener(({matches:H})=>{try{_(H?"dark":"light")}catch(de){console.error(de)}})}},[l]),v.useEffect(()=>{M.length<=1&&D(!1)},[M]),v.useEffect(()=>{const L=F=>{var H;if(s.every(z=>F[z]||F.code===z)){var $e;D(!0),($e=T.current)==null||$e.focus()}F.code==="Escape"&&(document.activeElement===T.current||(H=T.current)!=null&&H.contains(document.activeElement))&&D(!1)};return document.addEventListener("keydown",L),()=>document.removeEventListener("keydown",L)},[s]),v.useEffect(()=>{if(T.current)return()=>{G.current&&(G.current.focus({preventScroll:!0}),G.current=null,J.current=!1)}},[T.current]),v.createElement("section",{ref:r,"aria-label":`${N} ${A}`,tabIndex:-1,"aria-live":"polite","aria-relevant":"additions text","aria-atomic":"false",suppressHydrationWarning:!0},U.map((L,F)=>{var H;const[de,$e]=L.split("-");return M.length?v.createElement("ol",{key:L,dir:R==="auto"?lr():R,tabIndex:-1,ref:T,className:d,"data-sonner-toaster":!0,"data-sonner-theme":w,"data-y-position":de,"data-x-position":$e,style:{"--front-toast-height":`${((H=Q[0])==null?void 0:H.height)||0}px`,"--width":`${pg}px`,"--gap":`${j}px`,...g,...yg(m,f)},onBlur:z=>{J.current&&!z.currentTarget.contains(z.relatedTarget)&&(J.current=!1,G.current&&(G.current.focus({preventScroll:!0}),G.current=null))},onFocus:z=>{z.target instanceof HTMLElement&&z.target.dataset.dismissible==="false"||J.current||(J.current=!0,G.current=z.relatedTarget)},onMouseEnter:()=>D(!0),onMouseMove:()=>D(!0),onMouseLeave:()=>{Y||D(!1)},onDragEnd:()=>D(!1),onPointerDown:z=>{z.target instanceof HTMLElement&&z.target.dataset.dismissible==="false"||ne(!0)},onPointerUp:()=>ne(!1)},M.filter(z=>!z.position&&F===0||z.position===L).map((z,Xe)=>{var He,Qe;return v.createElement(bg,{key:z.id,icons:E,index:Xe,toast:z,defaultRichColors:p,duration:(He=C?.duration)!=null?He:h,className:C?.className,descriptionClassName:C?.descriptionClassName,invert:a,visibleToasts:b,closeButton:(Qe=C?.closeButton)!=null?Qe:c,interacting:Y,position:L,style:C?.style,unstyled:C?.unstyled,classNames:C?.classNames,cancelButtonStyle:C?.cancelButtonStyle,actionButtonStyle:C?.actionButtonStyle,closeButtonAriaLabel:C?.closeButtonAriaLabel,removeToast:qe,toasts:M.filter(Je=>Je.position==z.position),heights:Q.filter(Je=>Je.position==z.position),setHeights:O,expandByDefault:i,gap:j,expanded:W,swipeDirections:o.swipeDirections})})):null}))});/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wg=[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}],["path",{d:"M5 12.859a10 10 0 0 1 5.17-2.69",key:"1dl1wf"}],["path",{d:"M19 12.859a10 10 0 0 0-2.007-1.523",key:"4k23kn"}],["path",{d:"M2 8.82a15 15 0 0 1 4.177-2.643",key:"1grhjp"}],["path",{d:"M22 8.82a15 15 0 0 0-11.288-3.764",key:"z3jwby"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]],Eg=Oc("wifi-off",wg),dl=t.memo(({})=>{const{resolvedColorMode:e}=t.useContext(io);return x("a",{css:{position:"fixed",bottom:"1rem",right:"1rem",display:"flex",flexDirection:"row",gap:"0.375rem",alignItems:"center",width:"auto",borderRadius:"0.5rem",color:e==="light"?"#E5E7EB":"#27282B",border:e==="light"?"1px solid #27282B":"1px solid #E5E7EB",backgroundColor:e==="light"?"#151618":"#FCFCFD",padding:"0.375rem",transition:"background-color 0.2s ease-in-out",boxShadow:"0 1px 2px 0 rgba(0, 0, 0, 0.05)",zIndex:"9998",cursor:"pointer",align:"center",textAlign:"center"},href:"https://reflex.dev",target:"_blank"},x("svg",{css:{fill:"white",viewBox:"0 0 16 16"},height:"16",width:"16",xmlns:"http://www.w3.org/2000/svg"},x("rect",{css:{fill:"#6E56CF"},height:"16",rx:"2",width:"16"}),x("path",{css:{fill:"white"},d:"M10 9V13H12V9H10Z"}),x("path",{css:{fill:"white"},d:"M4 3V13H6V9H10V7H6V5H10V7H12V3H4Z"})),x(Ge,{css:{"@media screen and (min-width: 0)":{display:"none"},"@media screen and (min-width: 30em)":{display:"none"},"@media screen and (min-width: 48em)":{display:"none"},"@media screen and (min-width: 62em)":{display:"block"}}},x(K,{as:"p",css:{color:"var(--slate-1)",fontWeight:"600",fontFamily:"'Instrument Sans', sans-serif","--default-font-family":"'Instrument Sans', sans-serif",fontSize:"0.875rem",lineHeight:"1rem",letterSpacing:"-0.00656rem"}},"Built with Reflex")))}),ul=t.memo(({})=>{const{resolvedColorMode:e}=t.useContext(io);return dr.__toast=dg,x(xg,{closeButton:!1,expand:!0,position:"bottom-right",richColors:!0,theme:e})}),ml=t.memo(({})=>{const[e,o]=t.useContext(cr),r=dr.__toast,a={description:"Check if server is reachable at "+Al(Ol.EVENT).href,closeButton:!0,duration:12e4,id:"websocket-error"},[n,s]=t.useState(!1),[i,c]=t.useState(!1);return t.useEffect(()=>{o.length>=2?n||r?.error("Cannot connect to server: "+(o.length>0?o[o.length-1].message:"")+".",{...a,onDismiss:()=>s(!0)}):(r?.dismiss("websocket-error"),s(!1))},[o,i]),x(t.Fragment,{},x("div",{css:{position:"fixed",width:"100vw",height:"0"},title:"Connection Error: "+(o.length>0?o[o.length-1].message:"")},x(t.Fragment,{},o.length>0?x(t.Fragment,{},x(Eg,{css:{color:"crimson",zIndex:9999,position:"fixed",bottom:"33px",right:"33px",animation:Vl({from:{opacity:0},to:{opacity:1}})+" 1s infinite"},size:32})):x(t.Fragment,{}))),x(t.Fragment,{}))}),Rg=Object.freeze(Object.defineProperty({__proto__:null,DefaultOverlayComponents:ml,MemoizedBadge:dl,MemoizedToastProvider:ul},Symbol.toStringTag,{value:"Module"}));function Ng({children:e}){return x("html",{lang:"en"},x("head",{},x("meta",{charSet:"utf-8"}),x("meta",{content:"width=device-width, initial-scale=1",name:"viewport"}),x(Tl,{}),x(Pl,{})),x("body",{},e,x(Ml,{}),x(_l,{})))}const Qg=()=>[{rel:"stylesheet",href:Zc,type:"text/css"}];function Sg({children:e}){const[o,r]=t.useContext(cr);return x(t.StrictMode,{},x(qc,{fallbackRender:a=>x("div",{css:{height:"100%",width:"100%",position:"absolute",display:"flex",alignItems:"center",justifyContent:"center"}},x("div",{css:{display:"flex",flexDirection:"column",gap:"1rem"}},x("div",{css:{display:"flex",flexDirection:"column",gap:"1rem",maxWidth:"50ch",border:"1px solid #888888",borderRadius:"0.25rem",padding:"1rem"}},x("h2",{css:{fontSize:"1.25rem",fontWeight:"bold"}},x(t.Fragment,{},"An error occurred while rendering this page.")),x("p",{css:{opacity:"0.75"}},x(t.Fragment,{},"This is an error with the application itself.")),x("details",{},x("summary",{css:{padding:"0.5rem"}},x(t.Fragment,{},"Error message")),x("div",{css:{width:"100%",maxHeight:"50vh",overflow:"auto",background:"#000",color:"#fff",borderRadius:"0.25rem"}},x("div",{css:{padding:"0.5rem",width:"fit-content"}},x("pre",{},x(t.Fragment,{},a.error.name+": "+a.error.message+`
`+a.error.stack)))),x("button",{css:{padding:"0.35rem 0.75rem",margin:"0.5rem",background:"#fff",color:"#000",border:"1px solid #000",borderRadius:"0.25rem",fontWeight:"bold"},onClick:n=>o([Xo("_call_function",{function:()=>navigator.clipboard.writeText(a.error.name+": "+a.error.message+`
`+a.error.stack),callback:null},{})],[n],{})},x(t.Fragment,{},"Copy")))),x("hr",{css:{borderColor:"currentColor",opacity:"0.25"}}),x(Dl,{to:"https://reflex.dev"},x("div",{css:{display:"flex",alignItems:"baseline",justifyContent:"center",fontFamily:"monospace","--default-font-family":"monospace",gap:"0.5rem"}},x(t.Fragment,{},"Built with "),x("svg",{"aria-label":"Reflex",css:{fill:"currentColor"},height:"12",role:"img",width:"56",xmlns:"http://www.w3.org/2000/svg"},x("path",{d:"M0 11.5999V0.399902H8.96V4.8799H6.72V2.6399H2.24V4.8799H6.72V7.1199H2.24V11.5999H0ZM6.72 11.5999V7.1199H8.96V11.5999H6.72Z"}),x("path",{d:"M11.2 11.5999V0.399902H17.92V2.6399H13.44V4.8799H17.92V7.1199H13.44V9.3599H17.92V11.5999H11.2Z"}),x("path",{d:"M20.16 11.5999V0.399902H26.88V2.6399H22.4V4.8799H26.88V7.1199H22.4V11.5999H20.16Z"}),x("path",{d:"M29.12 11.5999V0.399902H31.36V9.3599H35.84V11.5999H29.12Z"}),x("path",{d:"M38.08 11.5999V0.399902H44.8V2.6399H40.32V4.8799H44.8V7.1199H40.32V9.3599H44.8V11.5999H38.08Z"}),x("path",{d:"M47.04 4.8799V0.399902H49.28V4.8799H47.04ZM53.76 4.8799V0.399902H56V4.8799H53.76ZM49.28 7.1199V4.8799H53.76V7.1199H49.28ZM47.04 11.5999V7.1199H49.28V11.5999H47.04ZM53.76 11.5999V7.1199H56V11.5999H53.76Z"}),x("title",{},x(t.Fragment,{},"Reflex"))))))),onError:(a,n)=>o([Xo("reflex___state____state.reflex___state____frontend_event_exception_state.handle_frontend_exception",{info:a.name+": "+a.message+`
`+a.stack,component_stack:n.componentStack},{})],[a,n],{})},x(ed,{},x(me,{accentColor:"blue",css:{...sr.styles.global[":root"],...sr.styles.global.body},hasBackground:!0,radius:"medium"},x(t.Fragment,{},x(ml,{}),x(t.Fragment,{},x(ul,{}),x(t.Fragment,{},e,x(dl,{}))))))))}function Jg({children:e}){return t.useEffect(()=>{let o={react:kl,"@radix-ui/themes":Up,"@emotion/react":Fl,"$/utils/context":Ll,"$/utils/state":jl,"$/utils/components":Rg};window.__reflex=o},[]),x(Ng,{},x(Qc,{defaultTheme:nt,attribute:"class"},x(Bl,{},x(Hl,{},x(Sg,{},e)))))}const ev=Il(function(){return x($l,{})});export{Jg as Layout,ev as default,Qg as links};
