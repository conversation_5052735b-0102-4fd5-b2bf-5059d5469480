

import { Fragment, useCallback, useContext, useEffect } from "react"
import { <PERSON><PERSON> as <PERSON>di<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Container as <PERSON>di<PERSON><PERSON>hem<PERSON><PERSON>ontainer, <PERSON><PERSON> as RadixThemes<PERSON>lex, Heading as <PERSON>di<PERSON>ThemesHeading, Separator as RadixThemesSeparator, Text as RadixThemesText } from "@radix-ui/themes"
import { EventLoopContext } from "$/utils/context"
import { Event } from "$/utils/state"
import { jsx } from "@emotion/react"



function Button_179436632369173387578399609862337879780 () {
  
  const [addEvents, connectErrors] = useContext(EventLoopContext);


  const on_click_2970b759e0bcb8e9d1e60cef41dd79c6 = useCallback(((_e) => (addEvents([(Event("_redirect", ({ ["path"] : "/dashboard", ["external"] : false, ["replace"] : false }), ({  })))], [_e], ({  })))), [addEvents, Event])



  
  return (
    jsx(
RadixThemesButton,
{color:"blue",onClick:on_click_2970b759e0bcb8e9d1e60cef41dd79c6},
"Go to Dashboard"
,)
  )
}

function Button_134631876806559893161780236817078496960 () {
  
  const [addEvents, connectErrors] = useContext(EventLoopContext);


  const on_click_3b8cc28c3e0c773eb7fc7bba592b8f93 = useCallback(((_e) => (addEvents([(Event("_redirect", ({ ["path"] : "/patients", ["external"] : false, ["replace"] : false }), ({  })))], [_e], ({  })))), [addEvents, Event])



  
  return (
    jsx(
RadixThemesButton,
{color:"green",onClick:on_click_3b8cc28c3e0c773eb7fc7bba592b8f93},
"Patient Search"
,)
  )
}

export default function Component() {
    




  return (
    jsx(
Fragment,
{},
jsx(
RadixThemesContainer,
{css:({ ["padding"] : "2rem", ["maxWidth"] : "800px", ["margin"] : "0 auto" }),size:"3"},
jsx(
RadixThemesFlex,
{align:"center",className:"rx-Stack",css:({ ["minHeight"] : "100vh" }),direction:"column",justify:"center",gap:"4"},
jsx(
RadixThemesHeading,
{size:"9"},
"\ud83c\udfe5 Psychiatry EMR"
,),jsx(
RadixThemesText,
{as:"p",css:({ ["color"] : "gray" }),size:"5"},
"Secure Patient Management System"
,),jsx(RadixThemesSeparator,{size:"4"},)
,jsx(
RadixThemesText,
{as:"p",css:({ ["color"] : "green" }),size:"4"},
"\u2705 Application successfully initialized!"
,),jsx(
RadixThemesText,
{as:"p",size:"3"},
"\ud83d\udd10 Encryption service active"
,),jsx(
RadixThemesText,
{as:"p",size:"3"},
"\ud83d\uddc4\ufe0f Database connected"
,),jsx(
RadixThemesText,
{as:"p",size:"3"},
"\ud83d\udcca Ready for patient management"
,),jsx(RadixThemesSeparator,{size:"4"},)
,jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"row",gap:"4"},
jsx(Button_179436632369173387578399609862337879780,{},)
,jsx(Button_134631876806559893161780236817078496960,{},)
,),),),jsx(
"title",
{},
"Psychiatry EMR"
,),jsx("meta",{content:"favicon.ico",property:"og:image"},)
,)
  )
}
