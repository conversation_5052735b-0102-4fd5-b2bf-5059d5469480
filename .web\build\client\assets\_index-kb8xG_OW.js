import{w as d,a as o}from"./chunk-QMGIS6GS-suYYFPSk.js";import{E as p,c as r}from"./state-B1hYtTsq.js";import{j as e}from"./emotion-react.browser.esm-BNSIgtcs.js";import{o as c}from"./separator-CPIfs_Op.js";import{c as i,r as f,o as l}from"./button-Ccwu8jNm.js";import{p as u}from"./container-Do-LxYxS.js";import{p as a}from"./text-DCkbNTq3.js";import"./index-XOwJfM4g.js";import"./jsx-runtime-D_zvdyIk.js";function g(){const[t,m]=o.useContext(p),n=o.useCallback(s=>t([r("_redirect",{path:"/patients",external:!1,replace:!1},{})],[s],{}),[t,r]);return e(l,{color:"green",onClick:n},"Patient Search")}function b(){const[t,m]=o.useContext(p),n=o.useCallback(s=>t([r("_redirect",{path:"/dashboard",external:!1,replace:!1},{})],[s],{}),[t,r]);return e(l,{color:"blue",onClick:n},"Go to Dashboard")}const P=d(function(){return e(o.Fragment,{},e(u,{css:{padding:"2rem",maxWidth:"800px",margin:"0 auto"},size:"3"},e(i,{align:"center",className:"rx-Stack",css:{minHeight:"100vh"},direction:"column",justify:"center",gap:"4"},e(f,{size:"9"},"🏥 Psychiatry EMR"),e(a,{as:"p",css:{color:"gray"},size:"5"},"Secure Patient Management System"),e(c,{size:"4"}),e(a,{as:"p",css:{color:"green"},size:"4"},"✅ Application successfully initialized!"),e(a,{as:"p",size:"3"},"🔐 Encryption service active"),e(a,{as:"p",size:"3"},"🗄️ Database connected"),e(a,{as:"p",size:"3"},"📊 Ready for patient management"),e(c,{size:"4"}),e(i,{align:"start",className:"rx-Stack",direction:"row",gap:"4"},e(b,{}),e(g,{})))),e("title",{},"Psychiatry EMR"),e("meta",{content:"favicon.ico",property:"og:image"}))});export{P as default};
