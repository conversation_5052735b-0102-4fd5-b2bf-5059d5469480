function ma(e,t){for(var r=0;r<t.length;r++){const a=t[r];if(typeof a!="string"&&!Array.isArray(a)){for(const n in a)if(n!=="default"&&!(n in e)){const o=Object.getOwnPropertyDescriptor(a,n);o&&Object.defineProperty(e,n,o.get?o:{enumerable:!0,get:()=>a[n]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}var dl=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function pa(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var zt={exports:{}},B={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ir;function ya(){if(Ir)return B;Ir=1;var e=Symbol.for("react.transitional.element"),t=Symbol.for("react.portal"),r=Symbol.for("react.fragment"),a=Symbol.for("react.strict_mode"),n=Symbol.for("react.profiler"),o=Symbol.for("react.consumer"),l=Symbol.for("react.context"),s=Symbol.for("react.forward_ref"),i=Symbol.for("react.suspense"),u=Symbol.for("react.memo"),f=Symbol.for("react.lazy"),p=Symbol.iterator;function h(m){return m===null||typeof m!="object"?null:(m=p&&m[p]||m["@@iterator"],typeof m=="function"?m:null)}var g={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},w=Object.assign,b={};function S(m,x,k){this.props=m,this.context=x,this.refs=b,this.updater=k||g}S.prototype.isReactComponent={},S.prototype.setState=function(m,x){if(typeof m!="object"&&typeof m!="function"&&m!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,m,x,"setState")},S.prototype.forceUpdate=function(m){this.updater.enqueueForceUpdate(this,m,"forceUpdate")};function R(){}R.prototype=S.prototype;function M(m,x,k){this.props=m,this.context=x,this.refs=b,this.updater=k||g}var P=M.prototype=new R;P.constructor=M,w(P,S.prototype),P.isPureReactComponent=!0;var T=Array.isArray,L={H:null,A:null,T:null,S:null,V:null},y=Object.prototype.hasOwnProperty;function $(m,x,k,N,W,ee){return k=ee.ref,{$$typeof:e,type:m,key:x,ref:k!==void 0?k:null,props:ee}}function J(m,x){return $(m.type,x,void 0,void 0,void 0,m.props)}function j(m){return typeof m=="object"&&m!==null&&m.$$typeof===e}function X(m){var x={"=":"=0",":":"=2"};return"$"+m.replace(/[=:]/g,function(k){return x[k]})}var le=/\/+/g;function ce(m,x){return typeof m=="object"&&m!==null&&m.key!=null?X(""+m.key):x.toString(36)}function q(){}function Z(m){switch(m.status){case"fulfilled":return m.value;case"rejected":throw m.reason;default:switch(typeof m.status=="string"?m.then(q,q):(m.status="pending",m.then(function(x){m.status==="pending"&&(m.status="fulfilled",m.value=x)},function(x){m.status==="pending"&&(m.status="rejected",m.reason=x)})),m.status){case"fulfilled":return m.value;case"rejected":throw m.reason}}throw m}function re(m,x,k,N,W){var ee=typeof m;(ee==="undefined"||ee==="boolean")&&(m=null);var z=!1;if(m===null)z=!0;else switch(ee){case"bigint":case"string":case"number":z=!0;break;case"object":switch(m.$$typeof){case e:case t:z=!0;break;case f:return z=m._init,re(z(m._payload),x,k,N,W)}}if(z)return W=W(m),z=N===""?"."+ce(m,0):N,T(W)?(k="",z!=null&&(k=z.replace(le,"$&/")+"/"),re(W,x,k,"",function(It){return It})):W!=null&&(j(W)&&(W=J(W,k+(W.key==null||m&&m.key===W.key?"":(""+W.key).replace(le,"$&/")+"/")+z)),x.push(W)),1;z=0;var Le=N===""?".":N+":";if(T(m))for(var ae=0;ae<m.length;ae++)N=m[ae],ee=Le+ce(N,ae),z+=re(N,x,k,ee,W);else if(ae=h(m),typeof ae=="function")for(m=ae.call(m),ae=0;!(N=m.next()).done;)N=N.value,ee=Le+ce(N,ae++),z+=re(N,x,k,ee,W);else if(ee==="object"){if(typeof m.then=="function")return re(Z(m),x,k,N,W);throw x=String(m),Error("Objects are not valid as a React child (found: "+(x==="[object Object]"?"object with keys {"+Object.keys(m).join(", ")+"}":x)+"). If you meant to render a collection of children, use an array instead.")}return z}function G(m,x,k){if(m==null)return m;var N=[],W=0;return re(m,N,"","",function(ee){return x.call(k,ee,W++)}),N}function he(m){if(m._status===-1){var x=m._result;x=x(),x.then(function(k){(m._status===0||m._status===-1)&&(m._status=1,m._result=k)},function(k){(m._status===0||m._status===-1)&&(m._status=2,m._result=k)}),m._status===-1&&(m._status=0,m._result=x)}if(m._status===1)return m._result.default;throw m._result}var se=typeof reportError=="function"?reportError:function(m){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var x=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof m=="object"&&m!==null&&typeof m.message=="string"?String(m.message):String(m),error:m});if(!window.dispatchEvent(x))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",m);return}console.error(m)};function me(){}return B.Children={map:G,forEach:function(m,x,k){G(m,function(){x.apply(this,arguments)},k)},count:function(m){var x=0;return G(m,function(){x++}),x},toArray:function(m){return G(m,function(x){return x})||[]},only:function(m){if(!j(m))throw Error("React.Children.only expected to receive a single React element child.");return m}},B.Component=S,B.Fragment=r,B.Profiler=n,B.PureComponent=M,B.StrictMode=a,B.Suspense=i,B.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=L,B.__COMPILER_RUNTIME={__proto__:null,c:function(m){return L.H.useMemoCache(m)}},B.cache=function(m){return function(){return m.apply(null,arguments)}},B.cloneElement=function(m,x,k){if(m==null)throw Error("The argument must be a React element, but you passed "+m+".");var N=w({},m.props),W=m.key,ee=void 0;if(x!=null)for(z in x.ref!==void 0&&(ee=void 0),x.key!==void 0&&(W=""+x.key),x)!y.call(x,z)||z==="key"||z==="__self"||z==="__source"||z==="ref"&&x.ref===void 0||(N[z]=x[z]);var z=arguments.length-2;if(z===1)N.children=k;else if(1<z){for(var Le=Array(z),ae=0;ae<z;ae++)Le[ae]=arguments[ae+2];N.children=Le}return $(m.type,W,void 0,void 0,ee,N)},B.createContext=function(m){return m={$$typeof:l,_currentValue:m,_currentValue2:m,_threadCount:0,Provider:null,Consumer:null},m.Provider=m,m.Consumer={$$typeof:o,_context:m},m},B.createElement=function(m,x,k){var N,W={},ee=null;if(x!=null)for(N in x.key!==void 0&&(ee=""+x.key),x)y.call(x,N)&&N!=="key"&&N!=="__self"&&N!=="__source"&&(W[N]=x[N]);var z=arguments.length-2;if(z===1)W.children=k;else if(1<z){for(var Le=Array(z),ae=0;ae<z;ae++)Le[ae]=arguments[ae+2];W.children=Le}if(m&&m.defaultProps)for(N in z=m.defaultProps,z)W[N]===void 0&&(W[N]=z[N]);return $(m,ee,void 0,void 0,null,W)},B.createRef=function(){return{current:null}},B.forwardRef=function(m){return{$$typeof:s,render:m}},B.isValidElement=j,B.lazy=function(m){return{$$typeof:f,_payload:{_status:-1,_result:m},_init:he}},B.memo=function(m,x){return{$$typeof:u,type:m,compare:x===void 0?null:x}},B.startTransition=function(m){var x=L.T,k={};L.T=k;try{var N=m(),W=L.S;W!==null&&W(k,N),typeof N=="object"&&N!==null&&typeof N.then=="function"&&N.then(me,se)}catch(ee){se(ee)}finally{L.T=x}},B.unstable_useCacheRefresh=function(){return L.H.useCacheRefresh()},B.use=function(m){return L.H.use(m)},B.useActionState=function(m,x,k){return L.H.useActionState(m,x,k)},B.useCallback=function(m,x){return L.H.useCallback(m,x)},B.useContext=function(m){return L.H.useContext(m)},B.useDebugValue=function(){},B.useDeferredValue=function(m,x){return L.H.useDeferredValue(m,x)},B.useEffect=function(m,x,k){var N=L.H;if(typeof k=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return N.useEffect(m,x)},B.useId=function(){return L.H.useId()},B.useImperativeHandle=function(m,x,k){return L.H.useImperativeHandle(m,x,k)},B.useInsertionEffect=function(m,x){return L.H.useInsertionEffect(m,x)},B.useLayoutEffect=function(m,x){return L.H.useLayoutEffect(m,x)},B.useMemo=function(m,x){return L.H.useMemo(m,x)},B.useOptimistic=function(m,x){return L.H.useOptimistic(m,x)},B.useReducer=function(m,x,k){return L.H.useReducer(m,x,k)},B.useRef=function(m){return L.H.useRef(m)},B.useState=function(m){return L.H.useState(m)},B.useSyncExternalStore=function(m,x,k){return L.H.useSyncExternalStore(m,x,k)},B.useTransition=function(){return L.H.useTransition()},B.version="19.1.0",B}var $r;function va(){return $r||($r=1,zt.exports=ya()),zt.exports}var d=va();const ga=pa(d),fl=ma({__proto__:null,default:ga},[d]);var tt={},Fr;function wa(){if(Fr)return tt;Fr=1,Object.defineProperty(tt,"__esModule",{value:!0}),tt.parse=l,tt.serialize=u;const e=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,t=/^[\u0021-\u003A\u003C-\u007E]*$/,r=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,a=/^[\u0020-\u003A\u003D-\u007E]*$/,n=Object.prototype.toString,o=(()=>{const h=function(){};return h.prototype=Object.create(null),h})();function l(h,g){const w=new o,b=h.length;if(b<2)return w;const S=g?.decode||f;let R=0;do{const M=h.indexOf("=",R);if(M===-1)break;const P=h.indexOf(";",R),T=P===-1?b:P;if(M>T){R=h.lastIndexOf(";",M-1)+1;continue}const L=s(h,R,M),y=i(h,M,L),$=h.slice(L,y);if(w[$]===void 0){let J=s(h,M+1,T),j=i(h,T,J);const X=S(h.slice(J,j));w[$]=X}R=T+1}while(R<b);return w}function s(h,g,w){do{const b=h.charCodeAt(g);if(b!==32&&b!==9)return g}while(++g<w);return w}function i(h,g,w){for(;g>w;){const b=h.charCodeAt(--g);if(b!==32&&b!==9)return g+1}return w}function u(h,g,w){const b=w?.encode||encodeURIComponent;if(!e.test(h))throw new TypeError(`argument name is invalid: ${h}`);const S=b(g);if(!t.test(S))throw new TypeError(`argument val is invalid: ${g}`);let R=h+"="+S;if(!w)return R;if(w.maxAge!==void 0){if(!Number.isInteger(w.maxAge))throw new TypeError(`option maxAge is invalid: ${w.maxAge}`);R+="; Max-Age="+w.maxAge}if(w.domain){if(!r.test(w.domain))throw new TypeError(`option domain is invalid: ${w.domain}`);R+="; Domain="+w.domain}if(w.path){if(!a.test(w.path))throw new TypeError(`option path is invalid: ${w.path}`);R+="; Path="+w.path}if(w.expires){if(!p(w.expires)||!Number.isFinite(w.expires.valueOf()))throw new TypeError(`option expires is invalid: ${w.expires}`);R+="; Expires="+w.expires.toUTCString()}if(w.httpOnly&&(R+="; HttpOnly"),w.secure&&(R+="; Secure"),w.partitioned&&(R+="; Partitioned"),w.priority)switch(typeof w.priority=="string"?w.priority.toLowerCase():void 0){case"low":R+="; Priority=Low";break;case"medium":R+="; Priority=Medium";break;case"high":R+="; Priority=High";break;default:throw new TypeError(`option priority is invalid: ${w.priority}`)}if(w.sameSite)switch(typeof w.sameSite=="string"?w.sameSite.toLowerCase():w.sameSite){case!0:case"strict":R+="; SameSite=Strict";break;case"lax":R+="; SameSite=Lax";break;case"none":R+="; SameSite=None";break;default:throw new TypeError(`option sameSite is invalid: ${w.sameSite}`)}return R}function f(h){if(h.indexOf("%")===-1)return h;try{return decodeURIComponent(h)}catch{return h}}function p(h){return n.call(h)==="[object Date]"}return tt}wa();/**
 * react-router v7.6.3
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */var dn=e=>{throw TypeError(e)},Ea=(e,t,r)=>t.has(e)||dn("Cannot "+r),Bt=(e,t,r)=>(Ea(e,t,"read from private field"),r?r.call(e):t.get(e)),Ra=(e,t,r)=>t.has(e)?dn("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,r),jr="popstate";function hl(e={}){function t(a,n){let{pathname:o,search:l,hash:s}=a.location;return lt("",{pathname:o,search:l,hash:s},n.state&&n.state.usr||null,n.state&&n.state.key||"default")}function r(a,n){return typeof n=="string"?n:Ae(n)}return Sa(t,r,null,e)}function V(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function ne(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function ba(){return Math.random().toString(36).substring(2,10)}function Hr(e,t){return{usr:e.state,key:e.key,idx:t}}function lt(e,t,r=null,a){return{pathname:typeof e=="string"?e:e.pathname,search:"",hash:"",...typeof t=="string"?Ne(t):t,state:r,key:t&&t.key||a||ba()}}function Ae({pathname:e="/",search:t="",hash:r=""}){return t&&t!=="?"&&(e+=t.charAt(0)==="?"?t:"?"+t),r&&r!=="#"&&(e+=r.charAt(0)==="#"?r:"#"+r),e}function Ne(e){let t={};if(e){let r=e.indexOf("#");r>=0&&(t.hash=e.substring(r),e=e.substring(0,r));let a=e.indexOf("?");a>=0&&(t.search=e.substring(a),e=e.substring(0,a)),e&&(t.pathname=e)}return t}function Sa(e,t,r,a={}){let{window:n=document.defaultView,v5Compat:o=!1}=a,l=n.history,s="POP",i=null,u=f();u==null&&(u=0,l.replaceState({...l.state,idx:u},""));function f(){return(l.state||{idx:null}).idx}function p(){s="POP";let S=f(),R=S==null?null:S-u;u=S,i&&i({action:s,location:b.location,delta:R})}function h(S,R){s="PUSH";let M=lt(b.location,S,R);u=f()+1;let P=Hr(M,u),T=b.createHref(M);try{l.pushState(P,"",T)}catch(L){if(L instanceof DOMException&&L.name==="DataCloneError")throw L;n.location.assign(T)}o&&i&&i({action:s,location:b.location,delta:1})}function g(S,R){s="REPLACE";let M=lt(b.location,S,R);u=f();let P=Hr(M,u),T=b.createHref(M);l.replaceState(P,"",T),o&&i&&i({action:s,location:b.location,delta:0})}function w(S){return fn(S)}let b={get action(){return s},get location(){return e(n,l)},listen(S){if(i)throw new Error("A history only accepts one active listener");return n.addEventListener(jr,p),i=S,()=>{n.removeEventListener(jr,p),i=null}},createHref(S){return t(n,S)},createURL:w,encodeLocation(S){let R=w(S);return{pathname:R.pathname,search:R.search,hash:R.hash}},push:h,replace:g,go(S){return l.go(S)}};return b}function fn(e,t=!1){let r="http://localhost";typeof window<"u"&&(r=window.location.origin!=="null"?window.location.origin:window.location.href),V(r,"No window.location.(origin|href) available to create URL");let a=typeof e=="string"?e:Ae(e);return a=a.replace(/ $/,"%20"),!t&&a.startsWith("//")&&(a=r+a),new URL(a,r)}var ot,Ur=class{constructor(e){if(Ra(this,ot,new Map),e)for(let[t,r]of e)this.set(t,r)}get(e){if(Bt(this,ot).has(e))return Bt(this,ot).get(e);if(e.defaultValue!==void 0)return e.defaultValue;throw new Error("No value found for context")}set(e,t){Bt(this,ot).set(e,t)}};ot=new WeakMap;var xa=new Set(["lazy","caseSensitive","path","id","index","children"]);function La(e){return xa.has(e)}var Ca=new Set(["lazy","caseSensitive","path","id","index","unstable_middleware","children"]);function Pa(e){return Ca.has(e)}function Ma(e){return e.index===!0}function Mt(e,t,r=[],a={}){return e.map((n,o)=>{let l=[...r,String(o)],s=typeof n.id=="string"?n.id:l.join("-");if(V(n.index!==!0||!n.children,"Cannot specify children on an index route"),V(!a[s],`Found a route id collision on id "${s}".  Route id's must be globally unique within Data Router usages`),Ma(n)){let i={...n,...t(n),id:s};return a[s]=i,i}else{let i={...n,...t(n),id:s,children:void 0};return a[s]=i,n.children&&(i.children=Mt(n.children,t,l,a)),i}})}function Ce(e,t,r="/"){return St(e,t,r,!1)}function St(e,t,r,a){let n=typeof t=="string"?Ne(t):t,o=ge(n.pathname||"/",r);if(o==null)return null;let l=mn(e);Ta(l);let s=null;for(let i=0;s==null&&i<l.length;++i){let u=ja(o);s=Fa(l[i],u,a)}return s}function hn(e,t){let{route:r,pathname:a,params:n}=e;return{id:r.id,pathname:a,params:n,data:t[r.id],handle:r.handle}}function mn(e,t=[],r=[],a=""){let n=(o,l,s)=>{let i={relativePath:s===void 0?o.path||"":s,caseSensitive:o.caseSensitive===!0,childrenIndex:l,route:o};i.relativePath.startsWith("/")&&(V(i.relativePath.startsWith(a),`Absolute route path "${i.relativePath}" nested under path "${a}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),i.relativePath=i.relativePath.slice(a.length));let u=Pe([a,i.relativePath]),f=r.concat(i);o.children&&o.children.length>0&&(V(o.index!==!0,`Index routes must not have child routes. Please remove all child routes from route path "${u}".`),mn(o.children,t,f,u)),!(o.path==null&&!o.index)&&t.push({path:u,score:Ia(u,o.index),routesMeta:f})};return e.forEach((o,l)=>{if(o.path===""||!o.path?.includes("?"))n(o,l);else for(let s of pn(o.path))n(o,l,s)}),t}function pn(e){let t=e.split("/");if(t.length===0)return[];let[r,...a]=t,n=r.endsWith("?"),o=r.replace(/\?$/,"");if(a.length===0)return n?[o,""]:[o];let l=pn(a.join("/")),s=[];return s.push(...l.map(i=>i===""?o:[o,i].join("/"))),n&&s.push(...l),s.map(i=>e.startsWith("/")&&i===""?"/":i)}function Ta(e){e.sort((t,r)=>t.score!==r.score?r.score-t.score:$a(t.routesMeta.map(a=>a.childrenIndex),r.routesMeta.map(a=>a.childrenIndex)))}var _a=/^:[\w-]+$/,Da=3,Oa=2,Aa=1,ka=10,Na=-2,zr=e=>e==="*";function Ia(e,t){let r=e.split("/"),a=r.length;return r.some(zr)&&(a+=Na),t&&(a+=Oa),r.filter(n=>!zr(n)).reduce((n,o)=>n+(_a.test(o)?Da:o===""?Aa:ka),a)}function $a(e,t){return e.length===t.length&&e.slice(0,-1).every((a,n)=>a===t[n])?e[e.length-1]-t[t.length-1]:0}function Fa(e,t,r=!1){let{routesMeta:a}=e,n={},o="/",l=[];for(let s=0;s<a.length;++s){let i=a[s],u=s===a.length-1,f=o==="/"?t:t.slice(o.length)||"/",p=Tt({path:i.relativePath,caseSensitive:i.caseSensitive,end:u},f),h=i.route;if(!p&&u&&r&&!a[a.length-1].route.index&&(p=Tt({path:i.relativePath,caseSensitive:i.caseSensitive,end:!1},f)),!p)return null;Object.assign(n,p.params),l.push({params:n,pathname:Pe([o,p.pathname]),pathnameBase:za(Pe([o,p.pathnameBase])),route:h}),p.pathnameBase!=="/"&&(o=Pe([o,p.pathnameBase]))}return l}function Tt(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[r,a]=yn(e.path,e.caseSensitive,e.end),n=t.match(r);if(!n)return null;let o=n[0],l=o.replace(/(.)\/+$/,"$1"),s=n.slice(1);return{params:a.reduce((u,{paramName:f,isOptional:p},h)=>{if(f==="*"){let w=s[h]||"";l=o.slice(0,o.length-w.length).replace(/(.)\/+$/,"$1")}const g=s[h];return p&&!g?u[f]=void 0:u[f]=(g||"").replace(/%2F/g,"/"),u},{}),pathname:o,pathnameBase:l,pattern:e}}function yn(e,t=!1,r=!0){ne(e==="*"||!e.endsWith("*")||e.endsWith("/*"),`Route path "${e}" will be treated as if it were "${e.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${e.replace(/\*$/,"/*")}".`);let a=[],n="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(l,s,i)=>(a.push({paramName:s,isOptional:i!=null}),i?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(a.push({paramName:"*"}),n+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):r?n+="\\/*$":e!==""&&e!=="/"&&(n+="(?:(?=\\/|$))"),[new RegExp(n,t?void 0:"i"),a]}function ja(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return ne(!1,`The URL path "${e}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${t}).`),e}}function ge(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let r=t.endsWith("/")?t.length-1:t.length,a=e.charAt(r);return a&&a!=="/"?null:e.slice(r)||"/"}function Ha(e,t="/"){let{pathname:r,search:a="",hash:n=""}=typeof e=="string"?Ne(e):e;return{pathname:r?r.startsWith("/")?r:Ua(r,t):t,search:Ba(a),hash:Wa(n)}}function Ua(e,t){let r=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(n=>{n===".."?r.length>1&&r.pop():n!=="."&&r.push(n)}),r.length>1?r.join("/"):"/"}function Wt(e,t,r,a){return`Cannot include a '${e}' character in a manually specified \`to.${t}\` field [${JSON.stringify(a)}].  Please separate it out to the \`to.${r}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function vn(e){return e.filter((t,r)=>r===0||t.route.path&&t.route.path.length>0)}function sr(e){let t=vn(e);return t.map((r,a)=>a===t.length-1?r.pathname:r.pathnameBase)}function ur(e,t,r,a=!1){let n;typeof e=="string"?n=Ne(e):(n={...e},V(!n.pathname||!n.pathname.includes("?"),Wt("?","pathname","search",n)),V(!n.pathname||!n.pathname.includes("#"),Wt("#","pathname","hash",n)),V(!n.search||!n.search.includes("#"),Wt("#","search","hash",n)));let o=e===""||n.pathname==="",l=o?"/":n.pathname,s;if(l==null)s=r;else{let p=t.length-1;if(!a&&l.startsWith("..")){let h=l.split("/");for(;h[0]==="..";)h.shift(),p-=1;n.pathname=h.join("/")}s=p>=0?t[p]:"/"}let i=Ha(n,s),u=l&&l!=="/"&&l.endsWith("/"),f=(o||l===".")&&r.endsWith("/");return!i.pathname.endsWith("/")&&(u||f)&&(i.pathname+="/"),i}var Pe=e=>e.join("/").replace(/\/\/+/g,"/"),za=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),Ba=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,Wa=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e,Ya=class{constructor(e,t){this.type="DataWithResponseInit",this.data=e,this.init=t||null}};function Va(e,t){return new Ya(e,typeof t=="number"?{status:t}:t)}var Ja=(e,t=302)=>{let r=t;typeof r=="number"?r={status:r}:typeof r.status>"u"&&(r.status=302);let a=new Headers(r.headers);return a.set("Location",e),new Response(null,{...r,headers:a})},ke=class{constructor(e,t,r,a=!1){this.status=e,this.statusText=t||"",this.internal=a,r instanceof Error?(this.data=r.toString(),this.error=r):this.data=r}};function ze(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}var gn=["POST","PUT","PATCH","DELETE"],Ga=new Set(gn),Ka=["GET",...gn],Xa=new Set(Ka),qa=new Set([301,302,303,307,308]),Qa=new Set([307,308]),Yt={state:"idle",location:void 0,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0},Za={state:"idle",data:void 0,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0},rt={state:"unblocked",proceed:void 0,reset:void 0,location:void 0},cr=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,eo=e=>({hasErrorBoundary:!!e.hasErrorBoundary}),wn="remix-router-transitions",En=Symbol("ResetLoaderData");function ml(e){const t=e.window?e.window:typeof window<"u"?window:void 0,r=typeof t<"u"&&typeof t.document<"u"&&typeof t.document.createElement<"u";V(e.routes.length>0,"You must provide a non-empty routes array to createRouter");let a=e.hydrationRouteProperties||[],n=e.mapRouteProperties||eo,o={},l=Mt(e.routes,n,void 0,o),s,i=e.basename||"/",u=e.dataStrategy||oo,f={unstable_middleware:!1,...e.future},p=null,h=new Set,g=null,w=null,b=null,S=e.hydrationData!=null,R=Ce(l,e.history.location,i),M=!1,P=null,T;if(R==null&&!e.patchRoutesOnNavigation){let c=Re(404,{pathname:e.history.location.pathname}),{matches:v,route:E}=en(l);T=!0,R=v,P={[E.id]:c}}else if(R&&!e.hydrationData&&mt(R,l,e.history.location.pathname).active&&(R=null),R)if(R.some(c=>c.route.lazy))T=!1;else if(!R.some(c=>c.route.loader))T=!0;else{let c=e.hydrationData?e.hydrationData.loaderData:null,v=e.hydrationData?e.hydrationData.errors:null;if(v){let E=R.findIndex(C=>v[C.route.id]!==void 0);T=R.slice(0,E+1).every(C=>!Qt(C.route,c,v))}else T=R.every(E=>!Qt(E.route,c,v))}else{T=!1,R=[];let c=mt(null,l,e.history.location.pathname);c.active&&c.matches&&(M=!0,R=c.matches)}let L,y={historyAction:e.history.action,location:e.history.location,matches:R,initialized:T,navigation:Yt,restoreScrollPosition:e.hydrationData!=null?!1:null,preventScrollReset:!1,revalidation:"idle",loaderData:e.hydrationData&&e.hydrationData.loaderData||{},actionData:e.hydrationData&&e.hydrationData.actionData||null,errors:e.hydrationData&&e.hydrationData.errors||P,fetchers:new Map,blockers:new Map},$="POP",J=!1,j,X=!1,le=new Map,ce=null,q=!1,Z=!1,re=new Set,G=new Map,he=0,se=-1,me=new Map,m=new Set,x=new Map,k=new Map,N=new Set,W=new Map,ee,z=null;function Le(){if(p=e.history.listen(({action:c,location:v,delta:E})=>{if(ee){ee(),ee=void 0;return}ne(W.size===0||E!=null,"You are trying to use a blocker on a POP navigation to a location that was not created by @remix-run/router. This will fail silently in production. This can happen if you are navigating outside the router via `window.history.pushState`/`window.location.hash` instead of using router navigation APIs.  This can also happen if you are using createHashRouter and the user manually changes the URL.");let C=Or({currentLocation:y.location,nextLocation:v,historyAction:c});if(C&&E!=null){let _=new Promise(A=>{ee=A});e.history.go(E*-1),ht(C,{state:"blocked",location:v,proceed(){ht(C,{state:"proceeding",proceed:void 0,reset:void 0,location:v}),_.then(()=>e.history.go(E))},reset(){let A=new Map(y.blockers);A.set(C,rt),de({blockers:A})}});return}return Ie(c,v)}),r){vo(t,le);let c=()=>go(t,le);t.addEventListener("pagehide",c),ce=()=>t.removeEventListener("pagehide",c)}return y.initialized||Ie("POP",y.location,{initialHydration:!0}),L}function ae(){p&&p(),ce&&ce(),h.clear(),j&&j.abort(),y.fetchers.forEach((c,v)=>Ft(v)),y.blockers.forEach((c,v)=>Dr(v))}function It(c){return h.add(c),()=>h.delete(c)}function de(c,v={}){y={...y,...c};let E=[],C=[];y.fetchers.forEach((_,A)=>{_.state==="idle"&&(N.has(A)?E.push(A):C.push(A))}),N.forEach(_=>{!y.fetchers.has(_)&&!G.has(_)&&E.push(_)}),[...h].forEach(_=>_(y,{deletedFetchers:E,viewTransitionOpts:v.viewTransitionOpts,flushSync:v.flushSync===!0})),E.forEach(_=>Ft(_)),C.forEach(_=>y.fetchers.delete(_))}function We(c,v,{flushSync:E}={}){let C=y.actionData!=null&&y.navigation.formMethod!=null&&ve(y.navigation.formMethod)&&y.navigation.state==="loading"&&c.state?._isRedirect!==!0,_;v.actionData?Object.keys(v.actionData).length>0?_=v.actionData:_=null:C?_=y.actionData:_=null;let A=v.loaderData?Qr(y.loaderData,v.loaderData,v.matches||[],v.errors):y.loaderData,F=y.blockers;F.size>0&&(F=new Map(F),F.forEach((O,U)=>F.set(U,rt)));let D=J===!0||y.navigation.formMethod!=null&&ve(y.navigation.formMethod)&&c.state?._isRedirect!==!0;s&&(l=s,s=void 0),q||$==="POP"||($==="PUSH"?e.history.push(c,c.state):$==="REPLACE"&&e.history.replace(c,c.state));let I;if($==="POP"){let O=le.get(y.location.pathname);O&&O.has(c.pathname)?I={currentLocation:y.location,nextLocation:c}:le.has(c.pathname)&&(I={currentLocation:c,nextLocation:y.location})}else if(X){let O=le.get(y.location.pathname);O?O.add(c.pathname):(O=new Set([c.pathname]),le.set(y.location.pathname,O)),I={currentLocation:y.location,nextLocation:c}}de({...v,actionData:_,loaderData:A,historyAction:$,location:c,initialized:!0,navigation:Yt,revalidation:"idle",restoreScrollPosition:kr(c,v.matches||y.matches),preventScrollReset:D,blockers:F},{viewTransitionOpts:I,flushSync:E===!0}),$="POP",J=!1,X=!1,q=!1,Z=!1,z?.resolve(),z=null}async function xr(c,v){if(typeof c=="number"){e.history.go(c);return}let E=qt(y.location,y.matches,i,c,v?.fromRouteId,v?.relative),{path:C,submission:_,error:A}=Br(!1,E,v),F=y.location,D=lt(y.location,C,v&&v.state);D={...D,...e.history.encodeLocation(D)};let I=v&&v.replace!=null?v.replace:void 0,O="PUSH";I===!0?O="REPLACE":I===!1||_!=null&&ve(_.formMethod)&&_.formAction===y.location.pathname+y.location.search&&(O="REPLACE");let U=v&&"preventScrollReset"in v?v.preventScrollReset===!0:void 0,H=(v&&v.flushSync)===!0,Y=Or({currentLocation:F,nextLocation:D,historyAction:O});if(Y){ht(Y,{state:"blocked",location:D,proceed(){ht(Y,{state:"proceeding",proceed:void 0,reset:void 0,location:D}),xr(c,v)},reset(){let te=new Map(y.blockers);te.set(Y,rt),de({blockers:te})}});return}await Ie(O,D,{submission:_,pendingError:A,preventScrollReset:U,replace:v&&v.replace,enableViewTransition:v&&v.viewTransition,flushSync:H})}function ea(){z||(z=wo()),$t(),de({revalidation:"loading"});let c=z.promise;return y.navigation.state==="submitting"?c:y.navigation.state==="idle"?(Ie(y.historyAction,y.location,{startUninterruptedRevalidation:!0}),c):(Ie($||y.historyAction,y.navigation.location,{overrideNavigation:y.navigation,enableViewTransition:X===!0}),c)}async function Ie(c,v,E){j&&j.abort(),j=null,$=c,q=(E&&E.startUninterruptedRevalidation)===!0,ca(y.location,y.matches),J=(E&&E.preventScrollReset)===!0,X=(E&&E.enableViewTransition)===!0;let C=s||l,_=E&&E.overrideNavigation,A=E?.initialHydration&&y.matches&&y.matches.length>0&&!M?y.matches:Ce(C,v,i),F=(E&&E.flushSync)===!0;if(A&&y.initialized&&!Z&&fo(y.location,v)&&!(E&&E.submission&&ve(E.submission.formMethod))){We(v,{matches:A},{flushSync:F});return}let D=mt(A,C,v.pathname);if(D.active&&D.matches&&(A=D.matches),!A){let{error:ue,notFoundMatches:pe,route:K}=jt(v.pathname);We(v,{matches:pe,loaderData:{},errors:{[K.id]:ue}},{flushSync:F});return}j=new AbortController;let I=Ve(e.history,v,j.signal,E&&E.submission),O=new Ur(e.unstable_getContext?await e.unstable_getContext():void 0),U;if(E&&E.pendingError)U=[He(A).route.id,{type:"error",error:E.pendingError}];else if(E&&E.submission&&ve(E.submission.formMethod)){let ue=await ta(I,v,E.submission,A,O,D.active,E&&E.initialHydration===!0,{replace:E.replace,flushSync:F});if(ue.shortCircuited)return;if(ue.pendingActionResult){let[pe,K]=ue.pendingActionResult;if(ye(K)&&ze(K.error)&&K.error.status===404){j=null,We(v,{matches:ue.matches,loaderData:{},errors:{[pe]:K.error}});return}}A=ue.matches||A,U=ue.pendingActionResult,_=Vt(v,E.submission),F=!1,D.active=!1,I=Ve(e.history,I.url,I.signal)}let{shortCircuited:H,matches:Y,loaderData:te,errors:ie}=await ra(I,v,A,O,D.active,_,E&&E.submission,E&&E.fetcherSubmission,E&&E.replace,E&&E.initialHydration===!0,F,U);H||(j=null,We(v,{matches:Y||A,...Zr(U),loaderData:te,errors:ie}))}async function ta(c,v,E,C,_,A,F,D={}){$t();let I=po(v,E);if(de({navigation:I},{flushSync:D.flushSync===!0}),A){let H=await pt(C,v.pathname,c.signal);if(H.type==="aborted")return{shortCircuited:!0};if(H.type==="error"){let Y=He(H.partialMatches).route.id;return{matches:H.partialMatches,pendingActionResult:[Y,{type:"error",error:H.error}]}}else if(H.matches)C=H.matches;else{let{notFoundMatches:Y,error:te,route:ie}=jt(v.pathname);return{matches:Y,pendingActionResult:[ie.id,{type:"error",error:te}]}}}let O,U=it(C,v);if(!U.route.action&&!U.route.lazy)O={type:"error",error:Re(405,{method:c.method,pathname:v.pathname,routeId:U.route.id})};else{let H=Je(n,o,c,C,U,F?[]:a,_),Y=await Xe(c,H,_,null);if(O=Y[U.route.id],!O){for(let te of C)if(Y[te.route.id]){O=Y[te.route.id];break}}if(c.signal.aborted)return{shortCircuited:!0}}if(Ue(O)){let H;return D&&D.replace!=null?H=D.replace:H=Kr(O.response.headers.get("Location"),new URL(c.url),i)===y.location.pathname+y.location.search,await $e(c,O,!0,{submission:E,replace:H}),{shortCircuited:!0}}if(ye(O)){let H=He(C,U.route.id);return(D&&D.replace)!==!0&&($="PUSH"),{matches:C,pendingActionResult:[H.route.id,O,U.route.id]}}return{matches:C,pendingActionResult:[U.route.id,O]}}async function ra(c,v,E,C,_,A,F,D,I,O,U,H){let Y=A||Vt(v,F),te=F||D||tn(Y),ie=!q&&!O;if(_){if(ie){let Ee=Lr(H);de({navigation:Y,...Ee!==void 0?{actionData:Ee}:{}},{flushSync:U})}let Q=await pt(E,v.pathname,c.signal);if(Q.type==="aborted")return{shortCircuited:!0};if(Q.type==="error"){let Ee=He(Q.partialMatches).route.id;return{matches:Q.partialMatches,loaderData:{},errors:{[Ee]:Q.error}}}else if(Q.matches)E=Q.matches;else{let{error:Ee,notFoundMatches:De,route:vt}=jt(v.pathname);return{matches:De,loaderData:{},errors:{[vt.id]:Ee}}}}let ue=s||l,{dsMatches:pe,revalidatingFetchers:K}=Wr(c,C,n,o,e.history,y,E,te,v,O?[]:a,O===!0,Z,re,N,x,m,ue,i,e.patchRoutesOnNavigation!=null,H);if(se=++he,!e.dataStrategy&&!pe.some(Q=>Q.shouldLoad)&&K.length===0){let Q=Tr();return We(v,{matches:E,loaderData:{},errors:H&&ye(H[1])?{[H[0]]:H[1].error}:null,...Zr(H),...Q?{fetchers:new Map(y.fetchers)}:{}},{flushSync:U}),{shortCircuited:!0}}if(ie){let Q={};if(!_){Q.navigation=Y;let Ee=Lr(H);Ee!==void 0&&(Q.actionData=Ee)}K.length>0&&(Q.fetchers=na(K)),de(Q,{flushSync:U})}K.forEach(Q=>{_e(Q.key),Q.controller&&G.set(Q.key,Q.controller)});let qe=()=>K.forEach(Q=>_e(Q.key));j&&j.signal.addEventListener("abort",qe);let{loaderResults:Fe,fetcherResults:Qe}=await Cr(pe,K,c,C);if(c.signal.aborted)return{shortCircuited:!0};j&&j.signal.removeEventListener("abort",qe),K.forEach(Q=>G.delete(Q.key));let we=gt(Fe);if(we)return await $e(c,we.result,!0,{replace:I}),{shortCircuited:!0};if(we=gt(Qe),we)return m.add(we.key),await $e(c,we.result,!0,{replace:I}),{shortCircuited:!0};let{loaderData:Ze,errors:et}=qr(y,E,Fe,H,K,Qe);O&&y.errors&&(et={...y.errors,...et});let Ht=Tr(),je=_r(se),yt=Ht||je||K.length>0;return{matches:E,loaderData:Ze,errors:et,...yt?{fetchers:new Map(y.fetchers)}:{}}}function Lr(c){if(c&&!ye(c[1]))return{[c[0]]:c[1].data};if(y.actionData)return Object.keys(y.actionData).length===0?null:y.actionData}function na(c){return c.forEach(v=>{let E=y.fetchers.get(v.key),C=nt(void 0,E?E.data:void 0);y.fetchers.set(v.key,C)}),new Map(y.fetchers)}async function aa(c,v,E,C){_e(c);let _=(C&&C.flushSync)===!0,A=s||l,F=qt(y.location,y.matches,i,E,v,C?.relative),D=Ce(A,F,i),I=mt(D,A,F);if(I.active&&I.matches&&(D=I.matches),!D){Me(c,v,Re(404,{pathname:F}),{flushSync:_});return}let{path:O,submission:U,error:H}=Br(!0,F,C);if(H){Me(c,v,H,{flushSync:_});return}let Y=it(D,O),te=new Ur(e.unstable_getContext?await e.unstable_getContext():void 0),ie=(C&&C.preventScrollReset)===!0;if(U&&ve(U.formMethod)){await oa(c,v,O,Y,D,te,I.active,_,ie,U);return}x.set(c,{routeId:v,path:O}),await ia(c,v,O,Y,D,te,I.active,_,ie,U)}async function oa(c,v,E,C,_,A,F,D,I,O){$t(),x.delete(c);function U(oe){if(!oe.route.action&&!oe.route.lazy){let Ye=Re(405,{method:O.formMethod,pathname:E,routeId:v});return Me(c,v,Ye,{flushSync:D}),!0}return!1}if(!F&&U(C))return;let H=y.fetchers.get(c);Te(c,yo(O,H),{flushSync:D});let Y=new AbortController,te=Ve(e.history,E,Y.signal,O);if(F){let oe=await pt(_,E,te.signal,c);if(oe.type==="aborted")return;if(oe.type==="error"){Me(c,v,oe.error,{flushSync:D});return}else if(oe.matches){if(_=oe.matches,C=it(_,E),U(C))return}else{Me(c,v,Re(404,{pathname:E}),{flushSync:D});return}}G.set(c,Y);let ie=he,ue=Je(n,o,te,_,C,a,A),K=(await Xe(te,ue,A,c))[C.route.id];if(te.signal.aborted){G.get(c)===Y&&G.delete(c);return}if(N.has(c)){if(Ue(K)||ye(K)){Te(c,Oe(void 0));return}}else{if(Ue(K))if(G.delete(c),se>ie){Te(c,Oe(void 0));return}else return m.add(c),Te(c,nt(O)),$e(te,K,!1,{fetcherSubmission:O,preventScrollReset:I});if(ye(K)){Me(c,v,K.error);return}}let qe=y.navigation.location||y.location,Fe=Ve(e.history,qe,Y.signal),Qe=s||l,we=y.navigation.state!=="idle"?Ce(Qe,y.navigation.location,i):y.matches;V(we,"Didn't find any matches after fetcher action");let Ze=++he;me.set(c,Ze);let et=nt(O,K.data);y.fetchers.set(c,et);let{dsMatches:Ht,revalidatingFetchers:je}=Wr(Fe,A,n,o,e.history,y,we,O,qe,a,!1,Z,re,N,x,m,Qe,i,e.patchRoutesOnNavigation!=null,[C.route.id,K]);je.filter(oe=>oe.key!==c).forEach(oe=>{let Ye=oe.key,Nr=y.fetchers.get(Ye),ha=nt(void 0,Nr?Nr.data:void 0);y.fetchers.set(Ye,ha),_e(Ye),oe.controller&&G.set(Ye,oe.controller)}),de({fetchers:new Map(y.fetchers)});let yt=()=>je.forEach(oe=>_e(oe.key));Y.signal.addEventListener("abort",yt);let{loaderResults:Q,fetcherResults:Ee}=await Cr(Ht,je,Fe,A);if(Y.signal.aborted)return;if(Y.signal.removeEventListener("abort",yt),me.delete(c),G.delete(c),je.forEach(oe=>G.delete(oe.key)),y.fetchers.has(c)){let oe=Oe(K.data);y.fetchers.set(c,oe)}let De=gt(Q);if(De)return $e(Fe,De.result,!1,{preventScrollReset:I});if(De=gt(Ee),De)return m.add(De.key),$e(Fe,De.result,!1,{preventScrollReset:I});let{loaderData:vt,errors:Ut}=qr(y,we,Q,void 0,je,Ee);_r(Ze),y.navigation.state==="loading"&&Ze>se?(V($,"Expected pending action"),j&&j.abort(),We(y.navigation.location,{matches:we,loaderData:vt,errors:Ut,fetchers:new Map(y.fetchers)})):(de({errors:Ut,loaderData:Qr(y.loaderData,vt,we,Ut),fetchers:new Map(y.fetchers)}),Z=!1)}async function ia(c,v,E,C,_,A,F,D,I,O){let U=y.fetchers.get(c);Te(c,nt(O,U?U.data:void 0),{flushSync:D});let H=new AbortController,Y=Ve(e.history,E,H.signal);if(F){let K=await pt(_,E,Y.signal,c);if(K.type==="aborted")return;if(K.type==="error"){Me(c,v,K.error,{flushSync:D});return}else if(K.matches)_=K.matches,C=it(_,E);else{Me(c,v,Re(404,{pathname:E}),{flushSync:D});return}}G.set(c,H);let te=he,ie=Je(n,o,Y,_,C,a,A),pe=(await Xe(Y,ie,A,c))[C.route.id];if(G.get(c)===H&&G.delete(c),!Y.signal.aborted){if(N.has(c)){Te(c,Oe(void 0));return}if(Ue(pe))if(se>te){Te(c,Oe(void 0));return}else{m.add(c),await $e(Y,pe,!1,{preventScrollReset:I});return}if(ye(pe)){Me(c,v,pe.error);return}Te(c,Oe(pe.data))}}async function $e(c,v,E,{submission:C,fetcherSubmission:_,preventScrollReset:A,replace:F}={}){v.response.headers.has("X-Remix-Revalidate")&&(Z=!0);let D=v.response.headers.get("Location");V(D,"Expected a Location header on the redirect Response"),D=Kr(D,new URL(c.url),i);let I=lt(y.location,D,{_isRedirect:!0});if(r){let ie=!1;if(v.response.headers.has("X-Remix-Reload-Document"))ie=!0;else if(cr.test(D)){const ue=fn(D,!0);ie=ue.origin!==t.location.origin||ge(ue.pathname,i)==null}if(ie){F?t.location.replace(D):t.location.assign(D);return}}j=null;let O=F===!0||v.response.headers.has("X-Remix-Replace")?"REPLACE":"PUSH",{formMethod:U,formAction:H,formEncType:Y}=y.navigation;!C&&!_&&U&&H&&Y&&(C=tn(y.navigation));let te=C||_;if(Qa.has(v.response.status)&&te&&ve(te.formMethod))await Ie(O,I,{submission:{...te,formAction:D},preventScrollReset:A||J,enableViewTransition:E?X:void 0});else{let ie=Vt(I,C);await Ie(O,I,{overrideNavigation:ie,fetcherSubmission:_,preventScrollReset:A||J,enableViewTransition:E?X:void 0})}}async function Xe(c,v,E,C){let _,A={};try{_=await io(u,c,v,C,E,!1)}catch(F){return v.filter(D=>D.shouldLoad).forEach(D=>{A[D.route.id]={type:"error",error:F}}),A}if(c.signal.aborted)return A;for(let[F,D]of Object.entries(_))if(ho(D)){let I=D.result;A[F]={type:"redirect",response:uo(I,c,F,v,i)}}else A[F]=await so(D);return A}async function Cr(c,v,E,C){let _=Xe(E,c,C,null),A=Promise.all(v.map(async I=>{if(I.matches&&I.match&&I.request&&I.controller){let U=(await Xe(I.request,I.matches,C,I.key))[I.match.route.id];return{[I.key]:U}}else return Promise.resolve({[I.key]:{type:"error",error:Re(404,{pathname:I.path})}})})),F=await _,D=(await A).reduce((I,O)=>Object.assign(I,O),{});return{loaderResults:F,fetcherResults:D}}function $t(){Z=!0,x.forEach((c,v)=>{G.has(v)&&re.add(v),_e(v)})}function Te(c,v,E={}){y.fetchers.set(c,v),de({fetchers:new Map(y.fetchers)},{flushSync:(E&&E.flushSync)===!0})}function Me(c,v,E,C={}){let _=He(y.matches,v);Ft(c),de({errors:{[_.route.id]:E},fetchers:new Map(y.fetchers)},{flushSync:(C&&C.flushSync)===!0})}function Pr(c){return k.set(c,(k.get(c)||0)+1),N.has(c)&&N.delete(c),y.fetchers.get(c)||Za}function Ft(c){let v=y.fetchers.get(c);G.has(c)&&!(v&&v.state==="loading"&&me.has(c))&&_e(c),x.delete(c),me.delete(c),m.delete(c),N.delete(c),re.delete(c),y.fetchers.delete(c)}function la(c){let v=(k.get(c)||0)-1;v<=0?(k.delete(c),N.add(c)):k.set(c,v),de({fetchers:new Map(y.fetchers)})}function _e(c){let v=G.get(c);v&&(v.abort(),G.delete(c))}function Mr(c){for(let v of c){let E=Pr(v),C=Oe(E.data);y.fetchers.set(v,C)}}function Tr(){let c=[],v=!1;for(let E of m){let C=y.fetchers.get(E);V(C,`Expected fetcher: ${E}`),C.state==="loading"&&(m.delete(E),c.push(E),v=!0)}return Mr(c),v}function _r(c){let v=[];for(let[E,C]of me)if(C<c){let _=y.fetchers.get(E);V(_,`Expected fetcher: ${E}`),_.state==="loading"&&(_e(E),me.delete(E),v.push(E))}return Mr(v),v.length>0}function sa(c,v){let E=y.blockers.get(c)||rt;return W.get(c)!==v&&W.set(c,v),E}function Dr(c){y.blockers.delete(c),W.delete(c)}function ht(c,v){let E=y.blockers.get(c)||rt;V(E.state==="unblocked"&&v.state==="blocked"||E.state==="blocked"&&v.state==="blocked"||E.state==="blocked"&&v.state==="proceeding"||E.state==="blocked"&&v.state==="unblocked"||E.state==="proceeding"&&v.state==="unblocked",`Invalid blocker state transition: ${E.state} -> ${v.state}`);let C=new Map(y.blockers);C.set(c,v),de({blockers:C})}function Or({currentLocation:c,nextLocation:v,historyAction:E}){if(W.size===0)return;W.size>1&&ne(!1,"A router only supports one blocker at a time");let C=Array.from(W.entries()),[_,A]=C[C.length-1],F=y.blockers.get(_);if(!(F&&F.state==="proceeding")&&A({currentLocation:c,nextLocation:v,historyAction:E}))return _}function jt(c){let v=Re(404,{pathname:c}),E=s||l,{matches:C,route:_}=en(E);return{notFoundMatches:C,route:_,error:v}}function ua(c,v,E){if(g=c,b=v,w=E||null,!S&&y.navigation===Yt){S=!0;let C=kr(y.location,y.matches);C!=null&&de({restoreScrollPosition:C})}return()=>{g=null,b=null,w=null}}function Ar(c,v){return w&&w(c,v.map(C=>hn(C,y.loaderData)))||c.key}function ca(c,v){if(g&&b){let E=Ar(c,v);g[E]=b()}}function kr(c,v){if(g){let E=Ar(c,v),C=g[E];if(typeof C=="number")return C}return null}function mt(c,v,E){if(e.patchRoutesOnNavigation)if(c){if(Object.keys(c[0].params).length>0)return{active:!0,matches:St(v,E,i,!0)}}else return{active:!0,matches:St(v,E,i,!0)||[]};return{active:!1,matches:null}}async function pt(c,v,E,C){if(!e.patchRoutesOnNavigation)return{type:"success",matches:c};let _=c;for(;;){let A=s==null,F=s||l,D=o;try{await e.patchRoutesOnNavigation({signal:E,path:v,matches:_,fetcherKey:C,patch:(U,H)=>{E.aborted||Yr(U,H,F,D,n)}})}catch(U){return{type:"error",error:U,partialMatches:_}}finally{A&&!E.aborted&&(l=[...l])}if(E.aborted)return{type:"aborted"};let I=Ce(F,v,i);if(I)return{type:"success",matches:I};let O=St(F,v,i,!0);if(!O||_.length===O.length&&_.every((U,H)=>U.route.id===O[H].route.id))return{type:"success",matches:null};_=O}}function da(c){o={},s=Mt(c,n,void 0,o)}function fa(c,v){let E=s==null;Yr(c,v,s||l,o,n),E&&(l=[...l],de({}))}return L={get basename(){return i},get future(){return f},get state(){return y},get routes(){return l},get window(){return t},initialize:Le,subscribe:It,enableScrollRestoration:ua,navigate:xr,fetch:aa,revalidate:ea,createHref:c=>e.history.createHref(c),encodeLocation:c=>e.history.encodeLocation(c),getFetcher:Pr,deleteFetcher:la,dispose:ae,getBlocker:sa,deleteBlocker:Dr,patchRoutes:fa,_internalFetchControllers:G,_internalSetRoutes:da},L}function to(e){return e!=null&&("formData"in e&&e.formData!=null||"body"in e&&e.body!==void 0)}function qt(e,t,r,a,n,o){let l,s;if(n){l=[];for(let u of t)if(l.push(u),u.route.id===n){s=u;break}}else l=t,s=t[t.length-1];let i=ur(a||".",sr(l),ge(e.pathname,r)||e.pathname,o==="path");if(a==null&&(i.search=e.search,i.hash=e.hash),(a==null||a===""||a===".")&&s){let u=fr(i.search);if(s.route.index&&!u)i.search=i.search?i.search.replace(/^\?/,"?index&"):"?index";else if(!s.route.index&&u){let f=new URLSearchParams(i.search),p=f.getAll("index");f.delete("index"),p.filter(g=>g).forEach(g=>f.append("index",g));let h=f.toString();i.search=h?`?${h}`:""}}return r!=="/"&&(i.pathname=i.pathname==="/"?r:Pe([r,i.pathname])),Ae(i)}function Br(e,t,r){if(!r||!to(r))return{path:t};if(r.formMethod&&!mo(r.formMethod))return{path:t,error:Re(405,{method:r.formMethod})};let a=()=>({path:t,error:Re(400,{type:"invalid-body"})}),o=(r.formMethod||"get").toUpperCase(),l=Cn(t);if(r.body!==void 0){if(r.formEncType==="text/plain"){if(!ve(o))return a();let p=typeof r.body=="string"?r.body:r.body instanceof FormData||r.body instanceof URLSearchParams?Array.from(r.body.entries()).reduce((h,[g,w])=>`${h}${g}=${w}
`,""):String(r.body);return{path:t,submission:{formMethod:o,formAction:l,formEncType:r.formEncType,formData:void 0,json:void 0,text:p}}}else if(r.formEncType==="application/json"){if(!ve(o))return a();try{let p=typeof r.body=="string"?JSON.parse(r.body):r.body;return{path:t,submission:{formMethod:o,formAction:l,formEncType:r.formEncType,formData:void 0,json:p,text:void 0}}}catch{return a()}}}V(typeof FormData=="function","FormData is not available in this environment");let s,i;if(r.formData)s=er(r.formData),i=r.formData;else if(r.body instanceof FormData)s=er(r.body),i=r.body;else if(r.body instanceof URLSearchParams)s=r.body,i=Xr(s);else if(r.body==null)s=new URLSearchParams,i=new FormData;else try{s=new URLSearchParams(r.body),i=Xr(s)}catch{return a()}let u={formMethod:o,formAction:l,formEncType:r&&r.formEncType||"application/x-www-form-urlencoded",formData:i,json:void 0,text:void 0};if(ve(u.formMethod))return{path:t,submission:u};let f=Ne(t);return e&&f.search&&fr(f.search)&&s.append("index",""),f.search=`?${s}`,{path:Ae(f),submission:u}}function Wr(e,t,r,a,n,o,l,s,i,u,f,p,h,g,w,b,S,R,M,P){let T=P?ye(P[1])?P[1].error:P[1].data:void 0,L=n.createURL(o.location),y=n.createURL(i),$;if(f&&o.errors){let q=Object.keys(o.errors)[0];$=l.findIndex(Z=>Z.route.id===q)}else if(P&&ye(P[1])){let q=P[0];$=l.findIndex(Z=>Z.route.id===q)-1}let J=P?P[1].statusCode:void 0,j=J&&J>=400,X={currentUrl:L,currentParams:o.matches[0]?.params||{},nextUrl:y,nextParams:l[0].params,...s,actionResult:T,actionStatus:J},le=l.map((q,Z)=>{let{route:re}=q,G=null;if($!=null&&Z>$?G=!1:re.lazy?G=!0:re.loader==null?G=!1:f?G=Qt(re,o.loaderData,o.errors):ro(o.loaderData,o.matches[Z],q)&&(G=!0),G!==null)return Zt(r,a,e,q,u,t,G);let he=j?!1:p||L.pathname+L.search===y.pathname+y.search||L.search!==y.search||no(o.matches[Z],q),se={...X,defaultShouldRevalidate:he},me=_t(q,se);return Zt(r,a,e,q,u,t,me,se)}),ce=[];return w.forEach((q,Z)=>{if(f||!l.some(k=>k.route.id===q.routeId)||g.has(Z))return;let re=o.fetchers.get(Z),G=re&&re.state!=="idle"&&re.data===void 0,he=Ce(S,q.path,R);if(!he){if(M&&G)return;ce.push({key:Z,routeId:q.routeId,path:q.path,matches:null,match:null,request:null,controller:null});return}if(b.has(Z))return;let se=it(he,q.path),me=new AbortController,m=Ve(n,q.path,me.signal),x=null;if(h.has(Z))h.delete(Z),x=Je(r,a,m,he,se,u,t);else if(G)p&&(x=Je(r,a,m,he,se,u,t));else{let k={...X,defaultShouldRevalidate:j?!1:p};_t(se,k)&&(x=Je(r,a,m,he,se,u,t,k))}x&&ce.push({key:Z,routeId:q.routeId,path:q.path,matches:x,match:se,request:m,controller:me})}),{dsMatches:le,revalidatingFetchers:ce}}function Qt(e,t,r){if(e.lazy)return!0;if(!e.loader)return!1;let a=t!=null&&e.id in t,n=r!=null&&r[e.id]!==void 0;return!a&&n?!1:typeof e.loader=="function"&&e.loader.hydrate===!0?!0:!a&&!n}function ro(e,t,r){let a=!t||r.route.id!==t.route.id,n=!e.hasOwnProperty(r.route.id);return a||n}function no(e,t){let r=e.route.path;return e.pathname!==t.pathname||r!=null&&r.endsWith("*")&&e.params["*"]!==t.params["*"]}function _t(e,t){if(e.route.shouldRevalidate){let r=e.route.shouldRevalidate(t);if(typeof r=="boolean")return r}return t.defaultShouldRevalidate}function Yr(e,t,r,a,n){let o;if(e){let i=a[e];V(i,`No route found to patch children into: routeId = ${e}`),i.children||(i.children=[]),o=i.children}else o=r;let l=t.filter(i=>!o.some(u=>Rn(i,u))),s=Mt(l,n,[e||"_","patch",String(o?.length||"0")],a);o.push(...s)}function Rn(e,t){return"id"in e&&"id"in t&&e.id===t.id?!0:e.index===t.index&&e.path===t.path&&e.caseSensitive===t.caseSensitive?(!e.children||e.children.length===0)&&(!t.children||t.children.length===0)?!0:e.children.every((r,a)=>t.children?.some(n=>Rn(r,n))):!1}var Vr=new WeakMap,bn=({key:e,route:t,manifest:r,mapRouteProperties:a})=>{let n=r[t.id];if(V(n,"No route found in manifest"),!n.lazy||typeof n.lazy!="object")return;let o=n.lazy[e];if(!o)return;let l=Vr.get(n);l||(l={},Vr.set(n,l));let s=l[e];if(s)return s;let i=(async()=>{let u=La(e),p=n[e]!==void 0&&e!=="hasErrorBoundary";if(u)ne(!u,"Route property "+e+" is not a supported lazy route property. This property will be ignored."),l[e]=Promise.resolve();else if(p)ne(!1,`Route "${n.id}" has a static property "${e}" defined. The lazy property will be ignored.`);else{let h=await o();h!=null&&(Object.assign(n,{[e]:h}),Object.assign(n,a(n)))}typeof n.lazy=="object"&&(n.lazy[e]=void 0,Object.values(n.lazy).every(h=>h===void 0)&&(n.lazy=void 0))})();return l[e]=i,i},Jr=new WeakMap;function ao(e,t,r,a,n){let o=r[e.id];if(V(o,"No route found in manifest"),!e.lazy)return{lazyRoutePromise:void 0,lazyHandlerPromise:void 0};if(typeof e.lazy=="function"){let f=Jr.get(o);if(f)return{lazyRoutePromise:f,lazyHandlerPromise:f};let p=(async()=>{V(typeof e.lazy=="function","No lazy route function found");let h=await e.lazy(),g={};for(let w in h){let b=h[w];if(b===void 0)continue;let S=Pa(w),M=o[w]!==void 0&&w!=="hasErrorBoundary";S?ne(!S,"Route property "+w+" is not a supported property to be returned from a lazy route function. This property will be ignored."):M?ne(!M,`Route "${o.id}" has a static property "${w}" defined but its lazy function is also returning a value for this property. The lazy route property "${w}" will be ignored.`):g[w]=b}Object.assign(o,g),Object.assign(o,{...a(o),lazy:void 0})})();return Jr.set(o,p),p.catch(()=>{}),{lazyRoutePromise:p,lazyHandlerPromise:p}}let l=Object.keys(e.lazy),s=[],i;for(let f of l){if(n&&n.includes(f))continue;let p=bn({key:f,route:e,manifest:r,mapRouteProperties:a});p&&(s.push(p),f===t&&(i=p))}let u=s.length>0?Promise.all(s).then(()=>{}):void 0;return u?.catch(()=>{}),i?.catch(()=>{}),{lazyRoutePromise:u,lazyHandlerPromise:i}}async function Gr(e){let t=e.matches.filter(n=>n.shouldLoad),r={};return(await Promise.all(t.map(n=>n.resolve()))).forEach((n,o)=>{r[t[o].route.id]=n}),r}async function oo(e){return e.matches.some(t=>t.route.unstable_middleware)?Sn(e,!1,()=>Gr(e),(t,r)=>({[r]:{type:"error",result:t}})):Gr(e)}async function Sn(e,t,r,a){let{matches:n,request:o,params:l,context:s}=e,i={handlerResult:void 0};try{let u=n.flatMap(p=>p.route.unstable_middleware?p.route.unstable_middleware.map(h=>[p.route.id,h]):[]),f=await xn({request:o,params:l,context:s},u,t,i,r);return t?f:i.handlerResult}catch(u){if(!i.middlewareError)throw u;let f=await a(i.middlewareError.error,i.middlewareError.routeId);return i.handlerResult?Object.assign(i.handlerResult,f):f}}async function xn(e,t,r,a,n,o=0){let{request:l}=e;if(l.signal.aborted)throw l.signal.reason?l.signal.reason:new Error(`Request aborted without an \`AbortSignal.reason\`: ${l.method} ${l.url}`);let s=t[o];if(!s)return a.handlerResult=await n(),a.handlerResult;let[i,u]=s,f=!1,p,h=async()=>{if(f)throw new Error("You may only call `next()` once per middleware");f=!0,await xn(e,t,r,a,n,o+1)};try{let g=await u({request:e.request,params:e.params,context:e.context},h);return f?g===void 0?p:g:h()}catch(g){throw a.middlewareError?a.middlewareError.error!==g&&(a.middlewareError={routeId:i,error:g}):a.middlewareError={routeId:i,error:g},g}}function Ln(e,t,r,a,n){let o=bn({key:"unstable_middleware",route:a.route,manifest:t,mapRouteProperties:e}),l=ao(a.route,ve(r.method)?"action":"loader",t,e,n);return{middleware:o,route:l.lazyRoutePromise,handler:l.lazyHandlerPromise}}function Zt(e,t,r,a,n,o,l,s=null){let i=!1,u=Ln(e,t,r,a,n);return{...a,_lazyPromises:u,shouldLoad:l,unstable_shouldRevalidateArgs:s,unstable_shouldCallHandler(f){return i=!0,s?typeof f=="boolean"?_t(a,{...s,defaultShouldRevalidate:f}):_t(a,s):l},resolve(f){return i||l||f&&r.method==="GET"&&(a.route.lazy||a.route.loader)?lo({request:r,match:a,lazyHandlerPromise:u?.handler,lazyRoutePromise:u?.route,handlerOverride:f,scopedContext:o}):Promise.resolve({type:"data",result:void 0})}}}function Je(e,t,r,a,n,o,l,s=null){return a.map(i=>i.route.id!==n.route.id?{...i,shouldLoad:!1,unstable_shouldRevalidateArgs:s,unstable_shouldCallHandler:()=>!1,_lazyPromises:Ln(e,t,r,i,o),resolve:()=>Promise.resolve({type:"data",result:void 0})}:Zt(e,t,r,i,o,l,!0,s))}async function io(e,t,r,a,n,o){r.some(u=>u._lazyPromises?.middleware)&&await Promise.all(r.map(u=>u._lazyPromises?.middleware));let l={request:t,params:r[0].params,context:n,matches:r},i=await e({...l,fetcherKey:a,unstable_runClientMiddleware:u=>{let f=l;return Sn(f,!1,()=>u({...f,fetcherKey:a,unstable_runClientMiddleware:()=>{throw new Error("Cannot call `unstable_runClientMiddleware()` from within an `unstable_runClientMiddleware` handler")}}),(p,h)=>({[h]:{type:"error",result:p}}))}});try{await Promise.all(r.flatMap(u=>[u._lazyPromises?.handler,u._lazyPromises?.route]))}catch{}return i}async function lo({request:e,match:t,lazyHandlerPromise:r,lazyRoutePromise:a,handlerOverride:n,scopedContext:o}){let l,s,i=ve(e.method),u=i?"action":"loader",f=p=>{let h,g=new Promise((S,R)=>h=R);s=()=>h(),e.signal.addEventListener("abort",s);let w=S=>typeof p!="function"?Promise.reject(new Error(`You cannot call the handler for a route which defines a boolean "${u}" [routeId: ${t.route.id}]`)):p({request:e,params:t.params,context:o},...S!==void 0?[S]:[]),b=(async()=>{try{return{type:"data",result:await(n?n(R=>w(R)):w())}}catch(S){return{type:"error",result:S}}})();return Promise.race([b,g])};try{let p=i?t.route.action:t.route.loader;if(r||a)if(p){let h,[g]=await Promise.all([f(p).catch(w=>{h=w}),r,a]);if(h!==void 0)throw h;l=g}else{await r;let h=i?t.route.action:t.route.loader;if(h)[l]=await Promise.all([f(h),a]);else if(u==="action"){let g=new URL(e.url),w=g.pathname+g.search;throw Re(405,{method:e.method,pathname:w,routeId:t.route.id})}else return{type:"data",result:void 0}}else if(p)l=await f(p);else{let h=new URL(e.url),g=h.pathname+h.search;throw Re(404,{pathname:g})}}catch(p){return{type:"error",result:p}}finally{s&&e.signal.removeEventListener("abort",s)}return l}async function so(e){let{result:t,type:r}=e;if(dr(t)){let a;try{let n=t.headers.get("Content-Type");n&&/\bapplication\/json\b/.test(n)?t.body==null?a=null:a=await t.json():a=await t.text()}catch(n){return{type:"error",error:n}}return r==="error"?{type:"error",error:new ke(t.status,t.statusText,a),statusCode:t.status,headers:t.headers}:{type:"data",data:a,statusCode:t.status,headers:t.headers}}return r==="error"?tr(t)?t.data instanceof Error?{type:"error",error:t.data,statusCode:t.init?.status,headers:t.init?.headers?new Headers(t.init.headers):void 0}:{type:"error",error:new ke(t.init?.status||500,void 0,t.data),statusCode:ze(t)?t.status:void 0,headers:t.init?.headers?new Headers(t.init.headers):void 0}:{type:"error",error:t,statusCode:ze(t)?t.status:void 0}:tr(t)?{type:"data",data:t.data,statusCode:t.init?.status,headers:t.init?.headers?new Headers(t.init.headers):void 0}:{type:"data",data:t}}function uo(e,t,r,a,n){let o=e.headers.get("Location");if(V(o,"Redirects returned/thrown from loaders/actions must have a Location header"),!cr.test(o)){let l=a.slice(0,a.findIndex(s=>s.route.id===r)+1);o=qt(new URL(t.url),l,n,o),e.headers.set("Location",o)}return e}function Kr(e,t,r){if(cr.test(e)){let a=e,n=a.startsWith("//")?new URL(t.protocol+a):new URL(a),o=ge(n.pathname,r)!=null;if(n.origin===t.origin&&o)return n.pathname+n.search+n.hash}return e}function Ve(e,t,r,a){let n=e.createURL(Cn(t)).toString(),o={signal:r};if(a&&ve(a.formMethod)){let{formMethod:l,formEncType:s}=a;o.method=l.toUpperCase(),s==="application/json"?(o.headers=new Headers({"Content-Type":s}),o.body=JSON.stringify(a.json)):s==="text/plain"?o.body=a.text:s==="application/x-www-form-urlencoded"&&a.formData?o.body=er(a.formData):o.body=a.formData}return new Request(n,o)}function er(e){let t=new URLSearchParams;for(let[r,a]of e.entries())t.append(r,typeof a=="string"?a:a.name);return t}function Xr(e){let t=new FormData;for(let[r,a]of e.entries())t.append(r,a);return t}function co(e,t,r,a=!1,n=!1){let o={},l=null,s,i=!1,u={},f=r&&ye(r[1])?r[1].error:void 0;return e.forEach(p=>{if(!(p.route.id in t))return;let h=p.route.id,g=t[h];if(V(!Ue(g),"Cannot handle redirect results in processLoaderData"),ye(g)){let w=g.error;if(f!==void 0&&(w=f,f=void 0),l=l||{},n)l[h]=w;else{let b=He(e,h);l[b.route.id]==null&&(l[b.route.id]=w)}a||(o[h]=En),i||(i=!0,s=ze(g.error)?g.error.status:500),g.headers&&(u[h]=g.headers)}else o[h]=g.data,g.statusCode&&g.statusCode!==200&&!i&&(s=g.statusCode),g.headers&&(u[h]=g.headers)}),f!==void 0&&r&&(l={[r[0]]:f},r[2]&&(o[r[2]]=void 0)),{loaderData:o,errors:l,statusCode:s||200,loaderHeaders:u}}function qr(e,t,r,a,n,o){let{loaderData:l,errors:s}=co(t,r,a);return n.filter(i=>!i.matches||i.matches.some(u=>u.shouldLoad)).forEach(i=>{let{key:u,match:f,controller:p}=i,h=o[u];if(V(h,"Did not find corresponding fetcher result"),!(p&&p.signal.aborted))if(ye(h)){let g=He(e.matches,f?.route.id);s&&s[g.route.id]||(s={...s,[g.route.id]:h.error}),e.fetchers.delete(u)}else if(Ue(h))V(!1,"Unhandled fetcher revalidation redirect");else{let g=Oe(h.data);e.fetchers.set(u,g)}}),{loaderData:l,errors:s}}function Qr(e,t,r,a){let n=Object.entries(t).filter(([,o])=>o!==En).reduce((o,[l,s])=>(o[l]=s,o),{});for(let o of r){let l=o.route.id;if(!t.hasOwnProperty(l)&&e.hasOwnProperty(l)&&o.route.loader&&(n[l]=e[l]),a&&a.hasOwnProperty(l))break}return n}function Zr(e){return e?ye(e[1])?{actionData:{}}:{actionData:{[e[0]]:e[1].data}}:{}}function He(e,t){return(t?e.slice(0,e.findIndex(a=>a.route.id===t)+1):[...e]).reverse().find(a=>a.route.hasErrorBoundary===!0)||e[0]}function en(e){let t=e.length===1?e[0]:e.find(r=>r.index||!r.path||r.path==="/")||{id:"__shim-error-route__"};return{matches:[{params:{},pathname:"",pathnameBase:"",route:t}],route:t}}function Re(e,{pathname:t,routeId:r,method:a,type:n,message:o}={}){let l="Unknown Server Error",s="Unknown @remix-run/router error";return e===400?(l="Bad Request",a&&t&&r?s=`You made a ${a} request to "${t}" but did not provide a \`loader\` for route "${r}", so there is no way to handle the request.`:n==="invalid-body"&&(s="Unable to encode submission body")):e===403?(l="Forbidden",s=`Route "${r}" does not match URL "${t}"`):e===404?(l="Not Found",s=`No route matches URL "${t}"`):e===405&&(l="Method Not Allowed",a&&t&&r?s=`You made a ${a.toUpperCase()} request to "${t}" but did not provide an \`action\` for route "${r}", so there is no way to handle the request.`:a&&(s=`Invalid request method "${a.toUpperCase()}"`)),new ke(e||500,l,new Error(s),!0)}function gt(e){let t=Object.entries(e);for(let r=t.length-1;r>=0;r--){let[a,n]=t[r];if(Ue(n))return{key:a,result:n}}}function Cn(e){let t=typeof e=="string"?Ne(e):e;return Ae({...t,hash:""})}function fo(e,t){return e.pathname!==t.pathname||e.search!==t.search?!1:e.hash===""?t.hash!=="":e.hash===t.hash?!0:t.hash!==""}function ho(e){return dr(e.result)&&qa.has(e.result.status)}function ye(e){return e.type==="error"}function Ue(e){return(e&&e.type)==="redirect"}function tr(e){return typeof e=="object"&&e!=null&&"type"in e&&"data"in e&&"init"in e&&e.type==="DataWithResponseInit"}function dr(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.headers=="object"&&typeof e.body<"u"}function mo(e){return Xa.has(e.toUpperCase())}function ve(e){return Ga.has(e.toUpperCase())}function fr(e){return new URLSearchParams(e).getAll("index").some(t=>t==="")}function it(e,t){let r=typeof t=="string"?Ne(t).search:t.search;if(e[e.length-1].route.index&&fr(r||""))return e[e.length-1];let a=vn(e);return a[a.length-1]}function tn(e){let{formMethod:t,formAction:r,formEncType:a,text:n,formData:o,json:l}=e;if(!(!t||!r||!a)){if(n!=null)return{formMethod:t,formAction:r,formEncType:a,formData:void 0,json:void 0,text:n};if(o!=null)return{formMethod:t,formAction:r,formEncType:a,formData:o,json:void 0,text:void 0};if(l!==void 0)return{formMethod:t,formAction:r,formEncType:a,formData:void 0,json:l,text:void 0}}}function Vt(e,t){return t?{state:"loading",location:e,formMethod:t.formMethod,formAction:t.formAction,formEncType:t.formEncType,formData:t.formData,json:t.json,text:t.text}:{state:"loading",location:e,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0}}function po(e,t){return{state:"submitting",location:e,formMethod:t.formMethod,formAction:t.formAction,formEncType:t.formEncType,formData:t.formData,json:t.json,text:t.text}}function nt(e,t){return e?{state:"loading",formMethod:e.formMethod,formAction:e.formAction,formEncType:e.formEncType,formData:e.formData,json:e.json,text:e.text,data:t}:{state:"loading",formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0,data:t}}function yo(e,t){return{state:"submitting",formMethod:e.formMethod,formAction:e.formAction,formEncType:e.formEncType,formData:e.formData,json:e.json,text:e.text,data:t?t.data:void 0}}function Oe(e){return{state:"idle",formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0,data:e}}function vo(e,t){try{let r=e.sessionStorage.getItem(wn);if(r){let a=JSON.parse(r);for(let[n,o]of Object.entries(a||{}))o&&Array.isArray(o)&&t.set(n,new Set(o||[]))}}catch{}}function go(e,t){if(t.size>0){let r={};for(let[a,n]of t)r[a]=[...n];try{e.sessionStorage.setItem(wn,JSON.stringify(r))}catch(a){ne(!1,`Failed to save applied view transitions in sessionStorage (${a}).`)}}}function wo(){let e,t,r=new Promise((a,n)=>{e=async o=>{a(o);try{await r}catch{}},t=async o=>{n(o);try{await r}catch{}}});return{promise:r,resolve:e,reject:t}}var Be=d.createContext(null);Be.displayName="DataRouter";var Ge=d.createContext(null);Ge.displayName="DataRouterState";var hr=d.createContext({isTransitioning:!1});hr.displayName="ViewTransition";var Pn=d.createContext(new Map);Pn.displayName="Fetchers";var Eo=d.createContext(null);Eo.displayName="Await";var be=d.createContext(null);be.displayName="Navigation";var Ot=d.createContext(null);Ot.displayName="Location";var Se=d.createContext({outlet:null,matches:[],isDataRoute:!1});Se.displayName="Route";var mr=d.createContext(null);mr.displayName="RouteError";function Ro(e,{relative:t}={}){V(ut(),"useHref() may be used only in the context of a <Router> component.");let{basename:r,navigator:a}=d.useContext(be),{hash:n,pathname:o,search:l}=ct(e,{relative:t}),s=o;return r!=="/"&&(s=o==="/"?r:Pe([r,o])),a.createHref({pathname:s,search:l,hash:n})}function ut(){return d.useContext(Ot)!=null}function xe(){return V(ut(),"useLocation() may be used only in the context of a <Router> component."),d.useContext(Ot).location}var Mn="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function Tn(e){d.useContext(be).static||d.useLayoutEffect(e)}function _n(){let{isDataRoute:e}=d.useContext(Se);return e?Fo():bo()}function bo(){V(ut(),"useNavigate() may be used only in the context of a <Router> component.");let e=d.useContext(Be),{basename:t,navigator:r}=d.useContext(be),{matches:a}=d.useContext(Se),{pathname:n}=xe(),o=JSON.stringify(sr(a)),l=d.useRef(!1);return Tn(()=>{l.current=!0}),d.useCallback((i,u={})=>{if(ne(l.current,Mn),!l.current)return;if(typeof i=="number"){r.go(i);return}let f=ur(i,JSON.parse(o),n,u.relative==="path");e==null&&t!=="/"&&(f.pathname=f.pathname==="/"?t:Pe([t,f.pathname])),(u.replace?r.replace:r.push)(f,u.state,u)},[t,r,o,n,e])}var So=d.createContext(null);function xo(e){let t=d.useContext(Se).outlet;return t&&d.createElement(So.Provider,{value:e},t)}function Lo(){let{matches:e}=d.useContext(Se),t=e[e.length-1];return t?t.params:{}}function ct(e,{relative:t}={}){let{matches:r}=d.useContext(Se),{pathname:a}=xe(),n=JSON.stringify(sr(r));return d.useMemo(()=>ur(e,JSON.parse(n),a,t==="path"),[e,n,a,t])}function Co(e,t,r,a){V(ut(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:n}=d.useContext(be),{matches:o}=d.useContext(Se),l=o[o.length-1],s=l?l.params:{},i=l?l.pathname:"/",u=l?l.pathnameBase:"/",f=l&&l.route;{let R=f&&f.path||"";On(i,!f||R.endsWith("*")||R.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${i}" (under <Route path="${R}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.

Please change the parent <Route path="${R}"> to <Route path="${R==="/"?"*":`${R}/*`}">.`)}let p=xe(),h;h=p;let g=h.pathname||"/",w=g;if(u!=="/"){let R=u.replace(/^\//,"").split("/");w="/"+g.replace(/^\//,"").split("/").slice(R.length).join("/")}let b=Ce(e,{pathname:w});return ne(f||b!=null,`No routes matched location "${h.pathname}${h.search}${h.hash}" `),ne(b==null||b[b.length-1].route.element!==void 0||b[b.length-1].route.Component!==void 0||b[b.length-1].route.lazy!==void 0,`Matched leaf route at location "${h.pathname}${h.search}${h.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`),Do(b&&b.map(R=>Object.assign({},R,{params:Object.assign({},s,R.params),pathname:Pe([u,n.encodeLocation?n.encodeLocation(R.pathname).pathname:R.pathname]),pathnameBase:R.pathnameBase==="/"?u:Pe([u,n.encodeLocation?n.encodeLocation(R.pathnameBase).pathname:R.pathnameBase])})),o,r,a)}function Po(){let e=Dn(),t=ze(e)?`${e.status} ${e.statusText}`:e instanceof Error?e.message:JSON.stringify(e),r=e instanceof Error?e.stack:null,a="rgba(200,200,200, 0.5)",n={padding:"0.5rem",backgroundColor:a},o={padding:"2px 4px",backgroundColor:a},l=null;return console.error("Error handled by React Router default ErrorBoundary:",e),l=d.createElement(d.Fragment,null,d.createElement("p",null,"💿 Hey developer 👋"),d.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",d.createElement("code",{style:o},"ErrorBoundary")," or"," ",d.createElement("code",{style:o},"errorElement")," prop on your route.")),d.createElement(d.Fragment,null,d.createElement("h2",null,"Unexpected Application Error!"),d.createElement("h3",{style:{fontStyle:"italic"}},t),r?d.createElement("pre",{style:n},r):null,l)}var Mo=d.createElement(Po,null),To=class extends d.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||t.revalidation!=="idle"&&e.revalidation==="idle"?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:e.error!==void 0?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return this.state.error!==void 0?d.createElement(Se.Provider,{value:this.props.routeContext},d.createElement(mr.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function _o({routeContext:e,match:t,children:r}){let a=d.useContext(Be);return a&&a.static&&a.staticContext&&(t.route.errorElement||t.route.ErrorBoundary)&&(a.staticContext._deepestRenderedBoundaryId=t.route.id),d.createElement(Se.Provider,{value:e},r)}function Do(e,t=[],r=null,a=null){if(e==null){if(!r)return null;if(r.errors)e=r.matches;else if(t.length===0&&!r.initialized&&r.matches.length>0)e=r.matches;else return null}let n=e,o=r?.errors;if(o!=null){let i=n.findIndex(u=>u.route.id&&o?.[u.route.id]!==void 0);V(i>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(o).join(",")}`),n=n.slice(0,Math.min(n.length,i+1))}let l=!1,s=-1;if(r)for(let i=0;i<n.length;i++){let u=n[i];if((u.route.HydrateFallback||u.route.hydrateFallbackElement)&&(s=i),u.route.id){let{loaderData:f,errors:p}=r,h=u.route.loader&&!f.hasOwnProperty(u.route.id)&&(!p||p[u.route.id]===void 0);if(u.route.lazy||h){l=!0,s>=0?n=n.slice(0,s+1):n=[n[0]];break}}}return n.reduceRight((i,u,f)=>{let p,h=!1,g=null,w=null;r&&(p=o&&u.route.id?o[u.route.id]:void 0,g=u.route.errorElement||Mo,l&&(s<0&&f===0?(On("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),h=!0,w=null):s===f&&(h=!0,w=u.route.hydrateFallbackElement||null)));let b=t.concat(n.slice(0,f+1)),S=()=>{let R;return p?R=g:h?R=w:u.route.Component?R=d.createElement(u.route.Component,null):u.route.element?R=u.route.element:R=i,d.createElement(_o,{match:u,routeContext:{outlet:i,matches:b,isDataRoute:r!=null},children:R})};return r&&(u.route.ErrorBoundary||u.route.errorElement||f===0)?d.createElement(To,{location:r.location,revalidation:r.revalidation,component:g,error:p,children:S(),routeContext:{outlet:null,matches:b,isDataRoute:!0}}):S()},null)}function pr(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function Oo(e){let t=d.useContext(Be);return V(t,pr(e)),t}function dt(e){let t=d.useContext(Ge);return V(t,pr(e)),t}function Ao(e){let t=d.useContext(Se);return V(t,pr(e)),t}function ft(e){let t=Ao(e),r=t.matches[t.matches.length-1];return V(r.route.id,`${e} can only be used on routes that contain a unique "id"`),r.route.id}function ko(){return ft("useRouteId")}function No(){return dt("useNavigation").navigation}function yr(){let{matches:e,loaderData:t}=dt("useMatches");return d.useMemo(()=>e.map(r=>hn(r,t)),[e,t])}function Io(){let e=dt("useLoaderData"),t=ft("useLoaderData");return e.loaderData[t]}function $o(){let e=dt("useActionData"),t=ft("useLoaderData");return e.actionData?e.actionData[t]:void 0}function Dn(){let e=d.useContext(mr),t=dt("useRouteError"),r=ft("useRouteError");return e!==void 0?e:t.errors?.[r]}function Fo(){let{router:e}=Oo("useNavigate"),t=ft("useNavigate"),r=d.useRef(!1);return Tn(()=>{r.current=!0}),d.useCallback(async(n,o={})=>{ne(r.current,Mn),r.current&&(typeof n=="number"?e.navigate(n):await e.navigate(n,{fromRouteId:t,...o}))},[e,t])}var rn={};function On(e,t,r){!t&&!rn[e]&&(rn[e]=!0,ne(!1,r))}var nn={};function an(e,t){!e&&!nn[t]&&(nn[t]=!0,console.warn(t))}function pl(e){let t={hasErrorBoundary:e.hasErrorBoundary||e.ErrorBoundary!=null||e.errorElement!=null};return e.Component&&(e.element&&ne(!1,"You should not include both `Component` and `element` on your route - `Component` will be used."),Object.assign(t,{element:d.createElement(e.Component),Component:void 0})),e.HydrateFallback&&(e.hydrateFallbackElement&&ne(!1,"You should not include both `HydrateFallback` and `hydrateFallbackElement` on your route - `HydrateFallback` will be used."),Object.assign(t,{hydrateFallbackElement:d.createElement(e.HydrateFallback),HydrateFallback:void 0})),e.ErrorBoundary&&(e.errorElement&&ne(!1,"You should not include both `ErrorBoundary` and `errorElement` on your route - `ErrorBoundary` will be used."),Object.assign(t,{errorElement:d.createElement(e.ErrorBoundary),ErrorBoundary:void 0})),t}var yl=["HydrateFallback","hydrateFallbackElement"],jo=class{constructor(){this.status="pending",this.promise=new Promise((e,t)=>{this.resolve=r=>{this.status==="pending"&&(this.status="resolved",e(r))},this.reject=r=>{this.status==="pending"&&(this.status="rejected",t(r))}})}};function vl({router:e,flushSync:t}){let[r,a]=d.useState(e.state),[n,o]=d.useState(),[l,s]=d.useState({isTransitioning:!1}),[i,u]=d.useState(),[f,p]=d.useState(),[h,g]=d.useState(),w=d.useRef(new Map),b=d.useCallback((P,{deletedFetchers:T,flushSync:L,viewTransitionOpts:y})=>{P.fetchers.forEach((J,j)=>{J.data!==void 0&&w.current.set(j,J.data)}),T.forEach(J=>w.current.delete(J)),an(L===!1||t!=null,'You provided the `flushSync` option to a router update, but you are not using the `<RouterProvider>` from `react-router/dom` so `ReactDOM.flushSync()` is unavailable.  Please update your app to `import { RouterProvider } from "react-router/dom"` and ensure you have `react-dom` installed as a dependency to use the `flushSync` option.');let $=e.window!=null&&e.window.document!=null&&typeof e.window.document.startViewTransition=="function";if(an(y==null||$,"You provided the `viewTransition` option to a router update, but you do not appear to be running in a DOM environment as `window.startViewTransition` is not available."),!y||!$){t&&L?t(()=>a(P)):d.startTransition(()=>a(P));return}if(t&&L){t(()=>{f&&(i&&i.resolve(),f.skipTransition()),s({isTransitioning:!0,flushSync:!0,currentLocation:y.currentLocation,nextLocation:y.nextLocation})});let J=e.window.document.startViewTransition(()=>{t(()=>a(P))});J.finished.finally(()=>{t(()=>{u(void 0),p(void 0),o(void 0),s({isTransitioning:!1})})}),t(()=>p(J));return}f?(i&&i.resolve(),f.skipTransition(),g({state:P,currentLocation:y.currentLocation,nextLocation:y.nextLocation})):(o(P),s({isTransitioning:!0,flushSync:!1,currentLocation:y.currentLocation,nextLocation:y.nextLocation}))},[e.window,t,f,i]);d.useLayoutEffect(()=>e.subscribe(b),[e,b]),d.useEffect(()=>{l.isTransitioning&&!l.flushSync&&u(new jo)},[l]),d.useEffect(()=>{if(i&&n&&e.window){let P=n,T=i.promise,L=e.window.document.startViewTransition(async()=>{d.startTransition(()=>a(P)),await T});L.finished.finally(()=>{u(void 0),p(void 0),o(void 0),s({isTransitioning:!1})}),p(L)}},[n,i,e.window]),d.useEffect(()=>{i&&n&&r.location.key===n.location.key&&i.resolve()},[i,f,r.location,n]),d.useEffect(()=>{!l.isTransitioning&&h&&(o(h.state),s({isTransitioning:!0,flushSync:!1,currentLocation:h.currentLocation,nextLocation:h.nextLocation}),g(void 0))},[l.isTransitioning,h]);let S=d.useMemo(()=>({createHref:e.createHref,encodeLocation:e.encodeLocation,go:P=>e.navigate(P),push:(P,T,L)=>e.navigate(P,{state:T,preventScrollReset:L?.preventScrollReset}),replace:(P,T,L)=>e.navigate(P,{replace:!0,state:T,preventScrollReset:L?.preventScrollReset})}),[e]),R=e.basename||"/",M=d.useMemo(()=>({router:e,navigator:S,static:!1,basename:R}),[e,S,R]);return d.createElement(d.Fragment,null,d.createElement(Be.Provider,{value:M},d.createElement(Ge.Provider,{value:r},d.createElement(Pn.Provider,{value:w.current},d.createElement(hr.Provider,{value:l},d.createElement(zo,{basename:R,location:r.location,navigationType:r.historyAction,navigator:S},d.createElement(Ho,{routes:e.routes,future:e.future,state:r})))))),null)}var Ho=d.memo(Uo);function Uo({routes:e,future:t,state:r}){return Co(e,void 0,r,t)}function gl(e){return xo(e.context)}function zo({basename:e="/",children:t=null,location:r,navigationType:a="POP",navigator:n,static:o=!1}){V(!ut(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let l=e.replace(/^\/*/,"/"),s=d.useMemo(()=>({basename:l,navigator:n,static:o,future:{}}),[l,n,o]);typeof r=="string"&&(r=Ne(r));let{pathname:i="/",search:u="",hash:f="",state:p=null,key:h="default"}=r,g=d.useMemo(()=>{let w=ge(i,l);return w==null?null:{location:{pathname:w,search:u,hash:f,state:p,key:h},navigationType:a}},[l,i,u,f,p,h,a]);return ne(g!=null,`<Router basename="${l}"> is not able to match the URL "${i}${u}${f}" because it does not start with the basename, so the <Router> won't render anything.`),g==null?null:d.createElement(be.Provider,{value:s},d.createElement(Ot.Provider,{children:t,value:g}))}function wl(e){return function(){const r={params:Lo(),loaderData:Io(),actionData:$o(),matches:yr()};return d.createElement(e,r)}}var xt="get",Lt="application/x-www-form-urlencoded";function At(e){return e!=null&&typeof e.tagName=="string"}function Bo(e){return At(e)&&e.tagName.toLowerCase()==="button"}function Wo(e){return At(e)&&e.tagName.toLowerCase()==="form"}function Yo(e){return At(e)&&e.tagName.toLowerCase()==="input"}function Vo(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function Jo(e,t){return e.button===0&&(!t||t==="_self")&&!Vo(e)}function rr(e=""){return new URLSearchParams(typeof e=="string"||Array.isArray(e)||e instanceof URLSearchParams?e:Object.keys(e).reduce((t,r)=>{let a=e[r];return t.concat(Array.isArray(a)?a.map(n=>[r,n]):[[r,a]])},[]))}function Go(e,t){let r=rr(e);return t&&t.forEach((a,n)=>{r.has(n)||t.getAll(n).forEach(o=>{r.append(n,o)})}),r}var wt=null;function Ko(){if(wt===null)try{new FormData(document.createElement("form"),0),wt=!1}catch{wt=!0}return wt}var Xo=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function Jt(e){return e!=null&&!Xo.has(e)?(ne(!1,`"${e}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${Lt}"`),null):e}function qo(e,t){let r,a,n,o,l;if(Wo(e)){let s=e.getAttribute("action");a=s?ge(s,t):null,r=e.getAttribute("method")||xt,n=Jt(e.getAttribute("enctype"))||Lt,o=new FormData(e)}else if(Bo(e)||Yo(e)&&(e.type==="submit"||e.type==="image")){let s=e.form;if(s==null)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let i=e.getAttribute("formaction")||s.getAttribute("action");if(a=i?ge(i,t):null,r=e.getAttribute("formmethod")||s.getAttribute("method")||xt,n=Jt(e.getAttribute("formenctype"))||Jt(s.getAttribute("enctype"))||Lt,o=new FormData(s,e),!Ko()){let{name:u,type:f,value:p}=e;if(f==="image"){let h=u?`${u}.`:"";o.append(`${h}x`,"0"),o.append(`${h}y`,"0")}else u&&o.append(u,p)}}else{if(At(e))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');r=xt,a=null,n=Lt,l=e}return o&&n==="text/plain"&&(l=o,o=void 0),{action:a,method:r.toLowerCase(),encType:n,formData:o,body:l}}function fe(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}async function An(e,t){if(e.id in t)return t[e.id];try{let r=await import(e.module);return t[e.id]=r,r}catch(r){return console.error(`Error loading route module \`${e.module}\`, reloading page...`),console.error(r),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}function Qo(e,t,r){let a=e.map(o=>{let l=t[o.route.id],s=r.routes[o.route.id];return[s&&s.css?s.css.map(i=>({rel:"stylesheet",href:i})):[],l?.links?.()||[]]}).flat(2),n=gr(e,r);return $n(a,n)}function kn(e){return e.css?e.css.map(t=>({rel:"stylesheet",href:t})):[]}async function Zo(e){if(!e.css)return;let t=kn(e);await Promise.all(t.map(In))}async function Nn(e,t){if(!e.css&&!t.links||!ai())return;let r=[];if(e.css&&r.push(...kn(e)),t.links&&r.push(...t.links()),r.length===0)return;let a=[];for(let n of r)!vr(n)&&n.rel==="stylesheet"&&a.push({...n,rel:"preload",as:"style"});await Promise.all(a.map(In))}async function In(e){return new Promise(t=>{if(e.media&&!window.matchMedia(e.media).matches||document.querySelector(`link[rel="stylesheet"][href="${e.href}"]`))return t();let r=document.createElement("link");Object.assign(r,e);function a(){document.head.contains(r)&&document.head.removeChild(r)}r.onload=()=>{a(),t()},r.onerror=()=>{a(),t()},document.head.appendChild(r)})}function vr(e){return e!=null&&typeof e.page=="string"}function ei(e){return e==null?!1:e.href==null?e.rel==="preload"&&typeof e.imageSrcSet=="string"&&typeof e.imageSizes=="string":typeof e.rel=="string"&&typeof e.href=="string"}async function ti(e,t,r){let a=await Promise.all(e.map(async n=>{let o=t.routes[n.route.id];if(o){let l=await An(o,r);return l.links?l.links():[]}return[]}));return $n(a.flat(1).filter(ei).filter(n=>n.rel==="stylesheet"||n.rel==="preload").map(n=>n.rel==="stylesheet"?{...n,rel:"prefetch",as:"style"}:{...n,rel:"prefetch"}))}function on(e,t,r,a,n,o){let l=(i,u)=>r[u]?i.route.id!==r[u].route.id:!0,s=(i,u)=>r[u].pathname!==i.pathname||r[u].route.path?.endsWith("*")&&r[u].params["*"]!==i.params["*"];return o==="assets"?t.filter((i,u)=>l(i,u)||s(i,u)):o==="data"?t.filter((i,u)=>{let f=a.routes[i.route.id];if(!f||!f.hasLoader)return!1;if(l(i,u)||s(i,u))return!0;if(i.route.shouldRevalidate){let p=i.route.shouldRevalidate({currentUrl:new URL(n.pathname+n.search+n.hash,window.origin),currentParams:r[0]?.params||{},nextUrl:new URL(e,window.origin),nextParams:i.params,defaultShouldRevalidate:!0});if(typeof p=="boolean")return p}return!0}):[]}function gr(e,t,{includeHydrateFallback:r}={}){return ri(e.map(a=>{let n=t.routes[a.route.id];if(!n)return[];let o=[n.module];return n.clientActionModule&&(o=o.concat(n.clientActionModule)),n.clientLoaderModule&&(o=o.concat(n.clientLoaderModule)),r&&n.hydrateFallbackModule&&(o=o.concat(n.hydrateFallbackModule)),n.imports&&(o=o.concat(n.imports)),o}).flat(1))}function ri(e){return[...new Set(e)]}function ni(e){let t={},r=Object.keys(e).sort();for(let a of r)t[a]=e[a];return t}function $n(e,t){let r=new Set,a=new Set(t);return e.reduce((n,o)=>{if(t&&!vr(o)&&o.as==="script"&&o.href&&a.has(o.href))return n;let s=JSON.stringify(ni(o));return r.has(s)||(r.add(s),n.push({key:s,link:o})),n},[])}var Et;function ai(){if(Et!==void 0)return Et;let e=document.createElement("link");return Et=e.relList.supports("preload"),e=null,Et}function ln(e){return{__html:e}}var oi=-1,ii=-2,li=-3,si=-4,ui=-5,ci=-6,di=-7,fi="B",hi="D",Fn="E",mi="M",pi="N",jn="P",yi="R",vi="S",gi="Y",wi="U",Ei="Z",Hn=class{constructor(){this.promise=new Promise((e,t)=>{this.resolve=e,this.reject=t})}};function Ri(){const e=new TextDecoder;let t="";return new TransformStream({transform(r,a){const n=e.decode(r,{stream:!0}),o=(t+n).split(`
`);t=o.pop()||"";for(const l of o)a.enqueue(l)},flush(r){t&&r.enqueue(t)}})}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");var Gt=typeof window<"u"?window:typeof globalThis<"u"?globalThis:void 0;function nr(e){const{hydrated:t,values:r}=this;if(typeof e=="number")return sn.call(this,e);if(!Array.isArray(e)||!e.length)throw new SyntaxError;const a=r.length;for(const n of e)r.push(n);return t.length=r.length,sn.call(this,a)}function sn(e){const{hydrated:t,values:r,deferred:a,plugins:n}=this;let o;const l=[[e,i=>{o=i}]];let s=[];for(;l.length>0;){const[i,u]=l.pop();switch(i){case di:u(void 0);continue;case ui:u(null);continue;case ii:u(NaN);continue;case ci:u(1/0);continue;case li:u(-1/0);continue;case si:u(-0);continue}if(t[i]){u(t[i]);continue}const f=r[i];if(!f||typeof f!="object"){t[i]=f,u(f);continue}if(Array.isArray(f))if(typeof f[0]=="string"){const[p,h,g]=f;switch(p){case hi:u(t[i]=new Date(h));continue;case wi:u(t[i]=new URL(h));continue;case fi:u(t[i]=BigInt(h));continue;case yi:u(t[i]=new RegExp(h,g));continue;case gi:u(t[i]=Symbol.for(h));continue;case vi:const w=new Set;t[i]=w;for(let T=f.length-1;T>0;T--)l.push([f[T],L=>{w.add(L)}]);u(w);continue;case mi:const b=new Map;t[i]=b;for(let T=f.length-2;T>0;T-=2){const L=[];l.push([f[T+1],y=>{L[1]=y}]),l.push([f[T],y=>{L[0]=y}]),s.push(()=>{b.set(L[0],L[1])})}u(b);continue;case pi:const S=Object.create(null);t[i]=S;for(const T of Object.keys(h).reverse()){const L=[];l.push([h[T],y=>{L[1]=y}]),l.push([Number(T.slice(1)),y=>{L[0]=y}]),s.push(()=>{S[L[0]]=L[1]})}u(S);continue;case jn:if(t[h])u(t[i]=t[h]);else{const T=new Hn;a[h]=T,u(t[i]=T.promise)}continue;case Fn:const[,R,M]=f;let P=M&&Gt&&Gt[M]?new Gt[M](R):new Error(R);t[i]=P,u(P);continue;case Ei:u(t[i]=t[h]);continue;default:if(Array.isArray(n)){const T=[],L=f.slice(1);for(let y=0;y<L.length;y++){const $=L[y];l.push([$,J=>{T[y]=J}])}s.push(()=>{for(const y of n){const $=y(f[0],...T);if($){u(t[i]=$.value);return}}throw new SyntaxError});continue}throw new SyntaxError}}else{const p=[];t[i]=p;for(let h=0;h<f.length;h++){const g=f[h];g!==oi&&l.push([g,w=>{p[h]=w}])}u(p);continue}else{const p={};t[i]=p;for(const h of Object.keys(f).reverse()){const g=[];l.push([f[h],w=>{g[1]=w}]),l.push([Number(h.slice(1)),w=>{g[0]=w}]),s.push(()=>{p[g[0]]=g[1]})}u(p);continue}}for(;s.length>0;)s.pop()();return o}async function bi(e,t){const{plugins:r}=t??{},a=new Hn,n=e.pipeThrough(Ri()).getReader(),o={values:[],hydrated:[],deferred:{},plugins:r},l=await Si.call(o,n);let s=a.promise;return l.done?a.resolve():s=xi.call(o,n).then(a.resolve).catch(i=>{for(const u of Object.values(o.deferred))u.reject(i);a.reject(i)}),{done:s.then(()=>n.closed),value:l.value}}async function Si(e){const t=await e.read();if(!t.value)throw new SyntaxError;let r;try{r=JSON.parse(t.value)}catch{throw new SyntaxError}return{done:t.done,value:nr.call(this,r)}}async function xi(e){let t=await e.read();for(;!t.done;){if(!t.value)continue;const r=t.value;switch(r[0]){case jn:{const a=r.indexOf(":"),n=Number(r.slice(1,a)),o=this.deferred[n];if(!o)throw new Error(`Deferred ID ${n} not found in stream`);const l=r.slice(a+1);let s;try{s=JSON.parse(l)}catch{throw new SyntaxError}const i=nr.call(this,s);o.resolve(i);break}case Fn:{const a=r.indexOf(":"),n=Number(r.slice(1,a)),o=this.deferred[n];if(!o)throw new Error(`Deferred ID ${n} not found in stream`);const l=r.slice(a+1);let s;try{s=JSON.parse(l)}catch{throw new SyntaxError}const i=nr.call(this,s);o.reject(i);break}default:throw new SyntaxError}t=await e.read()}}async function Li(e){let t={signal:e.signal};if(e.method!=="GET"){t.method=e.method;let r=e.headers.get("Content-Type");r&&/\bapplication\/json\b/.test(r)?(t.headers={"Content-Type":r},t.body=JSON.stringify(await e.json())):r&&/\btext\/plain\b/.test(r)?(t.headers={"Content-Type":r},t.body=await e.text()):r&&/\bapplication\/x-www-form-urlencoded\b/.test(r)?t.body=new URLSearchParams(await e.text()):t.body=await e.formData()}return t}var ar=Symbol("SingleFetchRedirect"),Un=class extends Error{},Ci=202,zn=new Set([100,101,204,205]);function El(e,t,r,a,n){let o=Pi(e,l=>{let s=t.routes[l.route.id];fe(s,"Route not found in manifest");let i=r[l.route.id];return{hasLoader:s.hasLoader,hasClientLoader:s.hasClientLoader,hasShouldRevalidate:!!i?.shouldRevalidate}},ki,a,n);return async l=>l.unstable_runClientMiddleware(o)}function Pi(e,t,r,a,n){return async o=>{let{request:l,matches:s,fetcherKey:i}=o,u=e();if(l.method!=="GET")return Mi(o,r,n);let f=s.some(p=>{let{hasLoader:h,hasClientLoader:g}=t(p);return p.unstable_shouldCallHandler()&&h&&!g});return!a&&!f?Ti(o,t,r,n):i?Oi(o,r,n):_i(o,u,t,r,a,n)}}async function Mi(e,t,r){let a=e.matches.find(l=>l.unstable_shouldCallHandler());fe(a,"No action match found");let n,o=await a.resolve(async l=>await l(async()=>{let{data:i,status:u}=await t(e,r,[a.route.id]);return n=u,st(i,a.route.id)}));return dr(o.result)||ze(o.result)||tr(o.result)?{[a.route.id]:o}:{[a.route.id]:{type:o.type,result:Va(o.result,n)}}}async function Ti(e,t,r,a){let n=e.matches.filter(l=>l.unstable_shouldCallHandler()),o={};return await Promise.all(n.map(l=>l.resolve(async s=>{try{let{hasClientLoader:i}=t(l),u=l.route.id,f=i?await s(async()=>{let{data:p}=await r(e,a,[u]);return st(p,u)}):await s();o[l.route.id]={type:"data",result:f}}catch(i){o[l.route.id]={type:"error",result:i}}}))),o}async function _i(e,t,r,a,n,o){let l=new Set,s=!1,i=e.matches.map(()=>un()),u=un(),f={},p=Promise.all(e.matches.map(async(g,w)=>g.resolve(async b=>{i[w].resolve();let S=g.route.id,{hasLoader:R,hasClientLoader:M,hasShouldRevalidate:P}=r(g),T=!g.unstable_shouldRevalidateArgs||g.unstable_shouldRevalidateArgs.actionStatus==null||g.unstable_shouldRevalidateArgs.actionStatus<400;if(!g.unstable_shouldCallHandler(T)){s||(s=g.unstable_shouldRevalidateArgs!=null&&R&&P===!0);return}if(M){R&&(s=!0);try{let y=await b(async()=>{let{data:$}=await a(e,o,[S]);return st($,S)});f[S]={type:"data",result:y}}catch(y){f[S]={type:"error",result:y}}return}R&&l.add(S);try{let y=await b(async()=>{let $=await u.promise;return st($,S)});f[S]={type:"data",result:y}}catch(y){f[S]={type:"error",result:y}}})));if(await Promise.all(i.map(g=>g.promise)),(!t.state.initialized&&t.state.navigation.state==="idle"||l.size===0)&&!window.__reactRouterHdrActive)u.resolve({routes:{}});else{let g=n&&s&&l.size>0?[...l.keys()]:void 0;try{let w=await a(e,o,g);u.resolve(w.data)}catch(w){u.reject(w)}}return await p,await Di(u.promise,e.matches,l,f),f}async function Di(e,t,r,a){try{let n,o=await e;if("routes"in o){for(let l of t)if(l.route.id in o.routes){let s=o.routes[l.route.id];if("error"in s){n=s.error;break}}}n!==void 0&&Array.from(r.values()).forEach(l=>{a[l].result instanceof Un&&(a[l].result=n)})}catch{}}async function Oi(e,t,r){let a=e.matches.find(l=>l.unstable_shouldCallHandler());fe(a,"No fetcher match found");let n=a.route.id,o=await a.resolve(async l=>l(async()=>{let{data:s}=await t(e,r,[n]);return st(s,n)}));return{[a.route.id]:o}}function Ai(e){let t=e.searchParams.getAll("index");e.searchParams.delete("index");let r=[];for(let a of t)a&&r.push(a);for(let a of r)e.searchParams.append("index",a);return e}function Bn(e,t){let r=typeof e=="string"?new URL(e,typeof window>"u"?"server://singlefetch/":window.location.origin):e;return r.pathname==="/"?r.pathname="_root.data":t&&ge(r.pathname,t)==="/"?r.pathname=`${t.replace(/\/$/,"")}/_root.data`:r.pathname=`${r.pathname.replace(/\/$/,"")}.data`,r}async function ki(e,t,r){let{request:a}=e,n=Bn(a.url,t);a.method==="GET"&&(n=Ai(n),r&&n.searchParams.set("_routes",r.join(",")));let o=await fetch(n,await Li(a));if(o.status===404&&!o.headers.has("X-Remix-Response"))throw new ke(404,"Not Found",!0);if(o.status===204&&o.headers.has("X-Remix-Redirect"))return{status:Ci,data:{redirect:{redirect:o.headers.get("X-Remix-Redirect"),status:Number(o.headers.get("X-Remix-Status")||"302"),revalidate:o.headers.get("X-Remix-Revalidate")==="true",reload:o.headers.get("X-Remix-Reload-Document")==="true",replace:o.headers.get("X-Remix-Replace")==="true"}}};if(zn.has(o.status)){let l={};return r&&a.method!=="GET"&&(l[r[0]]={data:void 0}),{status:o.status,data:{routes:l}}}fe(o.body,"No response body to decode");try{let l=await Ni(o.body,window),s;if(a.method==="GET"){let i=l.value;ar in i?s={redirect:i[ar]}:s={routes:i}}else{let i=l.value,u=r?.[0];fe(u,"No routeId found for single fetch call decoding"),"redirect"in i?s={redirect:i}:s={routes:{[u]:i}}}return{status:o.status,data:s}}catch{throw new Error("Unable to decode turbo-stream response")}}function Ni(e,t){return bi(e,{plugins:[(r,...a)=>{if(r==="SanitizedError"){let[n,o,l]=a,s=Error;n&&n in t&&typeof t[n]=="function"&&(s=t[n]);let i=new s(o);return i.stack=l,{value:i}}if(r==="ErrorResponse"){let[n,o,l]=a;return{value:new ke(o,l,n)}}if(r==="SingleFetchRedirect")return{value:{[ar]:a[0]}};if(r==="SingleFetchClassInstance")return{value:a[0]};if(r==="SingleFetchFallback")return{value:void 0}}]})}function st(e,t){if("redirect"in e){let{redirect:a,revalidate:n,reload:o,replace:l,status:s}=e.redirect;throw Ja(a,{status:s,headers:{...n?{"X-Remix-Revalidate":"yes"}:null,...o?{"X-Remix-Reload-Document":"yes"}:null,...l?{"X-Remix-Replace":"yes"}:null}})}let r=e.routes[t];if(r==null)throw new Un(`No result found for routeId "${t}"`);if("error"in r)throw r.error;if("data"in r)return r.data;throw new Error(`Invalid response found for routeId "${t}"`)}function un(){let e,t,r=new Promise((a,n)=>{e=async o=>{a(o);try{await r}catch{}},t=async o=>{n(o);try{await r}catch{}}});return{promise:r,resolve:e,reject:t}}var Rl=class extends d.Component{constructor(e){super(e),this.state={error:e.error||null,location:e.location}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location?{error:e.error||null,location:e.location}:{error:e.error||t.error,location:t.location}}render(){return this.state.error?d.createElement(Wn,{error:this.state.error,isOutsideRemixApp:!0}):this.props.children}};function Wn({error:e,isOutsideRemixApp:t}){console.error(e);let r=d.createElement("script",{dangerouslySetInnerHTML:{__html:`
        console.log(
          "💿 Hey developer 👋. You can provide a way better UX than this when your app throws errors. Check out https://reactrouter.com/how-to/error-boundary for more information."
        );
      `}});if(ze(e))return d.createElement(or,{title:"Unhandled Thrown Response!"},d.createElement("h1",{style:{fontSize:"24px"}},e.status," ",e.statusText),r);let a;if(e instanceof Error)a=e;else{let n=e==null?"Unknown Error":typeof e=="object"&&"toString"in e?e.toString():JSON.stringify(e);a=new Error(n)}return d.createElement(or,{title:"Application Error!",isOutsideRemixApp:t},d.createElement("h1",{style:{fontSize:"24px"}},"Application Error"),d.createElement("pre",{style:{padding:"2rem",background:"hsla(10, 50%, 50%, 0.1)",color:"red",overflow:"auto"}},a.stack),r)}function or({title:e,renderScripts:t,isOutsideRemixApp:r,children:a}){let{routeModules:n}=Ke();return n.root?.Layout&&!r?a:d.createElement("html",{lang:"en"},d.createElement("head",null,d.createElement("meta",{charSet:"utf-8"}),d.createElement("meta",{name:"viewport",content:"width=device-width,initial-scale=1,viewport-fit=cover"}),d.createElement("title",null,e)),d.createElement("body",null,d.createElement("main",{style:{fontFamily:"system-ui, sans-serif",padding:"2rem"}},a,t?d.createElement(Xi,null):null)))}function Ii(){return d.createElement(or,{title:"Loading...",renderScripts:!0},d.createElement("script",{dangerouslySetInnerHTML:{__html:`
              console.log(
                "💿 Hey developer 👋. You can provide a way better UX than this " +
                "when your app is loading JS modules and/or running \`clientLoader\` " +
                "functions. Check out https://reactrouter.com/start/framework/route-module#hydratefallback " +
                "for more information."
              );
            `}}))}function Yn(e){let t={};return Object.values(e).forEach(r=>{if(r){let a=r.parentId||"";t[a]||(t[a]=[]),t[a].push(r)}}),t}function $i(e,t,r){let a=Vn(t),n=t.HydrateFallback&&(!r||e.id==="root")?t.HydrateFallback:e.id==="root"?Ii:void 0,o=t.ErrorBoundary?t.ErrorBoundary:e.id==="root"?()=>d.createElement(Wn,{error:Dn()}):void 0;return e.id==="root"&&t.Layout?{...a?{element:d.createElement(t.Layout,null,d.createElement(a,null))}:{Component:a},...o?{errorElement:d.createElement(t.Layout,null,d.createElement(o,null))}:{ErrorBoundary:o},...n?{hydrateFallbackElement:d.createElement(t.Layout,null,d.createElement(n,null))}:{HydrateFallback:n}}:{Component:a,ErrorBoundary:o,HydrateFallback:n}}function bl(e,t,r,a,n,o){return wr(t,r,a,n,o,"",Yn(t),e)}function Rt(e,t){if(e==="loader"&&!t.hasLoader||e==="action"&&!t.hasAction){let a=`You are trying to call ${e==="action"?"serverAction()":"serverLoader()"} on a route that does not have a server ${e} (routeId: "${t.id}")`;throw console.error(a),new ke(400,"Bad Request",new Error(a),!0)}}function Kt(e,t){let r=e==="clientAction"?"a":"an",a=`Route "${t}" does not have ${r} ${e}, but you are trying to submit to it. To fix this, please add ${r} \`${e}\` function to the route`;throw console.error(a),new ke(405,"Method Not Allowed",new Error(a),!0)}function wr(e,t,r,a,n,o="",l=Yn(e),s){return(l[o]||[]).map(i=>{let u=t[i.id];function f(M){return fe(typeof M=="function","No single fetch function available for route handler"),M()}function p(M){return i.hasLoader?f(M):Promise.resolve(null)}function h(M){if(!i.hasAction)throw Kt("action",i.id);return f(M)}function g(M){import(M)}function w(M){M.clientActionModule&&g(M.clientActionModule),M.clientLoaderModule&&g(M.clientLoaderModule)}async function b(M){let P=t[i.id],T=P?Nn(i,P):Promise.resolve();try{return M()}finally{await T}}let S={id:i.id,index:i.index,path:i.path};if(u){Object.assign(S,{...S,...$i(i,u,n),unstable_middleware:u.unstable_clientMiddleware,handle:u.handle,shouldRevalidate:cn(S.path,u,i,a,s)});let M=r&&r.loaderData&&i.id in r.loaderData,P=M?r?.loaderData?.[i.id]:void 0,T=r&&r.errors&&i.id in r.errors,L=T?r?.errors?.[i.id]:void 0,y=s==null&&(u.clientLoader?.hydrate===!0||!i.hasLoader);S.loader=async({request:$,params:J,context:j},X)=>{try{return await b(async()=>(fe(u,"No `routeModule` available for critical-route loader"),u.clientLoader?u.clientLoader({request:$,params:J,context:j,async serverLoader(){if(Rt("loader",i),y){if(M)return P;if(T)throw L}return p(X)}}):p(X)))}finally{y=!1}},S.loader.hydrate=Jn(i.id,u.clientLoader,i.hasLoader,n),S.action=({request:$,params:J,context:j},X)=>b(async()=>{if(fe(u,"No `routeModule` available for critical-route action"),!u.clientAction){if(n)throw Kt("clientAction",i.id);return h(X)}return u.clientAction({request:$,params:J,context:j,async serverAction(){return Rt("action",i),h(X)}})})}else{i.hasClientLoader||(S.loader=(T,L)=>b(()=>p(L))),i.hasClientAction||(S.action=(T,L)=>b(()=>{if(n)throw Kt("clientAction",i.id);return h(L)}));let M;async function P(){return M?await M:(M=(async()=>{(i.clientLoaderModule||i.clientActionModule)&&await new Promise(L=>setTimeout(L,0));let T=ji(i,t);return w(i),await T})(),await M)}S.lazy={loader:i.hasClientLoader?async()=>{let{clientLoader:T}=i.clientLoaderModule?await import(i.clientLoaderModule):await P();return fe(T,"No `clientLoader` export found"),(L,y)=>T({...L,async serverLoader(){return Rt("loader",i),p(y)}})}:void 0,action:i.hasClientAction?async()=>{let T=i.clientActionModule?import(i.clientActionModule):P();w(i);let{clientAction:L}=await T;return fe(L,"No `clientAction` export found"),(y,$)=>L({...y,async serverAction(){return Rt("action",i),h($)}})}:void 0,unstable_middleware:i.hasClientMiddleware?async()=>{let{unstable_clientMiddleware:T}=i.clientMiddlewareModule?await import(i.clientMiddlewareModule):await P();return fe(T,"No `unstable_clientMiddleware` export found"),T}:void 0,shouldRevalidate:async()=>{let T=await P();return cn(S.path,T,i,a,s)},handle:async()=>(await P()).handle,Component:async()=>(await P()).Component,ErrorBoundary:i.hasErrorBoundary?async()=>(await P()).ErrorBoundary:void 0}}let R=wr(e,t,r,a,n,i.id,l,s);return R.length>0&&(S.children=R),S})}function cn(e,t,r,a,n){if(n)return Fi(r.id,t.shouldRevalidate,n);if(!a&&r.hasLoader&&!r.hasClientLoader){let o=e?yn(e)[1].map(s=>s.paramName):[];const l=s=>o.some(i=>s.currentParams[i]!==s.nextParams[i]);if(t.shouldRevalidate){let s=t.shouldRevalidate;return i=>s({...i,defaultShouldRevalidate:l(i)})}else return s=>l(s)}if(a&&t.shouldRevalidate){let o=t.shouldRevalidate;return l=>o({...l,defaultShouldRevalidate:!0})}return t.shouldRevalidate}function Fi(e,t,r){let a=!1;return n=>a?t?t(n):n.defaultShouldRevalidate:(a=!0,r.has(e))}async function ji(e,t){let r=An(e,t),a=Zo(e),n=await r;return await Promise.all([a,Nn(e,n)]),{Component:Vn(n),ErrorBoundary:n.ErrorBoundary,unstable_clientMiddleware:n.unstable_clientMiddleware,clientAction:n.clientAction,clientLoader:n.clientLoader,handle:n.handle,links:n.links,meta:n.meta,shouldRevalidate:n.shouldRevalidate}}function Vn(e){if(e.default==null)return;if(!(typeof e.default=="object"&&Object.keys(e.default).length===0))return e.default}function Jn(e,t,r,a){return a&&e!=="root"||t!=null&&(t.hydrate===!0||r!==!0)}var Ct=new Set,Hi=1e3,Dt=new Set,Ui=7680;function Er(e,t){return e.mode==="lazy"&&t===!0}function zi({sri:e,...t},r){let a=new Set(r.state.matches.map(s=>s.route.id)),n=r.state.location.pathname.split("/").filter(Boolean),o=["/"];for(n.pop();n.length>0;)o.push(`/${n.join("/")}`),n.pop();o.forEach(s=>{let i=Ce(r.routes,s,r.basename);i&&i.forEach(u=>a.add(u.route.id))});let l=[...a].reduce((s,i)=>Object.assign(s,{[i]:t.routes[i]}),{});return{...t,routes:l,sri:e?!0:void 0}}function Sl(e,t,r,a,n,o){if(Er(a,r))return async({path:l,patch:s,signal:i,fetcherKey:u})=>{Dt.has(l)||await Gn([l],u?window.location.href:l,e,t,r,n,o,a.manifestPath,s,i)}}function xl(e,t,r,a,n,o){d.useEffect(()=>{if(!Er(n,a)||window.navigator?.connection?.saveData===!0)return;function l(f){let p=f.tagName==="FORM"?f.getAttribute("action"):f.getAttribute("href");if(!p)return;let h=f.tagName==="A"?f.pathname:new URL(p,window.location.origin).pathname;Dt.has(h)||Ct.add(h)}async function s(){document.querySelectorAll("a[data-discover], form[data-discover]").forEach(l);let f=Array.from(Ct.keys()).filter(p=>Dt.has(p)?(Ct.delete(p),!1):!0);if(f.length!==0)try{await Gn(f,null,t,r,a,o,e.basename,n.manifestPath,e.patchRoutes)}catch(p){console.error("Failed to fetch manifest patches",p)}}let i=Yi(s,100);s();let u=new MutationObserver(()=>i());return u.observe(document.documentElement,{subtree:!0,childList:!0,attributes:!0,attributeFilter:["data-discover","href","action"]}),()=>u.disconnect()},[a,o,t,r,e,n])}function Bi(e,t){let r=e||"/__manifest";return t==null?r:`${t}${r}`.replace(/\/+/g,"/")}var Xt="react-router-manifest-version";async function Gn(e,t,r,a,n,o,l,s,i,u){let f=new URL(Bi(s,l),window.location.origin);if(e.sort().forEach(b=>f.searchParams.append("p",b)),f.searchParams.set("version",r.version),f.toString().length>Ui){Ct.clear();return}let p;try{let b=await fetch(f,{signal:u});if(b.ok){if(b.status===204&&b.headers.has("X-Remix-Reload-Document")){if(!t){console.warn("Detected a manifest version mismatch during eager route discovery. The next navigation/fetch to an undiscovered route will result in a new document navigation to sync up with the latest manifest.");return}if(sessionStorage.getItem(Xt)===r.version){console.error("Unable to discover routes due to manifest version mismatch.");return}sessionStorage.setItem(Xt,r.version),window.location.href=t,console.warn("Detected manifest version mismatch, reloading..."),await new Promise(()=>{})}else if(b.status>=400)throw new Error(await b.text())}else throw new Error(`${b.status} ${b.statusText}`);sessionStorage.removeItem(Xt),p=await b.json()}catch(b){if(u?.aborted)return;throw b}let h=new Set(Object.keys(r.routes)),g=Object.values(p).reduce((b,S)=>(S&&!h.has(S.id)&&(b[S.id]=S),b),{});Object.assign(r.routes,g),e.forEach(b=>Wi(b,Dt));let w=new Set;Object.values(g).forEach(b=>{b&&(!b.parentId||!g[b.parentId])&&w.add(b.parentId)}),w.forEach(b=>i(b||null,wr(g,a,null,n,o,b)))}function Wi(e,t){if(t.size>=Hi){let r=t.values().next().value;t.delete(r)}t.add(e)}function Yi(e,t){let r;return(...a)=>{window.clearTimeout(r),r=window.setTimeout(()=>e(...a),t)}}function Rr(){let e=d.useContext(Be);return fe(e,"You must render this element inside a <DataRouterContext.Provider> element"),e}function kt(){let e=d.useContext(Ge);return fe(e,"You must render this element inside a <DataRouterStateContext.Provider> element"),e}var Nt=d.createContext(void 0);Nt.displayName="FrameworkContext";function Ke(){let e=d.useContext(Nt);return fe(e,"You must render this element inside a <HydratedRouter> element"),e}function Vi(e,t){let r=d.useContext(Nt),[a,n]=d.useState(!1),[o,l]=d.useState(!1),{onFocus:s,onBlur:i,onMouseEnter:u,onMouseLeave:f,onTouchStart:p}=t,h=d.useRef(null);d.useEffect(()=>{if(e==="render"&&l(!0),e==="viewport"){let b=R=>{R.forEach(M=>{l(M.isIntersecting)})},S=new IntersectionObserver(b,{threshold:.5});return h.current&&S.observe(h.current),()=>{S.disconnect()}}},[e]),d.useEffect(()=>{if(a){let b=setTimeout(()=>{l(!0)},100);return()=>{clearTimeout(b)}}},[a]);let g=()=>{n(!0)},w=()=>{n(!1),l(!1)};return r?e!=="intent"?[o,h,{}]:[o,h,{onFocus:at(s,g),onBlur:at(i,w),onMouseEnter:at(u,g),onMouseLeave:at(f,w),onTouchStart:at(p,g)}]:[!1,h,{}]}function at(e,t){return r=>{e&&e(r),r.defaultPrevented||t(r)}}function br(e,t,r){if(r&&!Pt)return[e[0]];if(t){let a=e.findIndex(n=>t[n.route.id]!==void 0);return e.slice(0,a+1)}return e}function Ll(){let{isSpaMode:e,manifest:t,routeModules:r,criticalCss:a}=Ke(),{errors:n,matches:o}=kt(),l=br(o,n,e),s=d.useMemo(()=>Qo(l,r,t),[l,r,t]);return d.createElement(d.Fragment,null,typeof a=="string"?d.createElement("style",{dangerouslySetInnerHTML:{__html:a}}):null,typeof a=="object"?d.createElement("link",{rel:"stylesheet",href:a.href}):null,s.map(({key:i,link:u})=>vr(u)?d.createElement(Kn,{key:i,...u}):d.createElement("link",{key:i,...u})))}function Kn({page:e,...t}){let{router:r}=Rr(),a=d.useMemo(()=>Ce(r.routes,e,r.basename),[r.routes,e,r.basename]);return a?d.createElement(Gi,{page:e,matches:a,...t}):null}function Ji(e){let{manifest:t,routeModules:r}=Ke(),[a,n]=d.useState([]);return d.useEffect(()=>{let o=!1;return ti(e,t,r).then(l=>{o||n(l)}),()=>{o=!0}},[e,t,r]),a}function Gi({page:e,matches:t,...r}){let a=xe(),{manifest:n,routeModules:o}=Ke(),{basename:l}=Rr(),{loaderData:s,matches:i}=kt(),u=d.useMemo(()=>on(e,t,i,n,a,"data"),[e,t,i,n,a]),f=d.useMemo(()=>on(e,t,i,n,a,"assets"),[e,t,i,n,a]),p=d.useMemo(()=>{if(e===a.pathname+a.search+a.hash)return[];let w=new Set,b=!1;if(t.forEach(R=>{let M=n.routes[R.route.id];!M||!M.hasLoader||(!u.some(P=>P.route.id===R.route.id)&&R.route.id in s&&o[R.route.id]?.shouldRevalidate||M.hasClientLoader?b=!0:w.add(R.route.id))}),w.size===0)return[];let S=Bn(e,l);return b&&w.size>0&&S.searchParams.set("_routes",t.filter(R=>w.has(R.route.id)).map(R=>R.route.id).join(",")),[S.pathname+S.search]},[l,s,a,n,u,t,e,o]),h=d.useMemo(()=>gr(f,n),[f,n]),g=Ji(f);return d.createElement(d.Fragment,null,p.map(w=>d.createElement("link",{key:w,rel:"prefetch",as:"fetch",href:w,...r})),h.map(w=>d.createElement("link",{key:w,rel:"modulepreload",href:w,...r})),g.map(({key:w,link:b})=>d.createElement("link",{key:w,...b})))}function Cl(){let{isSpaMode:e,routeModules:t}=Ke(),{errors:r,matches:a,loaderData:n}=kt(),o=xe(),l=br(a,r,e),s=null;r&&(s=r[l[l.length-1].route.id]);let i=[],u=null,f=[];for(let p=0;p<l.length;p++){let h=l[p],g=h.route.id,w=n[g],b=h.params,S=t[g],R=[],M={id:g,data:w,meta:[],params:h.params,pathname:h.pathname,handle:h.route.handle,error:s};if(f[p]=M,S?.meta?R=typeof S.meta=="function"?S.meta({data:w,params:b,location:o,matches:f,error:s}):Array.isArray(S.meta)?[...S.meta]:S.meta:u&&(R=[...u]),R=R||[],!Array.isArray(R))throw new Error("The route at "+h.route.path+` returns an invalid value. All route meta functions must return an array of meta objects.

To reference the meta function API, see https://remix.run/route/meta`);M.meta=R,f[p]=M,i=[...R],u=i}return d.createElement(d.Fragment,null,i.flat().map(p=>{if(!p)return null;if("tagName"in p){let{tagName:h,...g}=p;if(!Ki(h))return console.warn(`A meta object uses an invalid tagName: ${h}. Expected either 'link' or 'meta'`),null;let w=h;return d.createElement(w,{key:JSON.stringify(g),...g})}if("title"in p)return d.createElement("title",{key:"title"},String(p.title));if("charset"in p&&(p.charSet??(p.charSet=p.charset),delete p.charset),"charSet"in p&&p.charSet!=null)return typeof p.charSet=="string"?d.createElement("meta",{key:"charSet",charSet:p.charSet}):null;if("script:ld+json"in p)try{let h=JSON.stringify(p["script:ld+json"]);return d.createElement("script",{key:`script:ld+json:${h}`,type:"application/ld+json",dangerouslySetInnerHTML:{__html:h}})}catch{return null}return d.createElement("meta",{key:JSON.stringify(p),...p})}))}function Ki(e){return typeof e=="string"&&/^(meta|link)$/.test(e)}var Pt=!1;function Xi(e){let{manifest:t,serverHandoffString:r,isSpaMode:a,renderMeta:n,routeDiscovery:o,ssr:l}=Ke(),{router:s,static:i,staticContext:u}=Rr(),{matches:f}=kt(),p=Er(o,l);n&&(n.didRenderScripts=!0);let h=br(f,null,a);d.useEffect(()=>{Pt=!0},[]);let g=d.useMemo(()=>{let R=u?`window.__reactRouterContext = ${r};window.__reactRouterContext.stream = new ReadableStream({start(controller){window.__reactRouterContext.streamController = controller;}}).pipeThrough(new TextEncoderStream());`:" ",M=i?`${t.hmr?.runtime?`import ${JSON.stringify(t.hmr.runtime)};`:""}${p?"":`import ${JSON.stringify(t.url)}`};
${h.map((P,T)=>{let L=`route${T}`,y=t.routes[P.route.id];fe(y,`Route ${P.route.id} not found in manifest`);let{clientActionModule:$,clientLoaderModule:J,clientMiddlewareModule:j,hydrateFallbackModule:X,module:le}=y,ce=[...$?[{module:$,varName:`${L}_clientAction`}]:[],...J?[{module:J,varName:`${L}_clientLoader`}]:[],...j?[{module:j,varName:`${L}_clientMiddleware`}]:[],...X?[{module:X,varName:`${L}_HydrateFallback`}]:[],{module:le,varName:`${L}_main`}];if(ce.length===1)return`import * as ${L} from ${JSON.stringify(le)};`;let q=ce.map(re=>`import * as ${re.varName} from "${re.module}";`).join(`
`),Z=`const ${L} = {${ce.map(re=>`...${re.varName}`).join(",")}};`;return[q,Z].join(`
`)}).join(`
`)}
  ${p?`window.__reactRouterManifest = ${JSON.stringify(zi(t,s),null,2)};`:""}
  window.__reactRouterRouteModules = {${h.map((P,T)=>`${JSON.stringify(P.route.id)}:route${T}`).join(",")}};

import(${JSON.stringify(t.entry.module)});`:" ";return d.createElement(d.Fragment,null,d.createElement("script",{...e,suppressHydrationWarning:!0,dangerouslySetInnerHTML:ln(R),type:void 0}),d.createElement("script",{...e,suppressHydrationWarning:!0,dangerouslySetInnerHTML:ln(M),type:"module",async:!0}))},[]),w=Pt?[]:qi(t.entry.imports.concat(gr(h,t,{includeHydrateFallback:!0}))),b=typeof t.sri=="object"?t.sri:{};return Pt?null:d.createElement(d.Fragment,null,typeof t.sri=="object"?d.createElement("script",{"rr-importmap":"",type:"importmap",suppressHydrationWarning:!0,dangerouslySetInnerHTML:{__html:JSON.stringify({integrity:b})}}):null,p?null:d.createElement("link",{rel:"modulepreload",href:t.url,crossOrigin:e.crossOrigin,integrity:b[t.url],suppressHydrationWarning:!0}),d.createElement("link",{rel:"modulepreload",href:t.entry.module,crossOrigin:e.crossOrigin,integrity:b[t.entry.module],suppressHydrationWarning:!0}),w.map(S=>d.createElement("link",{key:S,rel:"modulepreload",href:S,crossOrigin:e.crossOrigin,integrity:b[S],suppressHydrationWarning:!0})),g)}function qi(e){return[...new Set(e)]}function Qi(...e){return t=>{e.forEach(r=>{typeof r=="function"?r(t):r!=null&&(r.current=t)})}}var Xn=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u";try{Xn&&(window.__reactRouterVersion="7.6.3")}catch{}var qn=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,Qn=d.forwardRef(function({onClick:t,discover:r="render",prefetch:a="none",relative:n,reloadDocument:o,replace:l,state:s,target:i,to:u,preventScrollReset:f,viewTransition:p,...h},g){let{basename:w}=d.useContext(be),b=typeof u=="string"&&qn.test(u),S,R=!1;if(typeof u=="string"&&b&&(S=u,Xn))try{let j=new URL(window.location.href),X=u.startsWith("//")?new URL(j.protocol+u):new URL(u),le=ge(X.pathname,w);X.origin===j.origin&&le!=null?u=le+X.search+X.hash:R=!0}catch{ne(!1,`<Link to="${u}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let M=Ro(u,{relative:n}),[P,T,L]=Vi(a,h),y=nl(u,{replace:l,state:s,target:i,preventScrollReset:f,relative:n,viewTransition:p});function $(j){t&&t(j),j.defaultPrevented||y(j)}let J=d.createElement("a",{...h,...L,href:S||M,onClick:R||o?t:$,ref:Qi(g,T),target:i,"data-discover":!b&&r==="render"?"true":void 0});return P&&!b?d.createElement(d.Fragment,null,J,d.createElement(Kn,{page:M})):J});Qn.displayName="Link";var Zi=d.forwardRef(function({"aria-current":t="page",caseSensitive:r=!1,className:a="",end:n=!1,style:o,to:l,viewTransition:s,children:i,...u},f){let p=ct(l,{relative:u.relative}),h=xe(),g=d.useContext(Ge),{navigator:w,basename:b}=d.useContext(be),S=g!=null&&cl(p)&&s===!0,R=w.encodeLocation?w.encodeLocation(p).pathname:p.pathname,M=h.pathname,P=g&&g.navigation&&g.navigation.location?g.navigation.location.pathname:null;r||(M=M.toLowerCase(),P=P?P.toLowerCase():null,R=R.toLowerCase()),P&&b&&(P=ge(P,b)||P);const T=R!=="/"&&R.endsWith("/")?R.length-1:R.length;let L=M===R||!n&&M.startsWith(R)&&M.charAt(T)==="/",y=P!=null&&(P===R||!n&&P.startsWith(R)&&P.charAt(R.length)==="/"),$={isActive:L,isPending:y,isTransitioning:S},J=L?t:void 0,j;typeof a=="function"?j=a($):j=[a,L?"active":null,y?"pending":null,S?"transitioning":null].filter(Boolean).join(" ");let X=typeof o=="function"?o($):o;return d.createElement(Qn,{...u,"aria-current":J,className:j,ref:f,style:X,to:l,viewTransition:s},typeof i=="function"?i($):i)});Zi.displayName="NavLink";var el=d.forwardRef(({discover:e="render",fetcherKey:t,navigate:r,reloadDocument:a,replace:n,state:o,method:l=xt,action:s,onSubmit:i,relative:u,preventScrollReset:f,viewTransition:p,...h},g)=>{let w=il(),b=ll(s,{relative:u}),S=l.toLowerCase()==="get"?"get":"post",R=typeof s=="string"&&qn.test(s),M=P=>{if(i&&i(P),P.defaultPrevented)return;P.preventDefault();let T=P.nativeEvent.submitter,L=T?.getAttribute("formmethod")||l;w(T||P.currentTarget,{fetcherKey:t,method:L,navigate:r,replace:n,state:o,relative:u,preventScrollReset:f,viewTransition:p})};return d.createElement("form",{ref:g,method:S,action:b,onSubmit:a?i:M,...h,"data-discover":!R&&e==="render"?"true":void 0})});el.displayName="Form";function tl({getKey:e,storageKey:t,...r}){let a=d.useContext(Nt),{basename:n}=d.useContext(be),o=xe(),l=yr();sl({getKey:e,storageKey:t});let s=d.useMemo(()=>{if(!a||!e)return null;let u=lr(o,l,n,e);return u!==o.key?u:null},[]);if(!a||a.isSpaMode)return null;let i=((u,f)=>{if(!window.history.state||!window.history.state.key){let p=Math.random().toString(32).slice(2);window.history.replaceState({key:p},"")}try{let h=JSON.parse(sessionStorage.getItem(u)||"{}")[f||window.history.state.key];typeof h=="number"&&window.scrollTo(0,h)}catch(p){console.error(p),sessionStorage.removeItem(u)}}).toString();return d.createElement("script",{...r,suppressHydrationWarning:!0,dangerouslySetInnerHTML:{__html:`(${i})(${JSON.stringify(t||ir)}, ${JSON.stringify(s)})`}})}tl.displayName="ScrollRestoration";function Zn(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function Sr(e){let t=d.useContext(Be);return V(t,Zn(e)),t}function rl(e){let t=d.useContext(Ge);return V(t,Zn(e)),t}function nl(e,{target:t,replace:r,state:a,preventScrollReset:n,relative:o,viewTransition:l}={}){let s=_n(),i=xe(),u=ct(e,{relative:o});return d.useCallback(f=>{if(Jo(f,t)){f.preventDefault();let p=r!==void 0?r:Ae(i)===Ae(u);s(e,{replace:p,state:a,preventScrollReset:n,relative:o,viewTransition:l})}},[i,s,u,r,a,t,e,n,o,l])}function Pl(e){ne(typeof URLSearchParams<"u","You cannot use the `useSearchParams` hook in a browser that does not support the URLSearchParams API. If you need to support Internet Explorer 11, we recommend you load a polyfill such as https://github.com/ungap/url-search-params.");let t=d.useRef(rr(e)),r=d.useRef(!1),a=xe(),n=d.useMemo(()=>Go(a.search,r.current?null:t.current),[a.search]),o=_n(),l=d.useCallback((s,i)=>{const u=rr(typeof s=="function"?s(n):s);r.current=!0,o("?"+u,i)},[o,n]);return[n,l]}var al=0,ol=()=>`__${String(++al)}__`;function il(){let{router:e}=Sr("useSubmit"),{basename:t}=d.useContext(be),r=ko();return d.useCallback(async(a,n={})=>{let{action:o,method:l,encType:s,formData:i,body:u}=qo(a,t);if(n.navigate===!1){let f=n.fetcherKey||ol();await e.fetch(f,r,n.action||o,{preventScrollReset:n.preventScrollReset,formData:i,body:u,formMethod:n.method||l,formEncType:n.encType||s,flushSync:n.flushSync})}else await e.navigate(n.action||o,{preventScrollReset:n.preventScrollReset,formData:i,body:u,formMethod:n.method||l,formEncType:n.encType||s,replace:n.replace,state:n.state,fromRouteId:r,flushSync:n.flushSync,viewTransition:n.viewTransition})},[e,t,r])}function ll(e,{relative:t}={}){let{basename:r}=d.useContext(be),a=d.useContext(Se);V(a,"useFormAction must be used inside a RouteContext");let[n]=a.matches.slice(-1),o={...ct(e||".",{relative:t})},l=xe();if(e==null){o.search=l.search;let s=new URLSearchParams(o.search),i=s.getAll("index");if(i.some(f=>f==="")){s.delete("index"),i.filter(p=>p).forEach(p=>s.append("index",p));let f=s.toString();o.search=f?`?${f}`:""}}return(!e||e===".")&&n.route.index&&(o.search=o.search?o.search.replace(/^\?/,"?index&"):"?index"),r!=="/"&&(o.pathname=o.pathname==="/"?r:Pe([r,o.pathname])),Ae(o)}var ir="react-router-scroll-positions",bt={};function lr(e,t,r,a){let n=null;return a&&(r!=="/"?n=a({...e,pathname:ge(e.pathname,r)||e.pathname},t):n=a(e,t)),n==null&&(n=e.key),n}function sl({getKey:e,storageKey:t}={}){let{router:r}=Sr("useScrollRestoration"),{restoreScrollPosition:a,preventScrollReset:n}=rl("useScrollRestoration"),{basename:o}=d.useContext(be),l=xe(),s=yr(),i=No();d.useEffect(()=>(window.history.scrollRestoration="manual",()=>{window.history.scrollRestoration="auto"}),[]),ul(d.useCallback(()=>{if(i.state==="idle"){let u=lr(l,s,o,e);bt[u]=window.scrollY}try{sessionStorage.setItem(t||ir,JSON.stringify(bt))}catch(u){ne(!1,`Failed to save scroll positions in sessionStorage, <ScrollRestoration /> will not work properly (${u}).`)}window.history.scrollRestoration="auto"},[i.state,e,o,l,s,t])),typeof document<"u"&&(d.useLayoutEffect(()=>{try{let u=sessionStorage.getItem(t||ir);u&&(bt=JSON.parse(u))}catch{}},[t]),d.useLayoutEffect(()=>{let u=r?.enableScrollRestoration(bt,()=>window.scrollY,e?(f,p)=>lr(f,p,o,e):void 0);return()=>u&&u()},[r,o,e]),d.useLayoutEffect(()=>{if(a!==!1){if(typeof a=="number"){window.scrollTo(0,a);return}if(l.hash){let u=document.getElementById(decodeURIComponent(l.hash.slice(1)));if(u){u.scrollIntoView();return}}n!==!0&&window.scrollTo(0,0)}},[l,a,n]))}function ul(e,t){let{capture:r}={};d.useEffect(()=>{let a=r!=null?{capture:r}:void 0;return window.addEventListener("pagehide",e,a),()=>{window.removeEventListener("pagehide",e,a)}},[e,r])}function cl(e,t={}){let r=d.useContext(hr);V(r!=null,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:a}=Sr("useViewTransitionState"),n=ct(e,{relative:t.relative});if(!r.isTransitioning)return!1;let o=ge(r.currentLocation.pathname,a)||r.currentLocation.pathname,l=ge(r.nextLocation.pathname,a)||r.nextLocation.pathname;return Tt(n.pathname,l)!=null||Tt(n.pathname,o)!=null}[...zn];function Ml(e){if(!e)return null;let t=Object.entries(e),r={};for(let[a,n]of t)if(n&&n.__type==="RouteErrorResponse")r[a]=new ke(n.status,n.statusText,n.data,n.internal===!0);else if(n&&n.__type==="Error"){if(n.__subType){let o=window[n.__subType];if(typeof o=="function")try{let l=new o(n.message);l.stack=n.stack,r[a]=l}catch{}}if(r[a]==null){let o=new Error(n.message);o.stack=n.stack,r[a]=o}}else r[a]=n;return r}function Tl(e,t,r,a,n,o){let l={...e,loaderData:{...e.loaderData}},s=Ce(t,a,n);if(s)for(let i of s){let u=i.route.id,f=r(u);Jn(u,f.clientLoader,f.hasLoader,o)&&(f.hasHydrateFallback||!f.hasLoader)?delete l.loaderData[u]:f.hasLoader||(l.loaderData[u]=null)}return l}export{Pl as A,Nt as F,Ll as L,Cl as M,gl as O,Rl as R,tl as S,d as a,vl as b,wr as c,Ni as d,Ml as e,ml as f,Tl as g,Sl as h,V as i,El as j,hl as k,bl as l,pl as m,yl as n,xe as o,_n as p,ga as q,va as r,Xi as s,fl as t,xl as u,Qn as v,wl as w,dl as x,pa as y,Lo as z};
