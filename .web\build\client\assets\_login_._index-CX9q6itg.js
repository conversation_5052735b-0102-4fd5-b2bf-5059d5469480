import{w as f,a as _}from"./chunk-QMGIS6GS-suYYFPSk.js";import{D as d}from"./index-CKoiYi4C.js";import{f as r,E as c,c as o,h,i as x}from"./state-B1hYtTsq.js";import{j as t}from"./emotion-react.browser.esm-BNSIgtcs.js";import{c as u,r as p,o as b}from"./button-Ccwu8jNm.js";import{o as i}from"./card-BFhh40Od.js";import{p as l}from"./text-DCkbNTq3.js";import{u as g}from"./text-field-UsP3CXyu.js";import"./index-XOwJfM4g.js";import"./jsx-runtime-D_zvdyIk.js";function C(){const e=_.useContext(r.reflex___state____state__states___auth_state____auth_state);return t(_.Fragment,{},x(e.success_message_rx_state_)?t(_.Fragment,{},t(i,{css:{width:"100%",background:"green.50",border:"1px solid",borderColor:"green.200"}},t(w,{}))):t(_.Fragment,{}))}function w(){const e=_.useContext(r.reflex___state____state__states___auth_state____auth_state);return t(l,{as:"p",css:{color:"green.600"}},e.success_message_rx_state_)}function v(){const e=_.useContext(r.reflex___state____state__states___auth_state____auth_state),[s,m]=_.useContext(c),n=_.useCallback(a=>s([o("reflex___state____state.states___auth_state____auth_state.update_login_form",{field:"password",value:a.target.value},{})],[a],{}),[s,o]);return t(d,{css:{width:"100%"},debounceTimeout:300,element:g,onChange:n,placeholder:"Password",type:"password",value:h(e.login_password_rx_state_)?e.login_password_rx_state_:""})}function E(){const e=_.useContext(r.reflex___state____state__states___auth_state____auth_state),[s,m]=_.useContext(c),n=_.useCallback(a=>s([o("reflex___state____state.states___auth_state____auth_state.login",{},{})],[a],{}),[s,o]);return t(b,{color:"blue",css:{width:"100%"},loading:e.is_loading_rx_state_,onClick:n,size:"3"},"Login")}function y(){const e=_.useContext(r.reflex___state____state__states___auth_state____auth_state);return t(_.Fragment,{},x(e.error_message_rx_state_)?t(_.Fragment,{},t(i,{css:{width:"100%",background:"red.50",border:"1px solid",borderColor:"red.200"}},t(k,{}))):t(_.Fragment,{}))}function k(){const e=_.useContext(r.reflex___state____state__states___auth_state____auth_state);return t(l,{as:"p",css:{color:"red.600"}},e.error_message_rx_state_)}function F(){const e=_.useContext(r.reflex___state____state__states___auth_state____auth_state),[s,m]=_.useContext(c),n=_.useCallback(a=>s([o("reflex___state____state.states___auth_state____auth_state.update_login_form",{field:"username",value:a.target.value},{})],[a],{}),[s,o]);return t(d,{css:{width:"100%"},debounceTimeout:300,element:g,onChange:n,placeholder:"Username",value:h(e.login_username_rx_state_)?e.login_username_rx_state_:""})}const R=f(function(){return t(_.Fragment,{},t(u,{css:{display:"flex",alignItems:"center",justifyContent:"center",minHeight:"100vh",background:"gray.50"}},t(i,{css:{maxWidth:"400px",p:8}},t(u,{align:"start",className:"rx-Stack",css:{width:"100%"},direction:"column",gap:"4"},t(p,{css:{color:"blue.600",mb:6},size:"8"},"Psychiatry EMR"),t(l,{as:"p",css:{color:"gray.600",mb:4}},"Secure Patient Management System"),t(u,{align:"start",className:"rx-Stack",css:{width:"100%"},direction:"column",gap:"4"},t(F,{}),t(v,{}),t(E,{})),t(y,{}),t(C,{})))),t("title",{},"Login - Psychiatry EMR"),t("meta",{content:"favicon.ico",property:"og:image"}))});export{R as default};
