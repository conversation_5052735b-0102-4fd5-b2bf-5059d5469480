import{a as s,q as H,t as sr}from"./chunk-QMGIS6GS-suYYFPSk.js";import{u as j,c as Vt,b as ir,a as Ua,k as Va,m as Ga,S as cr,y as $,d as tn,s as lr,o as Oe,v as _e,r as yt,R as ge,l as ur,j as Dn,e as bt,h as Ka,f as Ya,g as Xa,i as qa,t as Za,p as Qa}from"./text-DCkbNTq3.js";import{P as B,d as dr,R as Ja,j as es,a as fr,f as pr,u as mr,k as ts}from"./button-Ccwu8jNm.js";import{d as ns}from"./container-Do-LxYxS.js";import{j as w}from"./jsx-runtime-D_zvdyIk.js";import{R as rs,a as os}from"./index-XOwJfM4g.js";function Ld(e,t){const n=s.createContext(t),r=a=>{const{children:i,...c}=a,u=s.useMemo(()=>c,Object.values(c));return w.jsx(n.Provider,{value:u,children:i})};r.displayName=e+"Provider";function o(a){const i=s.useContext(n);if(i)return i;if(t!==void 0)return t;throw new Error(`\`${a}\` must be used within \`${e}\``)}return[r,o]}function be(e,t=[]){let n=[];function r(a,i){const c=s.createContext(i),u=n.length;n=[...n,i];const d=f=>{const{scope:p,children:m,...v}=f,h=p?.[e]?.[u]||c,g=s.useMemo(()=>v,Object.values(v));return w.jsx(h.Provider,{value:g,children:m})};d.displayName=a+"Provider";function l(f,p){const m=p?.[e]?.[u]||c,v=s.useContext(m);if(v)return v;if(i!==void 0)return i;throw new Error(`\`${f}\` must be used within \`${a}\``)}return[d,l]}const o=()=>{const a=n.map(i=>s.createContext(i));return function(c){const u=c?.[e]||a;return s.useMemo(()=>({[`__scope${e}`]:{...c,[e]:u}}),[c,u])}};return o.scopeName=e,[r,as(o,...t)]}function as(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(a){const i=r.reduce((c,{useScope:u,scopeName:d})=>{const f=u(a)[`__scope${d}`];return{...c,...f}},{});return s.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return n.scopeName=t.scopeName,n}function hr(e){const t=e+"CollectionProvider",[n,r]=be(t),[o,a]=n(t,{collectionRef:{current:null},itemMap:new Map}),i=h=>{const{scope:g,children:y}=h,b=H.useRef(null),x=H.useRef(new Map).current;return w.jsx(o,{scope:g,itemMap:x,collectionRef:b,children:y})};i.displayName=t;const c=e+"CollectionSlot",u=Vt(c),d=H.forwardRef((h,g)=>{const{scope:y,children:b}=h,x=a(c,y),C=j(g,x.collectionRef);return w.jsx(u,{ref:C,children:b})});d.displayName=c;const l=e+"CollectionItemSlot",f="data-radix-collection-item",p=Vt(l),m=H.forwardRef((h,g)=>{const{scope:y,children:b,...x}=h,C=H.useRef(null),S=j(g,C),P=a(l,y);return H.useEffect(()=>(P.itemMap.set(C,{ref:C,...x}),()=>void P.itemMap.delete(C))),w.jsx(p,{[f]:"",ref:S,children:b})});m.displayName=l;function v(h){const g=a(e+"CollectionConsumer",h);return H.useCallback(()=>{const b=g.collectionRef.current;if(!b)return[];const x=Array.from(b.querySelectorAll(`[${f}]`));return Array.from(g.itemMap.values()).sort((P,E)=>x.indexOf(P.ref.current)-x.indexOf(E.ref.current))},[g.collectionRef,g.itemMap])}return[{Provider:i,Slot:d,ItemSlot:m},v,r]}function A(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e?.(o),n===!1||!o.defaultPrevented)return t?.(o)}}var ce=globalThis?.document?s.useLayoutEffect:()=>{},ss=sr[" useInsertionEffect ".trim().toString()]||ce;function xt({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){const[o,a,i]=is({defaultProp:t,onChange:n}),c=e!==void 0,u=c?e:o;{const l=s.useRef(e!==void 0);s.useEffect(()=>{const f=l.current;f!==c&&console.warn(`${r} is changing from ${f?"controlled":"uncontrolled"} to ${c?"controlled":"uncontrolled"}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`),l.current=c},[c,r])}const d=s.useCallback(l=>{if(c){const f=cs(l)?l(e):l;f!==e&&i.current?.(f)}else a(l)},[c,e,a,i]);return[u,d]}function is({defaultProp:e,onChange:t}){const[n,r]=s.useState(e),o=s.useRef(n),a=s.useRef(t);return ss(()=>{a.current=t},[t]),s.useEffect(()=>{o.current!==n&&(a.current?.(n),o.current=n)},[n,o]),[n,r,a]}function cs(e){return typeof e=="function"}function ls(e,t){return s.useReducer((n,r)=>t[n][r]??n,e)}var ee=e=>{const{present:t,children:n}=e,r=us(t),o=typeof n=="function"?n({present:r.isPresent}):s.Children.only(n),a=j(r.ref,ds(o));return typeof n=="function"||r.isPresent?s.cloneElement(o,{ref:a}):null};ee.displayName="Presence";function us(e){const[t,n]=s.useState(),r=s.useRef(null),o=s.useRef(e),a=s.useRef("none"),i=e?"mounted":"unmounted",[c,u]=ls(i,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return s.useEffect(()=>{const d=tt(r.current);a.current=c==="mounted"?d:"none"},[c]),ce(()=>{const d=r.current,l=o.current;if(l!==e){const p=a.current,m=tt(d);e?u("MOUNT"):m==="none"||d?.display==="none"?u("UNMOUNT"):u(l&&p!==m?"ANIMATION_OUT":"UNMOUNT"),o.current=e}},[e,u]),ce(()=>{if(t){let d;const l=t.ownerDocument.defaultView??window,f=m=>{const h=tt(r.current).includes(m.animationName);if(m.target===t&&h&&(u("ANIMATION_END"),!o.current)){const g=t.style.animationFillMode;t.style.animationFillMode="forwards",d=l.setTimeout(()=>{t.style.animationFillMode==="forwards"&&(t.style.animationFillMode=g)})}},p=m=>{m.target===t&&(a.current=tt(r.current))};return t.addEventListener("animationstart",p),t.addEventListener("animationcancel",f),t.addEventListener("animationend",f),()=>{l.clearTimeout(d),t.removeEventListener("animationstart",p),t.removeEventListener("animationcancel",f),t.removeEventListener("animationend",f)}}else u("ANIMATION_END")},[t,u]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:s.useCallback(d=>{r.current=d?getComputedStyle(d):null,n(d)},[])}}function tt(e){return e?.animationName||"none"}function ds(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=Object.getOwnPropertyDescriptor(e,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var fs=sr[" useId ".trim().toString()]||(()=>{}),ps=0;function Ae(e){const[t,n]=s.useState(fs());return ce(()=>{n(r=>r??String(ps++))},[e]),e||(t?`radix-${t}`:"")}var vr=s.createContext(void 0),gr=e=>{const{dir:t,children:n}=e;return w.jsx(vr.Provider,{value:t,children:n})};function Ct(e){const t=s.useContext(vr);return e||t||"ltr"}var wr=gr;const kd=Object.freeze(Object.defineProperty({__proto__:null,DirectionProvider:gr,Provider:wr,useDirection:Ct},Symbol.toStringTag,{value:"Module"}));function V(e){const t=s.useRef(e);return s.useEffect(()=>{t.current=e}),s.useMemo(()=>(...n)=>t.current?.(...n),[])}function ms(e,t=globalThis?.document){const n=V(e);s.useEffect(()=>{const r=o=>{o.key==="Escape"&&n(o)};return t.addEventListener("keydown",r,{capture:!0}),()=>t.removeEventListener("keydown",r,{capture:!0})},[n,t])}var hs="DismissableLayer",Gt="dismissableLayer.update",vs="dismissableLayer.pointerDownOutside",gs="dismissableLayer.focusOutside",On,yr=s.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),nn=s.forwardRef((e,t)=>{const{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:o,onFocusOutside:a,onInteractOutside:i,onDismiss:c,...u}=e,d=s.useContext(yr),[l,f]=s.useState(null),p=l?.ownerDocument??globalThis?.document,[,m]=s.useState({}),v=j(t,E=>f(E)),h=Array.from(d.layers),[g]=[...d.layersWithOutsidePointerEventsDisabled].slice(-1),y=h.indexOf(g),b=l?h.indexOf(l):-1,x=d.layersWithOutsidePointerEventsDisabled.size>0,C=b>=y,S=bs(E=>{const M=E.target,T=[...d.branches].some(N=>N.contains(M));!C||T||(o?.(E),i?.(E),E.defaultPrevented||c?.())},p),P=xs(E=>{const M=E.target;[...d.branches].some(N=>N.contains(M))||(a?.(E),i?.(E),E.defaultPrevented||c?.())},p);return ms(E=>{b===d.layers.size-1&&(r?.(E),!E.defaultPrevented&&c&&(E.preventDefault(),c()))},p),s.useEffect(()=>{if(l)return n&&(d.layersWithOutsidePointerEventsDisabled.size===0&&(On=p.body.style.pointerEvents,p.body.style.pointerEvents="none"),d.layersWithOutsidePointerEventsDisabled.add(l)),d.layers.add(l),_n(),()=>{n&&d.layersWithOutsidePointerEventsDisabled.size===1&&(p.body.style.pointerEvents=On)}},[l,p,n,d]),s.useEffect(()=>()=>{l&&(d.layers.delete(l),d.layersWithOutsidePointerEventsDisabled.delete(l),_n())},[l,d]),s.useEffect(()=>{const E=()=>m({});return document.addEventListener(Gt,E),()=>document.removeEventListener(Gt,E)},[]),w.jsx(B.div,{...u,ref:v,style:{pointerEvents:x?C?"auto":"none":void 0,...e.style},onFocusCapture:A(e.onFocusCapture,P.onFocusCapture),onBlurCapture:A(e.onBlurCapture,P.onBlurCapture),onPointerDownCapture:A(e.onPointerDownCapture,S.onPointerDownCapture)})});nn.displayName=hs;var ws="DismissableLayerBranch",ys=s.forwardRef((e,t)=>{const n=s.useContext(yr),r=s.useRef(null),o=j(t,r);return s.useEffect(()=>{const a=r.current;if(a)return n.branches.add(a),()=>{n.branches.delete(a)}},[n.branches]),w.jsx(B.div,{...e,ref:o})});ys.displayName=ws;function bs(e,t=globalThis?.document){const n=V(e),r=s.useRef(!1),o=s.useRef(()=>{});return s.useEffect(()=>{const a=c=>{if(c.target&&!r.current){let u=function(){br(vs,n,d,{discrete:!0})};const d={originalEvent:c};c.pointerType==="touch"?(t.removeEventListener("click",o.current),o.current=u,t.addEventListener("click",o.current,{once:!0})):u()}else t.removeEventListener("click",o.current);r.current=!1},i=window.setTimeout(()=>{t.addEventListener("pointerdown",a)},0);return()=>{window.clearTimeout(i),t.removeEventListener("pointerdown",a),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}function xs(e,t=globalThis?.document){const n=V(e),r=s.useRef(!1);return s.useEffect(()=>{const o=a=>{a.target&&!r.current&&br(gs,n,{originalEvent:a},{discrete:!1})};return t.addEventListener("focusin",o),()=>t.removeEventListener("focusin",o)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}function _n(){const e=new CustomEvent(Gt);document.dispatchEvent(e)}function br(e,t,n,{discrete:r}){const o=n.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?dr(o,a):o.dispatchEvent(a)}var Lt="focusScope.autoFocusOnMount",kt="focusScope.autoFocusOnUnmount",In={bubbles:!1,cancelable:!0},Cs="FocusScope",xr=s.forwardRef((e,t)=>{const{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:a,...i}=e,[c,u]=s.useState(null),d=V(o),l=V(a),f=s.useRef(null),p=j(t,h=>u(h)),m=s.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;s.useEffect(()=>{if(r){let h=function(x){if(m.paused||!c)return;const C=x.target;c.contains(C)?f.current=C:de(f.current,{select:!0})},g=function(x){if(m.paused||!c)return;const C=x.relatedTarget;C!==null&&(c.contains(C)||de(f.current,{select:!0}))},y=function(x){if(document.activeElement===document.body)for(const S of x)S.removedNodes.length>0&&de(c)};document.addEventListener("focusin",h),document.addEventListener("focusout",g);const b=new MutationObserver(y);return c&&b.observe(c,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",h),document.removeEventListener("focusout",g),b.disconnect()}}},[r,c,m.paused]),s.useEffect(()=>{if(c){kn.add(m);const h=document.activeElement;if(!c.contains(h)){const y=new CustomEvent(Lt,In);c.addEventListener(Lt,d),c.dispatchEvent(y),y.defaultPrevented||(Ss(As(Cr(c)),{select:!0}),document.activeElement===h&&de(c))}return()=>{c.removeEventListener(Lt,d),setTimeout(()=>{const y=new CustomEvent(kt,In);c.addEventListener(kt,l),c.dispatchEvent(y),y.defaultPrevented||de(h??document.body,{select:!0}),c.removeEventListener(kt,l),kn.remove(m)},0)}}},[c,d,l,m]);const v=s.useCallback(h=>{if(!n&&!r||m.paused)return;const g=h.key==="Tab"&&!h.altKey&&!h.ctrlKey&&!h.metaKey,y=document.activeElement;if(g&&y){const b=h.currentTarget,[x,C]=Es(b);x&&C?!h.shiftKey&&y===C?(h.preventDefault(),n&&de(x,{select:!0})):h.shiftKey&&y===x&&(h.preventDefault(),n&&de(C,{select:!0})):y===b&&h.preventDefault()}},[n,r,m.paused]);return w.jsx(B.div,{tabIndex:-1,...i,ref:p,onKeyDown:v})});xr.displayName=Cs;function Ss(e,{select:t=!1}={}){const n=document.activeElement;for(const r of e)if(de(r,{select:t}),document.activeElement!==n)return}function Es(e){const t=Cr(e),n=Ln(t,e),r=Ln(t.reverse(),e);return[n,r]}function Cr(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const o=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||o?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function Ln(e,t){for(const n of e)if(!Rs(n,{upTo:t}))return n}function Rs(e,{upTo:t}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(t!==void 0&&e===t)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function Ps(e){return e instanceof HTMLInputElement&&"select"in e}function de(e,{select:t=!1}={}){if(e&&e.focus){const n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&Ps(e)&&t&&e.select()}}var kn=Ms();function Ms(){let e=[];return{add(t){const n=e[0];t!==n&&n?.pause(),e=jn(e,t),e.unshift(t)},remove(t){e=jn(e,t),e[0]?.resume()}}}function jn(e,t){const n=[...e],r=n.indexOf(t);return r!==-1&&n.splice(r,1),n}function As(e){return e.filter(t=>t.tagName!=="A")}var Ts="Portal",St=s.forwardRef((e,t)=>{const{container:n,...r}=e,[o,a]=s.useState(!1);ce(()=>a(!0),[]);const i=n||o&&globalThis?.document?.body;return i?rs.createPortal(w.jsx(B.div,{...r,ref:t}),i):null});St.displayName=Ts;var jd=St,jt=0;function Ns(){s.useEffect(()=>{const e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??Fn()),document.body.insertAdjacentElement("beforeend",e[1]??Fn()),jt++,()=>{jt===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(t=>t.remove()),jt--}},[])}function Fn(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var ne=function(){return ne=Object.assign||function(t){for(var n,r=1,o=arguments.length;r<o;r++){n=arguments[r];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(t[a]=n[a])}return t},ne.apply(this,arguments)};function Sr(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}function Ds(e,t,n){if(n||arguments.length===2)for(var r=0,o=t.length,a;r<o;r++)(a||!(r in t))&&(a||(a=Array.prototype.slice.call(t,0,r)),a[r]=t[r]);return e.concat(a||Array.prototype.slice.call(t))}var it="right-scroll-bar-position",ct="width-before-scroll-bar",Os="with-scroll-bars-hidden",_s="--removed-body-scroll-bar-size";function Ft(e,t){return typeof e=="function"?e(t):e&&(e.current=t),e}function Is(e,t){var n=s.useState(function(){return{value:e,callback:t,facade:{get current(){return n.value},set current(r){var o=n.value;o!==r&&(n.value=r,n.callback(r,o))}}}})[0];return n.callback=t,n.facade}var Ls=typeof window<"u"?s.useLayoutEffect:s.useEffect,$n=new WeakMap;function ks(e,t){var n=Is(null,function(r){return e.forEach(function(o){return Ft(o,r)})});return Ls(function(){var r=$n.get(n);if(r){var o=new Set(r),a=new Set(e),i=n.current;o.forEach(function(c){a.has(c)||Ft(c,null)}),a.forEach(function(c){o.has(c)||Ft(c,i)})}$n.set(n,e)},[e]),n}function js(e){return e}function Fs(e,t){t===void 0&&(t=js);var n=[],r=!1,o={read:function(){if(r)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:e},useMedium:function(a){var i=t(a,r);return n.push(i),function(){n=n.filter(function(c){return c!==i})}},assignSyncMedium:function(a){for(r=!0;n.length;){var i=n;n=[],i.forEach(a)}n={push:function(c){return a(c)},filter:function(){return n}}},assignMedium:function(a){r=!0;var i=[];if(n.length){var c=n;n=[],c.forEach(a),i=n}var u=function(){var l=i;i=[],l.forEach(a)},d=function(){return Promise.resolve().then(u)};d(),n={push:function(l){i.push(l),d()},filter:function(l){return i=i.filter(l),n}}}};return o}function $s(e){e===void 0&&(e={});var t=Fs(null);return t.options=ne({async:!0,ssr:!1},e),t}var Er=function(e){var t=e.sideCar,n=Sr(e,["sideCar"]);if(!t)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw new Error("Sidecar medium not found");return s.createElement(r,ne({},n))};Er.isSideCarExport=!0;function Bs(e,t){return e.useMedium(t),Er}var Rr=$s(),$t=function(){},Et=s.forwardRef(function(e,t){var n=s.useRef(null),r=s.useState({onScrollCapture:$t,onWheelCapture:$t,onTouchMoveCapture:$t}),o=r[0],a=r[1],i=e.forwardProps,c=e.children,u=e.className,d=e.removeScrollBar,l=e.enabled,f=e.shards,p=e.sideCar,m=e.noRelative,v=e.noIsolation,h=e.inert,g=e.allowPinchZoom,y=e.as,b=y===void 0?"div":y,x=e.gapMode,C=Sr(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),S=p,P=ks([n,t]),E=ne(ne({},C),o);return s.createElement(s.Fragment,null,l&&s.createElement(S,{sideCar:Rr,removeScrollBar:d,shards:f,noRelative:m,noIsolation:v,inert:h,setCallbacks:a,allowPinchZoom:!!g,lockRef:n,gapMode:x}),i?s.cloneElement(s.Children.only(c),ne(ne({},E),{ref:P})):s.createElement(b,ne({},E,{className:u,ref:P}),c))});Et.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};Et.classNames={fullWidth:ct,zeroRight:it};var Ws=function(){if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function Hs(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=Ws();return t&&e.setAttribute("nonce",t),e}function zs(e,t){e.styleSheet?e.styleSheet.cssText=t:e.appendChild(document.createTextNode(t))}function Us(e){var t=document.head||document.getElementsByTagName("head")[0];t.appendChild(e)}var Vs=function(){var e=0,t=null;return{add:function(n){e==0&&(t=Hs())&&(zs(t,n),Us(t)),e++},remove:function(){e--,!e&&t&&(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},Gs=function(){var e=Vs();return function(t,n){s.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},Pr=function(){var e=Gs(),t=function(n){var r=n.styles,o=n.dynamic;return e(r,o),null};return t},Ks={left:0,top:0,right:0,gap:0},Bt=function(e){return parseInt(e||"",10)||0},Ys=function(e){var t=window.getComputedStyle(document.body),n=t[e==="padding"?"paddingLeft":"marginLeft"],r=t[e==="padding"?"paddingTop":"marginTop"],o=t[e==="padding"?"paddingRight":"marginRight"];return[Bt(n),Bt(r),Bt(o)]},Xs=function(e){if(e===void 0&&(e="margin"),typeof window>"u")return Ks;var t=Ys(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},qs=Pr(),Pe="data-scroll-locked",Zs=function(e,t,n,r){var o=e.left,a=e.top,i=e.right,c=e.gap;return n===void 0&&(n="margin"),`
  .`.concat(Os,` {
   overflow: hidden `).concat(r,`;
   padding-right: `).concat(c,"px ").concat(r,`;
  }
  body[`).concat(Pe,`] {
    overflow: hidden `).concat(r,`;
    overscroll-behavior: contain;
    `).concat([t&&"position: relative ".concat(r,";"),n==="margin"&&`
    padding-left: `.concat(o,`px;
    padding-top: `).concat(a,`px;
    padding-right: `).concat(i,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(c,"px ").concat(r,`;
    `),n==="padding"&&"padding-right: ".concat(c,"px ").concat(r,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(it,` {
    right: `).concat(c,"px ").concat(r,`;
  }
  
  .`).concat(ct,` {
    margin-right: `).concat(c,"px ").concat(r,`;
  }
  
  .`).concat(it," .").concat(it,` {
    right: 0 `).concat(r,`;
  }
  
  .`).concat(ct," .").concat(ct,` {
    margin-right: 0 `).concat(r,`;
  }
  
  body[`).concat(Pe,`] {
    `).concat(_s,": ").concat(c,`px;
  }
`)},Bn=function(){var e=parseInt(document.body.getAttribute(Pe)||"0",10);return isFinite(e)?e:0},Qs=function(){s.useEffect(function(){return document.body.setAttribute(Pe,(Bn()+1).toString()),function(){var e=Bn()-1;e<=0?document.body.removeAttribute(Pe):document.body.setAttribute(Pe,e.toString())}},[])},Js=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=r===void 0?"margin":r;Qs();var a=s.useMemo(function(){return Xs(o)},[o]);return s.createElement(qs,{styles:Zs(a,!t,o,n?"":"!important")})},Kt=!1;if(typeof window<"u")try{var nt=Object.defineProperty({},"passive",{get:function(){return Kt=!0,!0}});window.addEventListener("test",nt,nt),window.removeEventListener("test",nt,nt)}catch{Kt=!1}var Ce=Kt?{passive:!1}:!1,ei=function(e){return e.tagName==="TEXTAREA"},Mr=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return n[t]!=="hidden"&&!(n.overflowY===n.overflowX&&!ei(e)&&n[t]==="visible")},ti=function(e){return Mr(e,"overflowY")},ni=function(e){return Mr(e,"overflowX")},Wn=function(e,t){var n=t.ownerDocument,r=t;do{typeof ShadowRoot<"u"&&r instanceof ShadowRoot&&(r=r.host);var o=Ar(e,r);if(o){var a=Tr(e,r),i=a[1],c=a[2];if(i>c)return!0}r=r.parentNode}while(r&&r!==n.body);return!1},ri=function(e){var t=e.scrollTop,n=e.scrollHeight,r=e.clientHeight;return[t,n,r]},oi=function(e){var t=e.scrollLeft,n=e.scrollWidth,r=e.clientWidth;return[t,n,r]},Ar=function(e,t){return e==="v"?ti(t):ni(t)},Tr=function(e,t){return e==="v"?ri(t):oi(t)},ai=function(e,t){return e==="h"&&t==="rtl"?-1:1},si=function(e,t,n,r,o){var a=ai(e,window.getComputedStyle(t).direction),i=a*r,c=n.target,u=t.contains(c),d=!1,l=i>0,f=0,p=0;do{if(!c)break;var m=Tr(e,c),v=m[0],h=m[1],g=m[2],y=h-g-a*v;(v||y)&&Ar(e,c)&&(f+=y,p+=v);var b=c.parentNode;c=b&&b.nodeType===Node.DOCUMENT_FRAGMENT_NODE?b.host:b}while(!u&&c!==document.body||u&&(t.contains(c)||t===c));return(l&&Math.abs(f)<1||!l&&Math.abs(p)<1)&&(d=!0),d},rt=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},Hn=function(e){return[e.deltaX,e.deltaY]},zn=function(e){return e&&"current"in e?e.current:e},ii=function(e,t){return e[0]===t[0]&&e[1]===t[1]},ci=function(e){return`
  .block-interactivity-`.concat(e,` {pointer-events: none;}
  .allow-interactivity-`).concat(e,` {pointer-events: all;}
`)},li=0,Se=[];function ui(e){var t=s.useRef([]),n=s.useRef([0,0]),r=s.useRef(),o=s.useState(li++)[0],a=s.useState(Pr)[0],i=s.useRef(e);s.useEffect(function(){i.current=e},[e]),s.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var h=Ds([e.lockRef.current],(e.shards||[]).map(zn),!0).filter(Boolean);return h.forEach(function(g){return g.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),h.forEach(function(g){return g.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var c=s.useCallback(function(h,g){if("touches"in h&&h.touches.length===2||h.type==="wheel"&&h.ctrlKey)return!i.current.allowPinchZoom;var y=rt(h),b=n.current,x="deltaX"in h?h.deltaX:b[0]-y[0],C="deltaY"in h?h.deltaY:b[1]-y[1],S,P=h.target,E=Math.abs(x)>Math.abs(C)?"h":"v";if("touches"in h&&E==="h"&&P.type==="range")return!1;var M=Wn(E,P);if(!M)return!0;if(M?S=E:(S=E==="v"?"h":"v",M=Wn(E,P)),!M)return!1;if(!r.current&&"changedTouches"in h&&(x||C)&&(r.current=S),!S)return!0;var T=r.current||S;return si(T,g,h,T==="h"?x:C)},[]),u=s.useCallback(function(h){var g=h;if(!(!Se.length||Se[Se.length-1]!==a)){var y="deltaY"in g?Hn(g):rt(g),b=t.current.filter(function(S){return S.name===g.type&&(S.target===g.target||g.target===S.shadowParent)&&ii(S.delta,y)})[0];if(b&&b.should){g.cancelable&&g.preventDefault();return}if(!b){var x=(i.current.shards||[]).map(zn).filter(Boolean).filter(function(S){return S.contains(g.target)}),C=x.length>0?c(g,x[0]):!i.current.noIsolation;C&&g.cancelable&&g.preventDefault()}}},[]),d=s.useCallback(function(h,g,y,b){var x={name:h,delta:g,target:y,should:b,shadowParent:di(y)};t.current.push(x),setTimeout(function(){t.current=t.current.filter(function(C){return C!==x})},1)},[]),l=s.useCallback(function(h){n.current=rt(h),r.current=void 0},[]),f=s.useCallback(function(h){d(h.type,Hn(h),h.target,c(h,e.lockRef.current))},[]),p=s.useCallback(function(h){d(h.type,rt(h),h.target,c(h,e.lockRef.current))},[]);s.useEffect(function(){return Se.push(a),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",u,Ce),document.addEventListener("touchmove",u,Ce),document.addEventListener("touchstart",l,Ce),function(){Se=Se.filter(function(h){return h!==a}),document.removeEventListener("wheel",u,Ce),document.removeEventListener("touchmove",u,Ce),document.removeEventListener("touchstart",l,Ce)}},[]);var m=e.removeScrollBar,v=e.inert;return s.createElement(s.Fragment,null,v?s.createElement(a,{styles:ci(o)}):null,m?s.createElement(Js,{noRelative:e.noRelative,gapMode:e.gapMode}):null)}function di(e){for(var t=null;e!==null;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}const fi=Bs(Rr,ui);var Nr=s.forwardRef(function(e,t){return s.createElement(Et,ne({},e,{ref:t,sideCar:fi}))});Nr.classNames=Et.classNames;var pi=function(e){if(typeof document>"u")return null;var t=Array.isArray(e)?e[0]:e;return t.ownerDocument.body},Ee=new WeakMap,ot=new WeakMap,at={},Wt=0,Dr=function(e){return e&&(e.host||Dr(e.parentNode))},mi=function(e,t){return t.map(function(n){if(e.contains(n))return n;var r=Dr(n);return r&&e.contains(r)?r:(console.error("aria-hidden",n,"in not contained inside",e,". Doing nothing"),null)}).filter(function(n){return!!n})},hi=function(e,t,n,r){var o=mi(t,Array.isArray(e)?e:[e]);at[n]||(at[n]=new WeakMap);var a=at[n],i=[],c=new Set,u=new Set(o),d=function(f){!f||c.has(f)||(c.add(f),d(f.parentNode))};o.forEach(d);var l=function(f){!f||u.has(f)||Array.prototype.forEach.call(f.children,function(p){if(c.has(p))l(p);else try{var m=p.getAttribute(r),v=m!==null&&m!=="false",h=(Ee.get(p)||0)+1,g=(a.get(p)||0)+1;Ee.set(p,h),a.set(p,g),i.push(p),h===1&&v&&ot.set(p,!0),g===1&&p.setAttribute(n,"true"),v||p.setAttribute(r,"true")}catch(y){console.error("aria-hidden: cannot operate on ",p,y)}})};return l(t),c.clear(),Wt++,function(){i.forEach(function(f){var p=Ee.get(f)-1,m=a.get(f)-1;Ee.set(f,p),a.set(f,m),p||(ot.has(f)||f.removeAttribute(r),ot.delete(f)),m||f.removeAttribute(n)}),Wt--,Wt||(Ee=new WeakMap,Ee=new WeakMap,ot=new WeakMap,at={})}},vi=function(e,t,n){n===void 0&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=pi(e);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live], script"))),hi(r,o,n,"aria-hidden")):function(){return null}};function gi(e){const[t,n]=s.useState(void 0);return ce(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});const r=new ResizeObserver(o=>{if(!Array.isArray(o)||!o.length)return;const a=o[0];let i,c;if("borderBoxSize"in a){const u=a.borderBoxSize,d=Array.isArray(u)?u[0]:u;i=d.inlineSize,c=d.blockSize}else i=e.offsetWidth,c=e.offsetHeight;n({width:i,height:c})});return r.observe(e,{box:"border-box"}),()=>r.unobserve(e)}else n(void 0)},[e]),t}const wi=["top","right","bottom","left"],fe=Math.min,Y=Math.max,ut=Math.round,st=Math.floor,oe=e=>({x:e,y:e}),yi={left:"right",right:"left",bottom:"top",top:"bottom"},bi={start:"end",end:"start"};function Yt(e,t,n){return Y(e,fe(t,n))}function le(e,t){return typeof e=="function"?e(t):e}function ue(e){return e.split("-")[0]}function Ie(e){return e.split("-")[1]}function rn(e){return e==="x"?"y":"x"}function on(e){return e==="y"?"height":"width"}const xi=new Set(["top","bottom"]);function re(e){return xi.has(ue(e))?"y":"x"}function an(e){return rn(re(e))}function Ci(e,t,n){n===void 0&&(n=!1);const r=Ie(e),o=an(e),a=on(o);let i=o==="x"?r===(n?"end":"start")?"right":"left":r==="start"?"bottom":"top";return t.reference[a]>t.floating[a]&&(i=dt(i)),[i,dt(i)]}function Si(e){const t=dt(e);return[Xt(e),t,Xt(t)]}function Xt(e){return e.replace(/start|end/g,t=>bi[t])}const Un=["left","right"],Vn=["right","left"],Ei=["top","bottom"],Ri=["bottom","top"];function Pi(e,t,n){switch(e){case"top":case"bottom":return n?t?Vn:Un:t?Un:Vn;case"left":case"right":return t?Ei:Ri;default:return[]}}function Mi(e,t,n,r){const o=Ie(e);let a=Pi(ue(e),n==="start",r);return o&&(a=a.map(i=>i+"-"+o),t&&(a=a.concat(a.map(Xt)))),a}function dt(e){return e.replace(/left|right|bottom|top/g,t=>yi[t])}function Ai(e){return{top:0,right:0,bottom:0,left:0,...e}}function Or(e){return typeof e!="number"?Ai(e):{top:e,right:e,bottom:e,left:e}}function ft(e){const{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function Gn(e,t,n){let{reference:r,floating:o}=e;const a=re(t),i=an(t),c=on(i),u=ue(t),d=a==="y",l=r.x+r.width/2-o.width/2,f=r.y+r.height/2-o.height/2,p=r[c]/2-o[c]/2;let m;switch(u){case"top":m={x:l,y:r.y-o.height};break;case"bottom":m={x:l,y:r.y+r.height};break;case"right":m={x:r.x+r.width,y:f};break;case"left":m={x:r.x-o.width,y:f};break;default:m={x:r.x,y:r.y}}switch(Ie(t)){case"start":m[i]-=p*(n&&d?-1:1);break;case"end":m[i]+=p*(n&&d?-1:1);break}return m}const Ti=async(e,t,n)=>{const{placement:r="bottom",strategy:o="absolute",middleware:a=[],platform:i}=n,c=a.filter(Boolean),u=await(i.isRTL==null?void 0:i.isRTL(t));let d=await i.getElementRects({reference:e,floating:t,strategy:o}),{x:l,y:f}=Gn(d,r,u),p=r,m={},v=0;for(let h=0;h<c.length;h++){const{name:g,fn:y}=c[h],{x:b,y:x,data:C,reset:S}=await y({x:l,y:f,initialPlacement:r,placement:p,strategy:o,middlewareData:m,rects:d,platform:i,elements:{reference:e,floating:t}});l=b??l,f=x??f,m={...m,[g]:{...m[g],...C}},S&&v<=50&&(v++,typeof S=="object"&&(S.placement&&(p=S.placement),S.rects&&(d=S.rects===!0?await i.getElementRects({reference:e,floating:t,strategy:o}):S.rects),{x:l,y:f}=Gn(d,p,u)),h=-1)}return{x:l,y:f,placement:p,strategy:o,middlewareData:m}};async function He(e,t){var n;t===void 0&&(t={});const{x:r,y:o,platform:a,rects:i,elements:c,strategy:u}=e,{boundary:d="clippingAncestors",rootBoundary:l="viewport",elementContext:f="floating",altBoundary:p=!1,padding:m=0}=le(t,e),v=Or(m),g=c[p?f==="floating"?"reference":"floating":f],y=ft(await a.getClippingRect({element:(n=await(a.isElement==null?void 0:a.isElement(g)))==null||n?g:g.contextElement||await(a.getDocumentElement==null?void 0:a.getDocumentElement(c.floating)),boundary:d,rootBoundary:l,strategy:u})),b=f==="floating"?{x:r,y:o,width:i.floating.width,height:i.floating.height}:i.reference,x=await(a.getOffsetParent==null?void 0:a.getOffsetParent(c.floating)),C=await(a.isElement==null?void 0:a.isElement(x))?await(a.getScale==null?void 0:a.getScale(x))||{x:1,y:1}:{x:1,y:1},S=ft(a.convertOffsetParentRelativeRectToViewportRelativeRect?await a.convertOffsetParentRelativeRectToViewportRelativeRect({elements:c,rect:b,offsetParent:x,strategy:u}):b);return{top:(y.top-S.top+v.top)/C.y,bottom:(S.bottom-y.bottom+v.bottom)/C.y,left:(y.left-S.left+v.left)/C.x,right:(S.right-y.right+v.right)/C.x}}const Ni=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:r,placement:o,rects:a,platform:i,elements:c,middlewareData:u}=t,{element:d,padding:l=0}=le(e,t)||{};if(d==null)return{};const f=Or(l),p={x:n,y:r},m=an(o),v=on(m),h=await i.getDimensions(d),g=m==="y",y=g?"top":"left",b=g?"bottom":"right",x=g?"clientHeight":"clientWidth",C=a.reference[v]+a.reference[m]-p[m]-a.floating[v],S=p[m]-a.reference[m],P=await(i.getOffsetParent==null?void 0:i.getOffsetParent(d));let E=P?P[x]:0;(!E||!await(i.isElement==null?void 0:i.isElement(P)))&&(E=c.floating[x]||a.floating[v]);const M=C/2-S/2,T=E/2-h[v]/2-1,N=fe(f[y],T),_=fe(f[b],T),k=N,L=E-h[v]-_,I=E/2-h[v]/2+M,z=Yt(k,I,L),O=!u.arrow&&Ie(o)!=null&&I!==z&&a.reference[v]/2-(I<k?N:_)-h[v]/2<0,F=O?I<k?I-k:I-L:0;return{[m]:p[m]+F,data:{[m]:z,centerOffset:I-z-F,...O&&{alignmentOffset:F}},reset:O}}}),Di=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var n,r;const{placement:o,middlewareData:a,rects:i,initialPlacement:c,platform:u,elements:d}=t,{mainAxis:l=!0,crossAxis:f=!0,fallbackPlacements:p,fallbackStrategy:m="bestFit",fallbackAxisSideDirection:v="none",flipAlignment:h=!0,...g}=le(e,t);if((n=a.arrow)!=null&&n.alignmentOffset)return{};const y=ue(o),b=re(c),x=ue(c)===c,C=await(u.isRTL==null?void 0:u.isRTL(d.floating)),S=p||(x||!h?[dt(c)]:Si(c)),P=v!=="none";!p&&P&&S.push(...Mi(c,h,v,C));const E=[c,...S],M=await He(t,g),T=[];let N=((r=a.flip)==null?void 0:r.overflows)||[];if(l&&T.push(M[y]),f){const I=Ci(o,i,C);T.push(M[I[0]],M[I[1]])}if(N=[...N,{placement:o,overflows:T}],!T.every(I=>I<=0)){var _,k;const I=(((_=a.flip)==null?void 0:_.index)||0)+1,z=E[I];if(z&&(!(f==="alignment"?b!==re(z):!1)||N.every(D=>D.overflows[0]>0&&re(D.placement)===b)))return{data:{index:I,overflows:N},reset:{placement:z}};let O=(k=N.filter(F=>F.overflows[0]<=0).sort((F,D)=>F.overflows[1]-D.overflows[1])[0])==null?void 0:k.placement;if(!O)switch(m){case"bestFit":{var L;const F=(L=N.filter(D=>{if(P){const R=re(D.placement);return R===b||R==="y"}return!0}).map(D=>[D.placement,D.overflows.filter(R=>R>0).reduce((R,U)=>R+U,0)]).sort((D,R)=>D[1]-R[1])[0])==null?void 0:L[0];F&&(O=F);break}case"initialPlacement":O=c;break}if(o!==O)return{reset:{placement:O}}}return{}}}};function Kn(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function Yn(e){return wi.some(t=>e[t]>=0)}const Oi=function(e){return e===void 0&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,{strategy:r="referenceHidden",...o}=le(e,t);switch(r){case"referenceHidden":{const a=await He(t,{...o,elementContext:"reference"}),i=Kn(a,n.reference);return{data:{referenceHiddenOffsets:i,referenceHidden:Yn(i)}}}case"escaped":{const a=await He(t,{...o,altBoundary:!0}),i=Kn(a,n.floating);return{data:{escapedOffsets:i,escaped:Yn(i)}}}default:return{}}}}},_r=new Set(["left","top"]);async function _i(e,t){const{placement:n,platform:r,elements:o}=e,a=await(r.isRTL==null?void 0:r.isRTL(o.floating)),i=ue(n),c=Ie(n),u=re(n)==="y",d=_r.has(i)?-1:1,l=a&&u?-1:1,f=le(t,e);let{mainAxis:p,crossAxis:m,alignmentAxis:v}=typeof f=="number"?{mainAxis:f,crossAxis:0,alignmentAxis:null}:{mainAxis:f.mainAxis||0,crossAxis:f.crossAxis||0,alignmentAxis:f.alignmentAxis};return c&&typeof v=="number"&&(m=c==="end"?v*-1:v),u?{x:m*l,y:p*d}:{x:p*d,y:m*l}}const Ii=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var n,r;const{x:o,y:a,placement:i,middlewareData:c}=t,u=await _i(t,e);return i===((n=c.offset)==null?void 0:n.placement)&&(r=c.arrow)!=null&&r.alignmentOffset?{}:{x:o+u.x,y:a+u.y,data:{...u,placement:i}}}}},Li=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:r,placement:o}=t,{mainAxis:a=!0,crossAxis:i=!1,limiter:c={fn:g=>{let{x:y,y:b}=g;return{x:y,y:b}}},...u}=le(e,t),d={x:n,y:r},l=await He(t,u),f=re(ue(o)),p=rn(f);let m=d[p],v=d[f];if(a){const g=p==="y"?"top":"left",y=p==="y"?"bottom":"right",b=m+l[g],x=m-l[y];m=Yt(b,m,x)}if(i){const g=f==="y"?"top":"left",y=f==="y"?"bottom":"right",b=v+l[g],x=v-l[y];v=Yt(b,v,x)}const h=c.fn({...t,[p]:m,[f]:v});return{...h,data:{x:h.x-n,y:h.y-r,enabled:{[p]:a,[f]:i}}}}}},ki=function(e){return e===void 0&&(e={}),{options:e,fn(t){const{x:n,y:r,placement:o,rects:a,middlewareData:i}=t,{offset:c=0,mainAxis:u=!0,crossAxis:d=!0}=le(e,t),l={x:n,y:r},f=re(o),p=rn(f);let m=l[p],v=l[f];const h=le(c,t),g=typeof h=="number"?{mainAxis:h,crossAxis:0}:{mainAxis:0,crossAxis:0,...h};if(u){const x=p==="y"?"height":"width",C=a.reference[p]-a.floating[x]+g.mainAxis,S=a.reference[p]+a.reference[x]-g.mainAxis;m<C?m=C:m>S&&(m=S)}if(d){var y,b;const x=p==="y"?"width":"height",C=_r.has(ue(o)),S=a.reference[f]-a.floating[x]+(C&&((y=i.offset)==null?void 0:y[f])||0)+(C?0:g.crossAxis),P=a.reference[f]+a.reference[x]+(C?0:((b=i.offset)==null?void 0:b[f])||0)-(C?g.crossAxis:0);v<S?v=S:v>P&&(v=P)}return{[p]:m,[f]:v}}}},ji=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(t){var n,r;const{placement:o,rects:a,platform:i,elements:c}=t,{apply:u=()=>{},...d}=le(e,t),l=await He(t,d),f=ue(o),p=Ie(o),m=re(o)==="y",{width:v,height:h}=a.floating;let g,y;f==="top"||f==="bottom"?(g=f,y=p===(await(i.isRTL==null?void 0:i.isRTL(c.floating))?"start":"end")?"left":"right"):(y=f,g=p==="end"?"top":"bottom");const b=h-l.top-l.bottom,x=v-l.left-l.right,C=fe(h-l[g],b),S=fe(v-l[y],x),P=!t.middlewareData.shift;let E=C,M=S;if((n=t.middlewareData.shift)!=null&&n.enabled.x&&(M=x),(r=t.middlewareData.shift)!=null&&r.enabled.y&&(E=b),P&&!p){const N=Y(l.left,0),_=Y(l.right,0),k=Y(l.top,0),L=Y(l.bottom,0);m?M=v-2*(N!==0||_!==0?N+_:Y(l.left,l.right)):E=h-2*(k!==0||L!==0?k+L:Y(l.top,l.bottom))}await u({...t,availableWidth:M,availableHeight:E});const T=await i.getDimensions(c.floating);return v!==T.width||h!==T.height?{reset:{rects:!0}}:{}}}};function Rt(){return typeof window<"u"}function Le(e){return Ir(e)?(e.nodeName||"").toLowerCase():"#document"}function X(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function se(e){var t;return(t=(Ir(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function Ir(e){return Rt()?e instanceof Node||e instanceof X(e).Node:!1}function Q(e){return Rt()?e instanceof Element||e instanceof X(e).Element:!1}function ae(e){return Rt()?e instanceof HTMLElement||e instanceof X(e).HTMLElement:!1}function Xn(e){return!Rt()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof X(e).ShadowRoot}const Fi=new Set(["inline","contents"]);function Ke(e){const{overflow:t,overflowX:n,overflowY:r,display:o}=J(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!Fi.has(o)}const $i=new Set(["table","td","th"]);function Bi(e){return $i.has(Le(e))}const Wi=[":popover-open",":modal"];function Pt(e){return Wi.some(t=>{try{return e.matches(t)}catch{return!1}})}const Hi=["transform","translate","scale","rotate","perspective"],zi=["transform","translate","scale","rotate","perspective","filter"],Ui=["paint","layout","strict","content"];function sn(e){const t=cn(),n=Q(e)?J(e):e;return Hi.some(r=>n[r]?n[r]!=="none":!1)||(n.containerType?n.containerType!=="normal":!1)||!t&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!t&&(n.filter?n.filter!=="none":!1)||zi.some(r=>(n.willChange||"").includes(r))||Ui.some(r=>(n.contain||"").includes(r))}function Vi(e){let t=pe(e);for(;ae(t)&&!Te(t);){if(sn(t))return t;if(Pt(t))return null;t=pe(t)}return null}function cn(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}const Gi=new Set(["html","body","#document"]);function Te(e){return Gi.has(Le(e))}function J(e){return X(e).getComputedStyle(e)}function Mt(e){return Q(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function pe(e){if(Le(e)==="html")return e;const t=e.assignedSlot||e.parentNode||Xn(e)&&e.host||se(e);return Xn(t)?t.host:t}function Lr(e){const t=pe(e);return Te(t)?e.ownerDocument?e.ownerDocument.body:e.body:ae(t)&&Ke(t)?t:Lr(t)}function ze(e,t,n){var r;t===void 0&&(t=[]),n===void 0&&(n=!0);const o=Lr(e),a=o===((r=e.ownerDocument)==null?void 0:r.body),i=X(o);if(a){const c=qt(i);return t.concat(i,i.visualViewport||[],Ke(o)?o:[],c&&n?ze(c):[])}return t.concat(o,ze(o,[],n))}function qt(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function kr(e){const t=J(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const o=ae(e),a=o?e.offsetWidth:n,i=o?e.offsetHeight:r,c=ut(n)!==a||ut(r)!==i;return c&&(n=a,r=i),{width:n,height:r,$:c}}function ln(e){return Q(e)?e:e.contextElement}function Me(e){const t=ln(e);if(!ae(t))return oe(1);const n=t.getBoundingClientRect(),{width:r,height:o,$:a}=kr(t);let i=(a?ut(n.width):n.width)/r,c=(a?ut(n.height):n.height)/o;return(!i||!Number.isFinite(i))&&(i=1),(!c||!Number.isFinite(c))&&(c=1),{x:i,y:c}}const Ki=oe(0);function jr(e){const t=X(e);return!cn()||!t.visualViewport?Ki:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function Yi(e,t,n){return t===void 0&&(t=!1),!n||t&&n!==X(e)?!1:t}function ye(e,t,n,r){t===void 0&&(t=!1),n===void 0&&(n=!1);const o=e.getBoundingClientRect(),a=ln(e);let i=oe(1);t&&(r?Q(r)&&(i=Me(r)):i=Me(e));const c=Yi(a,n,r)?jr(a):oe(0);let u=(o.left+c.x)/i.x,d=(o.top+c.y)/i.y,l=o.width/i.x,f=o.height/i.y;if(a){const p=X(a),m=r&&Q(r)?X(r):r;let v=p,h=qt(v);for(;h&&r&&m!==v;){const g=Me(h),y=h.getBoundingClientRect(),b=J(h),x=y.left+(h.clientLeft+parseFloat(b.paddingLeft))*g.x,C=y.top+(h.clientTop+parseFloat(b.paddingTop))*g.y;u*=g.x,d*=g.y,l*=g.x,f*=g.y,u+=x,d+=C,v=X(h),h=qt(v)}}return ft({width:l,height:f,x:u,y:d})}function un(e,t){const n=Mt(e).scrollLeft;return t?t.left+n:ye(se(e)).left+n}function Fr(e,t,n){n===void 0&&(n=!1);const r=e.getBoundingClientRect(),o=r.left+t.scrollLeft-(n?0:un(e,r)),a=r.top+t.scrollTop;return{x:o,y:a}}function Xi(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e;const a=o==="fixed",i=se(r),c=t?Pt(t.floating):!1;if(r===i||c&&a)return n;let u={scrollLeft:0,scrollTop:0},d=oe(1);const l=oe(0),f=ae(r);if((f||!f&&!a)&&((Le(r)!=="body"||Ke(i))&&(u=Mt(r)),ae(r))){const m=ye(r);d=Me(r),l.x=m.x+r.clientLeft,l.y=m.y+r.clientTop}const p=i&&!f&&!a?Fr(i,u,!0):oe(0);return{width:n.width*d.x,height:n.height*d.y,x:n.x*d.x-u.scrollLeft*d.x+l.x+p.x,y:n.y*d.y-u.scrollTop*d.y+l.y+p.y}}function qi(e){return Array.from(e.getClientRects())}function Zi(e){const t=se(e),n=Mt(e),r=e.ownerDocument.body,o=Y(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),a=Y(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight);let i=-n.scrollLeft+un(e);const c=-n.scrollTop;return J(r).direction==="rtl"&&(i+=Y(t.clientWidth,r.clientWidth)-o),{width:o,height:a,x:i,y:c}}function Qi(e,t){const n=X(e),r=se(e),o=n.visualViewport;let a=r.clientWidth,i=r.clientHeight,c=0,u=0;if(o){a=o.width,i=o.height;const d=cn();(!d||d&&t==="fixed")&&(c=o.offsetLeft,u=o.offsetTop)}return{width:a,height:i,x:c,y:u}}const Ji=new Set(["absolute","fixed"]);function ec(e,t){const n=ye(e,!0,t==="fixed"),r=n.top+e.clientTop,o=n.left+e.clientLeft,a=ae(e)?Me(e):oe(1),i=e.clientWidth*a.x,c=e.clientHeight*a.y,u=o*a.x,d=r*a.y;return{width:i,height:c,x:u,y:d}}function qn(e,t,n){let r;if(t==="viewport")r=Qi(e,n);else if(t==="document")r=Zi(se(e));else if(Q(t))r=ec(t,n);else{const o=jr(e);r={x:t.x-o.x,y:t.y-o.y,width:t.width,height:t.height}}return ft(r)}function $r(e,t){const n=pe(e);return n===t||!Q(n)||Te(n)?!1:J(n).position==="fixed"||$r(n,t)}function tc(e,t){const n=t.get(e);if(n)return n;let r=ze(e,[],!1).filter(c=>Q(c)&&Le(c)!=="body"),o=null;const a=J(e).position==="fixed";let i=a?pe(e):e;for(;Q(i)&&!Te(i);){const c=J(i),u=sn(i);!u&&c.position==="fixed"&&(o=null),(a?!u&&!o:!u&&c.position==="static"&&!!o&&Ji.has(o.position)||Ke(i)&&!u&&$r(e,i))?r=r.filter(l=>l!==i):o=c,i=pe(i)}return t.set(e,r),r}function nc(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e;const i=[...n==="clippingAncestors"?Pt(t)?[]:tc(t,this._c):[].concat(n),r],c=i[0],u=i.reduce((d,l)=>{const f=qn(t,l,o);return d.top=Y(f.top,d.top),d.right=fe(f.right,d.right),d.bottom=fe(f.bottom,d.bottom),d.left=Y(f.left,d.left),d},qn(t,c,o));return{width:u.right-u.left,height:u.bottom-u.top,x:u.left,y:u.top}}function rc(e){const{width:t,height:n}=kr(e);return{width:t,height:n}}function oc(e,t,n){const r=ae(t),o=se(t),a=n==="fixed",i=ye(e,!0,a,t);let c={scrollLeft:0,scrollTop:0};const u=oe(0);function d(){u.x=un(o)}if(r||!r&&!a)if((Le(t)!=="body"||Ke(o))&&(c=Mt(t)),r){const m=ye(t,!0,a,t);u.x=m.x+t.clientLeft,u.y=m.y+t.clientTop}else o&&d();a&&!r&&o&&d();const l=o&&!r&&!a?Fr(o,c):oe(0),f=i.left+c.scrollLeft-u.x-l.x,p=i.top+c.scrollTop-u.y-l.y;return{x:f,y:p,width:i.width,height:i.height}}function Ht(e){return J(e).position==="static"}function Zn(e,t){if(!ae(e)||J(e).position==="fixed")return null;if(t)return t(e);let n=e.offsetParent;return se(e)===n&&(n=n.ownerDocument.body),n}function Br(e,t){const n=X(e);if(Pt(e))return n;if(!ae(e)){let o=pe(e);for(;o&&!Te(o);){if(Q(o)&&!Ht(o))return o;o=pe(o)}return n}let r=Zn(e,t);for(;r&&Bi(r)&&Ht(r);)r=Zn(r,t);return r&&Te(r)&&Ht(r)&&!sn(r)?n:r||Vi(e)||n}const ac=async function(e){const t=this.getOffsetParent||Br,n=this.getDimensions,r=await n(e.floating);return{reference:oc(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}};function sc(e){return J(e).direction==="rtl"}const ic={convertOffsetParentRelativeRectToViewportRelativeRect:Xi,getDocumentElement:se,getClippingRect:nc,getOffsetParent:Br,getElementRects:ac,getClientRects:qi,getDimensions:rc,getScale:Me,isElement:Q,isRTL:sc};function Wr(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function cc(e,t){let n=null,r;const o=se(e);function a(){var c;clearTimeout(r),(c=n)==null||c.disconnect(),n=null}function i(c,u){c===void 0&&(c=!1),u===void 0&&(u=1),a();const d=e.getBoundingClientRect(),{left:l,top:f,width:p,height:m}=d;if(c||t(),!p||!m)return;const v=st(f),h=st(o.clientWidth-(l+p)),g=st(o.clientHeight-(f+m)),y=st(l),x={rootMargin:-v+"px "+-h+"px "+-g+"px "+-y+"px",threshold:Y(0,fe(1,u))||1};let C=!0;function S(P){const E=P[0].intersectionRatio;if(E!==u){if(!C)return i();E?i(!1,E):r=setTimeout(()=>{i(!1,1e-7)},1e3)}E===1&&!Wr(d,e.getBoundingClientRect())&&i(),C=!1}try{n=new IntersectionObserver(S,{...x,root:o.ownerDocument})}catch{n=new IntersectionObserver(S,x)}n.observe(e)}return i(!0),a}function lc(e,t,n,r){r===void 0&&(r={});const{ancestorScroll:o=!0,ancestorResize:a=!0,elementResize:i=typeof ResizeObserver=="function",layoutShift:c=typeof IntersectionObserver=="function",animationFrame:u=!1}=r,d=ln(e),l=o||a?[...d?ze(d):[],...ze(t)]:[];l.forEach(y=>{o&&y.addEventListener("scroll",n,{passive:!0}),a&&y.addEventListener("resize",n)});const f=d&&c?cc(d,n):null;let p=-1,m=null;i&&(m=new ResizeObserver(y=>{let[b]=y;b&&b.target===d&&m&&(m.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var x;(x=m)==null||x.observe(t)})),n()}),d&&!u&&m.observe(d),m.observe(t));let v,h=u?ye(e):null;u&&g();function g(){const y=ye(e);h&&!Wr(h,y)&&n(),h=y,v=requestAnimationFrame(g)}return n(),()=>{var y;l.forEach(b=>{o&&b.removeEventListener("scroll",n),a&&b.removeEventListener("resize",n)}),f?.(),(y=m)==null||y.disconnect(),m=null,u&&cancelAnimationFrame(v)}}const uc=Ii,dc=Li,fc=Di,pc=ji,mc=Oi,Qn=Ni,hc=ki,vc=(e,t,n)=>{const r=new Map,o={platform:ic,...n},a={...o.platform,_c:r};return Ti(e,t,{...o,platform:a})};var gc=typeof document<"u",wc=function(){},lt=gc?s.useLayoutEffect:wc;function pt(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if(typeof e=="function"&&e.toString()===t.toString())return!0;let n,r,o;if(e&&t&&typeof e=="object"){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(r=n;r--!==0;)if(!pt(e[r],t[r]))return!1;return!0}if(o=Object.keys(e),n=o.length,n!==Object.keys(t).length)return!1;for(r=n;r--!==0;)if(!{}.hasOwnProperty.call(t,o[r]))return!1;for(r=n;r--!==0;){const a=o[r];if(!(a==="_owner"&&e.$$typeof)&&!pt(e[a],t[a]))return!1}return!0}return e!==e&&t!==t}function Hr(e){return typeof window>"u"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function Jn(e,t){const n=Hr(e);return Math.round(t*n)/n}function zt(e){const t=s.useRef(e);return lt(()=>{t.current=e}),t}function yc(e){e===void 0&&(e={});const{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:o,elements:{reference:a,floating:i}={},transform:c=!0,whileElementsMounted:u,open:d}=e,[l,f]=s.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,m]=s.useState(r);pt(p,r)||m(r);const[v,h]=s.useState(null),[g,y]=s.useState(null),b=s.useCallback(D=>{D!==P.current&&(P.current=D,h(D))},[]),x=s.useCallback(D=>{D!==E.current&&(E.current=D,y(D))},[]),C=a||v,S=i||g,P=s.useRef(null),E=s.useRef(null),M=s.useRef(l),T=u!=null,N=zt(u),_=zt(o),k=zt(d),L=s.useCallback(()=>{if(!P.current||!E.current)return;const D={placement:t,strategy:n,middleware:p};_.current&&(D.platform=_.current),vc(P.current,E.current,D).then(R=>{const U={...R,isPositioned:k.current!==!1};I.current&&!pt(M.current,U)&&(M.current=U,os.flushSync(()=>{f(U)}))})},[p,t,n,_,k]);lt(()=>{d===!1&&M.current.isPositioned&&(M.current.isPositioned=!1,f(D=>({...D,isPositioned:!1})))},[d]);const I=s.useRef(!1);lt(()=>(I.current=!0,()=>{I.current=!1}),[]),lt(()=>{if(C&&(P.current=C),S&&(E.current=S),C&&S){if(N.current)return N.current(C,S,L);L()}},[C,S,L,N,T]);const z=s.useMemo(()=>({reference:P,floating:E,setReference:b,setFloating:x}),[b,x]),O=s.useMemo(()=>({reference:C,floating:S}),[C,S]),F=s.useMemo(()=>{const D={position:n,left:0,top:0};if(!O.floating)return D;const R=Jn(O.floating,l.x),U=Jn(O.floating,l.y);return c?{...D,transform:"translate("+R+"px, "+U+"px)",...Hr(O.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:R,top:U}},[n,c,O.floating,l.x,l.y]);return s.useMemo(()=>({...l,update:L,refs:z,elements:O,floatingStyles:F}),[l,L,z,O,F])}const bc=e=>{function t(n){return{}.hasOwnProperty.call(n,"current")}return{name:"arrow",options:e,fn(n){const{element:r,padding:o}=typeof e=="function"?e(n):e;return r&&t(r)?r.current!=null?Qn({element:r.current,padding:o}).fn(n):{}:r?Qn({element:r,padding:o}).fn(n):{}}}},xc=(e,t)=>({...uc(e),options:[e,t]}),Cc=(e,t)=>({...dc(e),options:[e,t]}),Sc=(e,t)=>({...hc(e),options:[e,t]}),Ec=(e,t)=>({...fc(e),options:[e,t]}),Rc=(e,t)=>({...pc(e),options:[e,t]}),Pc=(e,t)=>({...mc(e),options:[e,t]}),Mc=(e,t)=>({...bc(e),options:[e,t]});var Ac="Arrow",zr=s.forwardRef((e,t)=>{const{children:n,width:r=10,height:o=5,...a}=e;return w.jsx(B.svg,{...a,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:w.jsx("polygon",{points:"0,0 30,0 15,10"})})});zr.displayName=Ac;var Tc=zr,dn="Popper",[Ur,At]=be(dn),[Nc,Vr]=Ur(dn),Gr=e=>{const{__scopePopper:t,children:n}=e,[r,o]=s.useState(null);return w.jsx(Nc,{scope:t,anchor:r,onAnchorChange:o,children:n})};Gr.displayName=dn;var Kr="PopperAnchor",Yr=s.forwardRef((e,t)=>{const{__scopePopper:n,virtualRef:r,...o}=e,a=Vr(Kr,n),i=s.useRef(null),c=j(t,i);return s.useEffect(()=>{a.onAnchorChange(r?.current||i.current)}),r?null:w.jsx(B.div,{...o,ref:c})});Yr.displayName=Kr;var fn="PopperContent",[Dc,Oc]=Ur(fn),Xr=s.forwardRef((e,t)=>{const{__scopePopper:n,side:r="bottom",sideOffset:o=0,align:a="center",alignOffset:i=0,arrowPadding:c=0,avoidCollisions:u=!0,collisionBoundary:d=[],collisionPadding:l=0,sticky:f="partial",hideWhenDetached:p=!1,updatePositionStrategy:m="optimized",onPlaced:v,...h}=e,g=Vr(fn,n),[y,b]=s.useState(null),x=j(t,W=>b(W)),[C,S]=s.useState(null),P=gi(C),E=P?.width??0,M=P?.height??0,T=r+(a!=="center"?"-"+a:""),N=typeof l=="number"?l:{top:0,right:0,bottom:0,left:0,...l},_=Array.isArray(d)?d:[d],k=_.length>0,L={padding:N,boundary:_.filter(Ic),altBoundary:k},{refs:I,floatingStyles:z,placement:O,isPositioned:F,middlewareData:D}=yc({strategy:"fixed",placement:T,whileElementsMounted:(...W)=>lc(...W,{animationFrame:m==="always"}),elements:{reference:g.anchor},middleware:[xc({mainAxis:o+M,alignmentAxis:i}),u&&Cc({mainAxis:!0,crossAxis:!1,limiter:f==="partial"?Sc():void 0,...L}),u&&Ec({...L}),Rc({...L,apply:({elements:W,rects:Fe,availableWidth:Ba,availableHeight:Wa})=>{const{width:Ha,height:za}=Fe.reference,et=W.floating.style;et.setProperty("--radix-popper-available-width",`${Ba}px`),et.setProperty("--radix-popper-available-height",`${Wa}px`),et.setProperty("--radix-popper-anchor-width",`${Ha}px`),et.setProperty("--radix-popper-anchor-height",`${za}px`)}}),C&&Mc({element:C,padding:c}),Lc({arrowWidth:E,arrowHeight:M}),p&&Pc({strategy:"referenceHidden",...L})]}),[R,U]=Qr(O),te=V(v);ce(()=>{F&&te?.()},[F,te]);const he=D.arrow?.x,ke=D.arrow?.y,je=D.arrow?.centerOffset!==0,[Je,ve]=s.useState();return ce(()=>{y&&ve(window.getComputedStyle(y).zIndex)},[y]),w.jsx("div",{ref:I.setFloating,"data-radix-popper-content-wrapper":"",style:{...z,transform:F?z.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:Je,"--radix-popper-transform-origin":[D.transformOrigin?.x,D.transformOrigin?.y].join(" "),...D.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:w.jsx(Dc,{scope:n,placedSide:R,onArrowChange:S,arrowX:he,arrowY:ke,shouldHideArrow:je,children:w.jsx(B.div,{"data-side":R,"data-align":U,...h,ref:x,style:{...h.style,animation:F?void 0:"none"}})})})});Xr.displayName=fn;var qr="PopperArrow",_c={top:"bottom",right:"left",bottom:"top",left:"right"},Zr=s.forwardRef(function(t,n){const{__scopePopper:r,...o}=t,a=Oc(qr,r),i=_c[a.placedSide];return w.jsx("span",{ref:a.onArrowChange,style:{position:"absolute",left:a.arrowX,top:a.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[a.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[a.placedSide],visibility:a.shouldHideArrow?"hidden":void 0},children:w.jsx(Tc,{...o,ref:n,style:{...o.style,display:"block"}})})});Zr.displayName=qr;function Ic(e){return e!==null}var Lc=e=>({name:"transformOrigin",options:e,fn(t){const{placement:n,rects:r,middlewareData:o}=t,i=o.arrow?.centerOffset!==0,c=i?0:e.arrowWidth,u=i?0:e.arrowHeight,[d,l]=Qr(n),f={start:"0%",center:"50%",end:"100%"}[l],p=(o.arrow?.x??0)+c/2,m=(o.arrow?.y??0)+u/2;let v="",h="";return d==="bottom"?(v=i?f:`${p}px`,h=`${-u}px`):d==="top"?(v=i?f:`${p}px`,h=`${r.floating.height+u}px`):d==="right"?(v=`${-u}px`,h=i?f:`${m}px`):d==="left"&&(v=`${r.floating.width+u}px`,h=i?f:`${m}px`),{data:{x:v,y:h}}}});function Qr(e){const[t,n="center"]=e.split("-");return[t,n]}var pn=Gr,Jr=Yr,eo=Xr,to=Zr,Ut="rovingFocusGroup.onEntryFocus",kc={bubbles:!1,cancelable:!0},Ye="RovingFocusGroup",[Zt,no,jc]=hr(Ye),[Fc,ro]=be(Ye,[jc]),[$c,Bc]=Fc(Ye),oo=s.forwardRef((e,t)=>w.jsx(Zt.Provider,{scope:e.__scopeRovingFocusGroup,children:w.jsx(Zt.Slot,{scope:e.__scopeRovingFocusGroup,children:w.jsx(Wc,{...e,ref:t})})}));oo.displayName=Ye;var Wc=s.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:n,orientation:r,loop:o=!1,dir:a,currentTabStopId:i,defaultCurrentTabStopId:c,onCurrentTabStopIdChange:u,onEntryFocus:d,preventScrollOnEntryFocus:l=!1,...f}=e,p=s.useRef(null),m=j(t,p),v=Ct(a),[h,g]=xt({prop:i,defaultProp:c??null,onChange:u,caller:Ye}),[y,b]=s.useState(!1),x=V(d),C=no(n),S=s.useRef(!1),[P,E]=s.useState(0);return s.useEffect(()=>{const M=p.current;if(M)return M.addEventListener(Ut,x),()=>M.removeEventListener(Ut,x)},[x]),w.jsx($c,{scope:n,orientation:r,dir:v,loop:o,currentTabStopId:h,onItemFocus:s.useCallback(M=>g(M),[g]),onItemShiftTab:s.useCallback(()=>b(!0),[]),onFocusableItemAdd:s.useCallback(()=>E(M=>M+1),[]),onFocusableItemRemove:s.useCallback(()=>E(M=>M-1),[]),children:w.jsx(B.div,{tabIndex:y||P===0?-1:0,"data-orientation":r,...f,ref:m,style:{outline:"none",...e.style},onMouseDown:A(e.onMouseDown,()=>{S.current=!0}),onFocus:A(e.onFocus,M=>{const T=!S.current;if(M.target===M.currentTarget&&T&&!y){const N=new CustomEvent(Ut,kc);if(M.currentTarget.dispatchEvent(N),!N.defaultPrevented){const _=C().filter(O=>O.focusable),k=_.find(O=>O.active),L=_.find(O=>O.id===h),z=[k,L,..._].filter(Boolean).map(O=>O.ref.current);io(z,l)}}S.current=!1}),onBlur:A(e.onBlur,()=>b(!1))})})}),ao="RovingFocusGroupItem",so=s.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:n,focusable:r=!0,active:o=!1,tabStopId:a,children:i,...c}=e,u=Ae(),d=a||u,l=Bc(ao,n),f=l.currentTabStopId===d,p=no(n),{onFocusableItemAdd:m,onFocusableItemRemove:v,currentTabStopId:h}=l;return s.useEffect(()=>{if(r)return m(),()=>v()},[r,m,v]),w.jsx(Zt.ItemSlot,{scope:n,id:d,focusable:r,active:o,children:w.jsx(B.span,{tabIndex:f?0:-1,"data-orientation":l.orientation,...c,ref:t,onMouseDown:A(e.onMouseDown,g=>{r?l.onItemFocus(d):g.preventDefault()}),onFocus:A(e.onFocus,()=>l.onItemFocus(d)),onKeyDown:A(e.onKeyDown,g=>{if(g.key==="Tab"&&g.shiftKey){l.onItemShiftTab();return}if(g.target!==g.currentTarget)return;const y=Uc(g,l.orientation,l.dir);if(y!==void 0){if(g.metaKey||g.ctrlKey||g.altKey||g.shiftKey)return;g.preventDefault();let x=p().filter(C=>C.focusable).map(C=>C.ref.current);if(y==="last")x.reverse();else if(y==="prev"||y==="next"){y==="prev"&&x.reverse();const C=x.indexOf(g.currentTarget);x=l.loop?Vc(x,C+1):x.slice(C+1)}setTimeout(()=>io(x))}}),children:typeof i=="function"?i({isCurrentTabStop:f,hasTabStop:h!=null}):i})})});so.displayName=ao;var Hc={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function zc(e,t){return t!=="rtl"?e:e==="ArrowLeft"?"ArrowRight":e==="ArrowRight"?"ArrowLeft":e}function Uc(e,t,n){const r=zc(e.key,n);if(!(t==="vertical"&&["ArrowLeft","ArrowRight"].includes(r))&&!(t==="horizontal"&&["ArrowUp","ArrowDown"].includes(r)))return Hc[r]}function io(e,t=!1){const n=document.activeElement;for(const r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}function Vc(e,t){return e.map((n,r)=>e[(t+r)%e.length])}var Gc=oo,Kc=so,Qt=["Enter"," "],Yc=["ArrowDown","PageUp","Home"],co=["ArrowUp","PageDown","End"],Xc=[...Yc,...co],qc={ltr:[...Qt,"ArrowRight"],rtl:[...Qt,"ArrowLeft"]},Zc={ltr:["ArrowLeft"],rtl:["ArrowRight"]},Xe="Menu",[Ue,Qc,Jc]=hr(Xe),[xe,lo]=be(Xe,[Jc,At,ro]),qe=At(),uo=ro(),[fo,me]=xe(Xe),[el,Ze]=xe(Xe),po=e=>{const{__scopeMenu:t,open:n=!1,children:r,dir:o,onOpenChange:a,modal:i=!0}=e,c=qe(t),[u,d]=s.useState(null),l=s.useRef(!1),f=V(a),p=Ct(o);return s.useEffect(()=>{const m=()=>{l.current=!0,document.addEventListener("pointerdown",v,{capture:!0,once:!0}),document.addEventListener("pointermove",v,{capture:!0,once:!0})},v=()=>l.current=!1;return document.addEventListener("keydown",m,{capture:!0}),()=>{document.removeEventListener("keydown",m,{capture:!0}),document.removeEventListener("pointerdown",v,{capture:!0}),document.removeEventListener("pointermove",v,{capture:!0})}},[]),w.jsx(pn,{...c,children:w.jsx(fo,{scope:t,open:n,onOpenChange:f,content:u,onContentChange:d,children:w.jsx(el,{scope:t,onClose:s.useCallback(()=>f(!1),[f]),isUsingKeyboardRef:l,dir:p,modal:i,children:r})})})};po.displayName=Xe;var tl="MenuAnchor",mn=s.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e,o=qe(n);return w.jsx(Jr,{...o,...r,ref:t})});mn.displayName=tl;var hn="MenuPortal",[nl,mo]=xe(hn,{forceMount:void 0}),ho=e=>{const{__scopeMenu:t,forceMount:n,children:r,container:o}=e,a=me(hn,t);return w.jsx(nl,{scope:t,forceMount:n,children:w.jsx(ee,{present:n||a.open,children:w.jsx(St,{asChild:!0,container:o,children:r})})})};ho.displayName=hn;var q="MenuContent",[rl,vn]=xe(q),vo=s.forwardRef((e,t)=>{const n=mo(q,e.__scopeMenu),{forceMount:r=n.forceMount,...o}=e,a=me(q,e.__scopeMenu),i=Ze(q,e.__scopeMenu);return w.jsx(Ue.Provider,{scope:e.__scopeMenu,children:w.jsx(ee,{present:r||a.open,children:w.jsx(Ue.Slot,{scope:e.__scopeMenu,children:i.modal?w.jsx(ol,{...o,ref:t}):w.jsx(al,{...o,ref:t})})})})}),ol=s.forwardRef((e,t)=>{const n=me(q,e.__scopeMenu),r=s.useRef(null),o=j(t,r);return s.useEffect(()=>{const a=r.current;if(a)return vi(a)},[]),w.jsx(gn,{...e,ref:o,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:A(e.onFocusOutside,a=>a.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),al=s.forwardRef((e,t)=>{const n=me(q,e.__scopeMenu);return w.jsx(gn,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),sl=Vt("MenuContent.ScrollLock"),gn=s.forwardRef((e,t)=>{const{__scopeMenu:n,loop:r=!1,trapFocus:o,onOpenAutoFocus:a,onCloseAutoFocus:i,disableOutsidePointerEvents:c,onEntryFocus:u,onEscapeKeyDown:d,onPointerDownOutside:l,onFocusOutside:f,onInteractOutside:p,onDismiss:m,disableOutsideScroll:v,...h}=e,g=me(q,n),y=Ze(q,n),b=qe(n),x=uo(n),C=Qc(n),[S,P]=s.useState(null),E=s.useRef(null),M=j(t,E,g.onContentChange),T=s.useRef(0),N=s.useRef(""),_=s.useRef(0),k=s.useRef(null),L=s.useRef("right"),I=s.useRef(0),z=v?Nr:s.Fragment,O=v?{as:sl,allowPinchZoom:!0}:void 0,F=R=>{const U=N.current+R,te=C().filter(W=>!W.disabled),he=document.activeElement,ke=te.find(W=>W.ref.current===he)?.textValue,je=te.map(W=>W.textValue),Je=wl(je,U,ke),ve=te.find(W=>W.textValue===Je)?.ref.current;(function W(Fe){N.current=Fe,window.clearTimeout(T.current),Fe!==""&&(T.current=window.setTimeout(()=>W(""),1e3))})(U),ve&&setTimeout(()=>ve.focus())};s.useEffect(()=>()=>window.clearTimeout(T.current),[]),Ns();const D=s.useCallback(R=>L.current===k.current?.side&&bl(R,k.current?.area),[]);return w.jsx(rl,{scope:n,searchRef:N,onItemEnter:s.useCallback(R=>{D(R)&&R.preventDefault()},[D]),onItemLeave:s.useCallback(R=>{D(R)||(E.current?.focus(),P(null))},[D]),onTriggerLeave:s.useCallback(R=>{D(R)&&R.preventDefault()},[D]),pointerGraceTimerRef:_,onPointerGraceIntentChange:s.useCallback(R=>{k.current=R},[]),children:w.jsx(z,{...O,children:w.jsx(xr,{asChild:!0,trapped:o,onMountAutoFocus:A(a,R=>{R.preventDefault(),E.current?.focus({preventScroll:!0})}),onUnmountAutoFocus:i,children:w.jsx(nn,{asChild:!0,disableOutsidePointerEvents:c,onEscapeKeyDown:d,onPointerDownOutside:l,onFocusOutside:f,onInteractOutside:p,onDismiss:m,children:w.jsx(Gc,{asChild:!0,...x,dir:y.dir,orientation:"vertical",loop:r,currentTabStopId:S,onCurrentTabStopIdChange:P,onEntryFocus:A(u,R=>{y.isUsingKeyboardRef.current||R.preventDefault()}),preventScrollOnEntryFocus:!0,children:w.jsx(eo,{role:"menu","aria-orientation":"vertical","data-state":_o(g.open),"data-radix-menu-content":"",dir:y.dir,...b,...h,ref:M,style:{outline:"none",...h.style},onKeyDown:A(h.onKeyDown,R=>{const te=R.target.closest("[data-radix-menu-content]")===R.currentTarget,he=R.ctrlKey||R.altKey||R.metaKey,ke=R.key.length===1;te&&(R.key==="Tab"&&R.preventDefault(),!he&&ke&&F(R.key));const je=E.current;if(R.target!==je||!Xc.includes(R.key))return;R.preventDefault();const ve=C().filter(W=>!W.disabled).map(W=>W.ref.current);co.includes(R.key)&&ve.reverse(),vl(ve)}),onBlur:A(e.onBlur,R=>{R.currentTarget.contains(R.target)||(window.clearTimeout(T.current),N.current="")}),onPointerMove:A(e.onPointerMove,Ve(R=>{const U=R.target,te=I.current!==R.clientX;if(R.currentTarget.contains(U)&&te){const he=R.clientX>I.current?"right":"left";L.current=he,I.current=R.clientX}}))})})})})})})});vo.displayName=q;var il="MenuGroup",wn=s.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e;return w.jsx(B.div,{role:"group",...r,ref:t})});wn.displayName=il;var cl="MenuLabel",go=s.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e;return w.jsx(B.div,{...r,ref:t})});go.displayName=cl;var mt="MenuItem",er="menu.itemSelect",Tt=s.forwardRef((e,t)=>{const{disabled:n=!1,onSelect:r,...o}=e,a=s.useRef(null),i=Ze(mt,e.__scopeMenu),c=vn(mt,e.__scopeMenu),u=j(t,a),d=s.useRef(!1),l=()=>{const f=a.current;if(!n&&f){const p=new CustomEvent(er,{bubbles:!0,cancelable:!0});f.addEventListener(er,m=>r?.(m),{once:!0}),dr(f,p),p.defaultPrevented?d.current=!1:i.onClose()}};return w.jsx(wo,{...o,ref:u,disabled:n,onClick:A(e.onClick,l),onPointerDown:f=>{e.onPointerDown?.(f),d.current=!0},onPointerUp:A(e.onPointerUp,f=>{d.current||f.currentTarget?.click()}),onKeyDown:A(e.onKeyDown,f=>{const p=c.searchRef.current!=="";n||p&&f.key===" "||Qt.includes(f.key)&&(f.currentTarget.click(),f.preventDefault())})})});Tt.displayName=mt;var wo=s.forwardRef((e,t)=>{const{__scopeMenu:n,disabled:r=!1,textValue:o,...a}=e,i=vn(mt,n),c=uo(n),u=s.useRef(null),d=j(t,u),[l,f]=s.useState(!1),[p,m]=s.useState("");return s.useEffect(()=>{const v=u.current;v&&m((v.textContent??"").trim())},[a.children]),w.jsx(Ue.ItemSlot,{scope:n,disabled:r,textValue:o??p,children:w.jsx(Kc,{asChild:!0,...c,focusable:!r,children:w.jsx(B.div,{role:"menuitem","data-highlighted":l?"":void 0,"aria-disabled":r||void 0,"data-disabled":r?"":void 0,...a,ref:d,onPointerMove:A(e.onPointerMove,Ve(v=>{r?i.onItemLeave(v):(i.onItemEnter(v),v.defaultPrevented||v.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:A(e.onPointerLeave,Ve(v=>i.onItemLeave(v))),onFocus:A(e.onFocus,()=>f(!0)),onBlur:A(e.onBlur,()=>f(!1))})})})}),ll="MenuCheckboxItem",yo=s.forwardRef((e,t)=>{const{checked:n=!1,onCheckedChange:r,...o}=e;return w.jsx(Eo,{scope:e.__scopeMenu,checked:n,children:w.jsx(Tt,{role:"menuitemcheckbox","aria-checked":ht(n)?"mixed":n,...o,ref:t,"data-state":xn(n),onSelect:A(o.onSelect,()=>r?.(ht(n)?!0:!n),{checkForDefaultPrevented:!1})})})});yo.displayName=ll;var bo="MenuRadioGroup",[ul,dl]=xe(bo,{value:void 0,onValueChange:()=>{}}),xo=s.forwardRef((e,t)=>{const{value:n,onValueChange:r,...o}=e,a=V(r);return w.jsx(ul,{scope:e.__scopeMenu,value:n,onValueChange:a,children:w.jsx(wn,{...o,ref:t})})});xo.displayName=bo;var Co="MenuRadioItem",So=s.forwardRef((e,t)=>{const{value:n,...r}=e,o=dl(Co,e.__scopeMenu),a=n===o.value;return w.jsx(Eo,{scope:e.__scopeMenu,checked:a,children:w.jsx(Tt,{role:"menuitemradio","aria-checked":a,...r,ref:t,"data-state":xn(a),onSelect:A(r.onSelect,()=>o.onValueChange?.(n),{checkForDefaultPrevented:!1})})})});So.displayName=Co;var yn="MenuItemIndicator",[Eo,fl]=xe(yn,{checked:!1}),Ro=s.forwardRef((e,t)=>{const{__scopeMenu:n,forceMount:r,...o}=e,a=fl(yn,n);return w.jsx(ee,{present:r||ht(a.checked)||a.checked===!0,children:w.jsx(B.span,{...o,ref:t,"data-state":xn(a.checked)})})});Ro.displayName=yn;var pl="MenuSeparator",Po=s.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e;return w.jsx(B.div,{role:"separator","aria-orientation":"horizontal",...r,ref:t})});Po.displayName=pl;var ml="MenuArrow",Mo=s.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e,o=qe(n);return w.jsx(to,{...o,...r,ref:t})});Mo.displayName=ml;var bn="MenuSub",[hl,Ao]=xe(bn),To=e=>{const{__scopeMenu:t,children:n,open:r=!1,onOpenChange:o}=e,a=me(bn,t),i=qe(t),[c,u]=s.useState(null),[d,l]=s.useState(null),f=V(o);return s.useEffect(()=>(a.open===!1&&f(!1),()=>f(!1)),[a.open,f]),w.jsx(pn,{...i,children:w.jsx(fo,{scope:t,open:r,onOpenChange:f,content:d,onContentChange:l,children:w.jsx(hl,{scope:t,contentId:Ae(),triggerId:Ae(),trigger:c,onTriggerChange:u,children:n})})})};To.displayName=bn;var Be="MenuSubTrigger",No=s.forwardRef((e,t)=>{const n=me(Be,e.__scopeMenu),r=Ze(Be,e.__scopeMenu),o=Ao(Be,e.__scopeMenu),a=vn(Be,e.__scopeMenu),i=s.useRef(null),{pointerGraceTimerRef:c,onPointerGraceIntentChange:u}=a,d={__scopeMenu:e.__scopeMenu},l=s.useCallback(()=>{i.current&&window.clearTimeout(i.current),i.current=null},[]);return s.useEffect(()=>l,[l]),s.useEffect(()=>{const f=c.current;return()=>{window.clearTimeout(f),u(null)}},[c,u]),w.jsx(mn,{asChild:!0,...d,children:w.jsx(wo,{id:o.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":o.contentId,"data-state":_o(n.open),...e,ref:ir(t,o.onTriggerChange),onClick:f=>{e.onClick?.(f),!(e.disabled||f.defaultPrevented)&&(f.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:A(e.onPointerMove,Ve(f=>{a.onItemEnter(f),!f.defaultPrevented&&!e.disabled&&!n.open&&!i.current&&(a.onPointerGraceIntentChange(null),i.current=window.setTimeout(()=>{n.onOpenChange(!0),l()},100))})),onPointerLeave:A(e.onPointerLeave,Ve(f=>{l();const p=n.content?.getBoundingClientRect();if(p){const m=n.content?.dataset.side,v=m==="right",h=v?-5:5,g=p[v?"left":"right"],y=p[v?"right":"left"];a.onPointerGraceIntentChange({area:[{x:f.clientX+h,y:f.clientY},{x:g,y:p.top},{x:y,y:p.top},{x:y,y:p.bottom},{x:g,y:p.bottom}],side:m}),window.clearTimeout(c.current),c.current=window.setTimeout(()=>a.onPointerGraceIntentChange(null),300)}else{if(a.onTriggerLeave(f),f.defaultPrevented)return;a.onPointerGraceIntentChange(null)}})),onKeyDown:A(e.onKeyDown,f=>{const p=a.searchRef.current!=="";e.disabled||p&&f.key===" "||qc[r.dir].includes(f.key)&&(n.onOpenChange(!0),n.content?.focus(),f.preventDefault())})})})});No.displayName=Be;var Do="MenuSubContent",Oo=s.forwardRef((e,t)=>{const n=mo(q,e.__scopeMenu),{forceMount:r=n.forceMount,...o}=e,a=me(q,e.__scopeMenu),i=Ze(q,e.__scopeMenu),c=Ao(Do,e.__scopeMenu),u=s.useRef(null),d=j(t,u);return w.jsx(Ue.Provider,{scope:e.__scopeMenu,children:w.jsx(ee,{present:r||a.open,children:w.jsx(Ue.Slot,{scope:e.__scopeMenu,children:w.jsx(gn,{id:c.contentId,"aria-labelledby":c.triggerId,...o,ref:d,align:"start",side:i.dir==="rtl"?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:l=>{i.isUsingKeyboardRef.current&&u.current?.focus(),l.preventDefault()},onCloseAutoFocus:l=>l.preventDefault(),onFocusOutside:A(e.onFocusOutside,l=>{l.target!==c.trigger&&a.onOpenChange(!1)}),onEscapeKeyDown:A(e.onEscapeKeyDown,l=>{i.onClose(),l.preventDefault()}),onKeyDown:A(e.onKeyDown,l=>{const f=l.currentTarget.contains(l.target),p=Zc[i.dir].includes(l.key);f&&p&&(a.onOpenChange(!1),c.trigger?.focus(),l.preventDefault())})})})})})});Oo.displayName=Do;function _o(e){return e?"open":"closed"}function ht(e){return e==="indeterminate"}function xn(e){return ht(e)?"indeterminate":e?"checked":"unchecked"}function vl(e){const t=document.activeElement;for(const n of e)if(n===t||(n.focus(),document.activeElement!==t))return}function gl(e,t){return e.map((n,r)=>e[(t+r)%e.length])}function wl(e,t,n){const o=t.length>1&&Array.from(t).every(d=>d===t[0])?t[0]:t,a=n?e.indexOf(n):-1;let i=gl(e,Math.max(a,0));o.length===1&&(i=i.filter(d=>d!==n));const u=i.find(d=>d.toLowerCase().startsWith(o.toLowerCase()));return u!==n?u:void 0}function yl(e,t){const{x:n,y:r}=e;let o=!1;for(let a=0,i=t.length-1;a<t.length;i=a++){const c=t[a],u=t[i],d=c.x,l=c.y,f=u.x,p=u.y;l>r!=p>r&&n<(f-d)*(r-l)/(p-l)+d&&(o=!o)}return o}function bl(e,t){if(!t)return!1;const n={x:e.clientX,y:e.clientY};return yl(n,t)}function Ve(e){return t=>t.pointerType==="mouse"?e(t):void 0}var xl=po,Cl=mn,Sl=ho,El=vo,Rl=wn,Pl=go,Ml=Tt,Al=yo,Tl=xo,Nl=So,Dl=Ro,Ol=Po,_l=Mo,Il=To,Ll=No,kl=Oo,Nt="DropdownMenu",[jl,Fd]=be(Nt,[lo]),G=lo(),[Fl,Io]=jl(Nt),Lo=e=>{const{__scopeDropdownMenu:t,children:n,dir:r,open:o,defaultOpen:a,onOpenChange:i,modal:c=!0}=e,u=G(t),d=s.useRef(null),[l,f]=xt({prop:o,defaultProp:a??!1,onChange:i,caller:Nt});return w.jsx(Fl,{scope:t,triggerId:Ae(),triggerRef:d,contentId:Ae(),open:l,onOpenChange:f,onOpenToggle:s.useCallback(()=>f(p=>!p),[f]),modal:c,children:w.jsx(xl,{...u,open:l,onOpenChange:f,dir:r,modal:c,children:n})})};Lo.displayName=Nt;var ko="DropdownMenuTrigger",jo=s.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,disabled:r=!1,...o}=e,a=Io(ko,n),i=G(n);return w.jsx(Cl,{asChild:!0,...i,children:w.jsx(B.button,{type:"button",id:a.triggerId,"aria-haspopup":"menu","aria-expanded":a.open,"aria-controls":a.open?a.contentId:void 0,"data-state":a.open?"open":"closed","data-disabled":r?"":void 0,disabled:r,...o,ref:ir(t,a.triggerRef),onPointerDown:A(e.onPointerDown,c=>{!r&&c.button===0&&c.ctrlKey===!1&&(a.onOpenToggle(),a.open||c.preventDefault())}),onKeyDown:A(e.onKeyDown,c=>{r||(["Enter"," "].includes(c.key)&&a.onOpenToggle(),c.key==="ArrowDown"&&a.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(c.key)&&c.preventDefault())})})})});jo.displayName=ko;var $l="DropdownMenuPortal",Fo=e=>{const{__scopeDropdownMenu:t,...n}=e,r=G(t);return w.jsx(Sl,{...r,...n})};Fo.displayName=$l;var $o="DropdownMenuContent",Bo=s.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=Io($o,n),a=G(n),i=s.useRef(!1);return w.jsx(El,{id:o.contentId,"aria-labelledby":o.triggerId,...a,...r,ref:t,onCloseAutoFocus:A(e.onCloseAutoFocus,c=>{i.current||o.triggerRef.current?.focus(),i.current=!1,c.preventDefault()}),onInteractOutside:A(e.onInteractOutside,c=>{const u=c.detail.originalEvent,d=u.button===0&&u.ctrlKey===!0,l=u.button===2||d;(!o.modal||l)&&(i.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});Bo.displayName=$o;var Bl="DropdownMenuGroup",Wo=s.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=G(n);return w.jsx(Rl,{...o,...r,ref:t})});Wo.displayName=Bl;var Wl="DropdownMenuLabel",Ho=s.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=G(n);return w.jsx(Pl,{...o,...r,ref:t})});Ho.displayName=Wl;var Hl="DropdownMenuItem",zo=s.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=G(n);return w.jsx(Ml,{...o,...r,ref:t})});zo.displayName=Hl;var zl="DropdownMenuCheckboxItem",Uo=s.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=G(n);return w.jsx(Al,{...o,...r,ref:t})});Uo.displayName=zl;var Ul="DropdownMenuRadioGroup",Vo=s.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=G(n);return w.jsx(Tl,{...o,...r,ref:t})});Vo.displayName=Ul;var Vl="DropdownMenuRadioItem",Go=s.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=G(n);return w.jsx(Nl,{...o,...r,ref:t})});Go.displayName=Vl;var Gl="DropdownMenuItemIndicator",Ko=s.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=G(n);return w.jsx(Dl,{...o,...r,ref:t})});Ko.displayName=Gl;var Kl="DropdownMenuSeparator",Yo=s.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=G(n);return w.jsx(Ol,{...o,...r,ref:t})});Yo.displayName=Kl;var Yl="DropdownMenuArrow",Xl=s.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=G(n);return w.jsx(_l,{...o,...r,ref:t})});Xl.displayName=Yl;var ql=e=>{const{__scopeDropdownMenu:t,children:n,open:r,onOpenChange:o,defaultOpen:a}=e,i=G(t),[c,u]=xt({prop:r,defaultProp:a??!1,onChange:o,caller:"DropdownMenuSub"});return w.jsx(Il,{...i,open:c,onOpenChange:u,children:n})},Zl="DropdownMenuSubTrigger",Xo=s.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=G(n);return w.jsx(Ll,{...o,...r,ref:t})});Xo.displayName=Zl;var Ql="DropdownMenuSubContent",qo=s.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=G(n);return w.jsx(kl,{...o,...r,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});qo.displayName=Ql;var Jl=Lo,eu=jo,Zo=Fo,tu=Bo,nu=Wo,ru=Ho,ou=zo,au=Uo,su=Vo,iu=Go,Qo=Ko,cu=Yo,lu=ql,uu=Xo,du=qo;function fu(e,[t,n]){return Math.min(n,Math.max(t,e))}function pu(e,t){return s.useReducer((n,r)=>t[n][r]??n,e)}var Cn="ScrollArea",[Jo,$d]=be(Cn),[mu,Z]=Jo(Cn),ea=s.forwardRef((e,t)=>{const{__scopeScrollArea:n,type:r="hover",dir:o,scrollHideDelay:a=600,...i}=e,[c,u]=s.useState(null),[d,l]=s.useState(null),[f,p]=s.useState(null),[m,v]=s.useState(null),[h,g]=s.useState(null),[y,b]=s.useState(0),[x,C]=s.useState(0),[S,P]=s.useState(!1),[E,M]=s.useState(!1),T=j(t,_=>u(_)),N=Ct(o);return w.jsx(mu,{scope:n,type:r,dir:N,scrollHideDelay:a,scrollArea:c,viewport:d,onViewportChange:l,content:f,onContentChange:p,scrollbarX:m,onScrollbarXChange:v,scrollbarXEnabled:S,onScrollbarXEnabledChange:P,scrollbarY:h,onScrollbarYChange:g,scrollbarYEnabled:E,onScrollbarYEnabledChange:M,onCornerWidthChange:b,onCornerHeightChange:C,children:w.jsx(B.div,{dir:N,...i,ref:T,style:{position:"relative","--radix-scroll-area-corner-width":y+"px","--radix-scroll-area-corner-height":x+"px",...e.style}})})});ea.displayName=Cn;var ta="ScrollAreaViewport",na=s.forwardRef((e,t)=>{const{__scopeScrollArea:n,children:r,nonce:o,...a}=e,i=Z(ta,n),c=s.useRef(null),u=j(t,c,i.onViewportChange);return w.jsxs(w.Fragment,{children:[w.jsx("style",{dangerouslySetInnerHTML:{__html:"[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}"},nonce:o}),w.jsx(B.div,{"data-radix-scroll-area-viewport":"",...a,ref:u,style:{overflowX:i.scrollbarXEnabled?"scroll":"hidden",overflowY:i.scrollbarYEnabled?"scroll":"hidden",...e.style},children:w.jsx("div",{ref:i.onContentChange,style:{minWidth:"100%",display:"table"},children:r})})]})});na.displayName=ta;var ie="ScrollAreaScrollbar",ra=s.forwardRef((e,t)=>{const{forceMount:n,...r}=e,o=Z(ie,e.__scopeScrollArea),{onScrollbarXEnabledChange:a,onScrollbarYEnabledChange:i}=o,c=e.orientation==="horizontal";return s.useEffect(()=>(c?a(!0):i(!0),()=>{c?a(!1):i(!1)}),[c,a,i]),o.type==="hover"?w.jsx(hu,{...r,ref:t,forceMount:n}):o.type==="scroll"?w.jsx(vu,{...r,ref:t,forceMount:n}):o.type==="auto"?w.jsx(oa,{...r,ref:t,forceMount:n}):o.type==="always"?w.jsx(Sn,{...r,ref:t}):null});ra.displayName=ie;var hu=s.forwardRef((e,t)=>{const{forceMount:n,...r}=e,o=Z(ie,e.__scopeScrollArea),[a,i]=s.useState(!1);return s.useEffect(()=>{const c=o.scrollArea;let u=0;if(c){const d=()=>{window.clearTimeout(u),i(!0)},l=()=>{u=window.setTimeout(()=>i(!1),o.scrollHideDelay)};return c.addEventListener("pointerenter",d),c.addEventListener("pointerleave",l),()=>{window.clearTimeout(u),c.removeEventListener("pointerenter",d),c.removeEventListener("pointerleave",l)}}},[o.scrollArea,o.scrollHideDelay]),w.jsx(ee,{present:n||a,children:w.jsx(oa,{"data-state":a?"visible":"hidden",...r,ref:t})})}),vu=s.forwardRef((e,t)=>{const{forceMount:n,...r}=e,o=Z(ie,e.__scopeScrollArea),a=e.orientation==="horizontal",i=Ot(()=>u("SCROLL_END"),100),[c,u]=pu("hidden",{hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}});return s.useEffect(()=>{if(c==="idle"){const d=window.setTimeout(()=>u("HIDE"),o.scrollHideDelay);return()=>window.clearTimeout(d)}},[c,o.scrollHideDelay,u]),s.useEffect(()=>{const d=o.viewport,l=a?"scrollLeft":"scrollTop";if(d){let f=d[l];const p=()=>{const m=d[l];f!==m&&(u("SCROLL"),i()),f=m};return d.addEventListener("scroll",p),()=>d.removeEventListener("scroll",p)}},[o.viewport,a,u,i]),w.jsx(ee,{present:n||c!=="hidden",children:w.jsx(Sn,{"data-state":c==="hidden"?"hidden":"visible",...r,ref:t,onPointerEnter:A(e.onPointerEnter,()=>u("POINTER_ENTER")),onPointerLeave:A(e.onPointerLeave,()=>u("POINTER_LEAVE"))})})}),oa=s.forwardRef((e,t)=>{const n=Z(ie,e.__scopeScrollArea),{forceMount:r,...o}=e,[a,i]=s.useState(!1),c=e.orientation==="horizontal",u=Ot(()=>{if(n.viewport){const d=n.viewport.offsetWidth<n.viewport.scrollWidth,l=n.viewport.offsetHeight<n.viewport.scrollHeight;i(c?d:l)}},10);return Ne(n.viewport,u),Ne(n.content,u),w.jsx(ee,{present:r||a,children:w.jsx(Sn,{"data-state":a?"visible":"hidden",...o,ref:t})})}),Sn=s.forwardRef((e,t)=>{const{orientation:n="vertical",...r}=e,o=Z(ie,e.__scopeScrollArea),a=s.useRef(null),i=s.useRef(0),[c,u]=s.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),d=la(c.viewport,c.content),l={...r,sizes:c,onSizesChange:u,hasThumb:d>0&&d<1,onThumbChange:p=>a.current=p,onThumbPointerUp:()=>i.current=0,onThumbPointerDown:p=>i.current=p};function f(p,m){return Cu(p,i.current,c,m)}return n==="horizontal"?w.jsx(gu,{...l,ref:t,onThumbPositionChange:()=>{if(o.viewport&&a.current){const p=o.viewport.scrollLeft,m=tr(p,c,o.dir);a.current.style.transform=`translate3d(${m}px, 0, 0)`}},onWheelScroll:p=>{o.viewport&&(o.viewport.scrollLeft=p)},onDragScroll:p=>{o.viewport&&(o.viewport.scrollLeft=f(p,o.dir))}}):n==="vertical"?w.jsx(wu,{...l,ref:t,onThumbPositionChange:()=>{if(o.viewport&&a.current){const p=o.viewport.scrollTop,m=tr(p,c);a.current.style.transform=`translate3d(0, ${m}px, 0)`}},onWheelScroll:p=>{o.viewport&&(o.viewport.scrollTop=p)},onDragScroll:p=>{o.viewport&&(o.viewport.scrollTop=f(p))}}):null}),gu=s.forwardRef((e,t)=>{const{sizes:n,onSizesChange:r,...o}=e,a=Z(ie,e.__scopeScrollArea),[i,c]=s.useState(),u=s.useRef(null),d=j(t,u,a.onScrollbarXChange);return s.useEffect(()=>{u.current&&c(getComputedStyle(u.current))},[u]),w.jsx(sa,{"data-orientation":"horizontal",...o,ref:d,sizes:n,style:{bottom:0,left:a.dir==="rtl"?"var(--radix-scroll-area-corner-width)":0,right:a.dir==="ltr"?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":Dt(n)+"px",...e.style},onThumbPointerDown:l=>e.onThumbPointerDown(l.x),onDragScroll:l=>e.onDragScroll(l.x),onWheelScroll:(l,f)=>{if(a.viewport){const p=a.viewport.scrollLeft+l.deltaX;e.onWheelScroll(p),da(p,f)&&l.preventDefault()}},onResize:()=>{u.current&&a.viewport&&i&&r({content:a.viewport.scrollWidth,viewport:a.viewport.offsetWidth,scrollbar:{size:u.current.clientWidth,paddingStart:gt(i.paddingLeft),paddingEnd:gt(i.paddingRight)}})}})}),wu=s.forwardRef((e,t)=>{const{sizes:n,onSizesChange:r,...o}=e,a=Z(ie,e.__scopeScrollArea),[i,c]=s.useState(),u=s.useRef(null),d=j(t,u,a.onScrollbarYChange);return s.useEffect(()=>{u.current&&c(getComputedStyle(u.current))},[u]),w.jsx(sa,{"data-orientation":"vertical",...o,ref:d,sizes:n,style:{top:0,right:a.dir==="ltr"?0:void 0,left:a.dir==="rtl"?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":Dt(n)+"px",...e.style},onThumbPointerDown:l=>e.onThumbPointerDown(l.y),onDragScroll:l=>e.onDragScroll(l.y),onWheelScroll:(l,f)=>{if(a.viewport){const p=a.viewport.scrollTop+l.deltaY;e.onWheelScroll(p),da(p,f)&&l.preventDefault()}},onResize:()=>{u.current&&a.viewport&&i&&r({content:a.viewport.scrollHeight,viewport:a.viewport.offsetHeight,scrollbar:{size:u.current.clientHeight,paddingStart:gt(i.paddingTop),paddingEnd:gt(i.paddingBottom)}})}})}),[yu,aa]=Jo(ie),sa=s.forwardRef((e,t)=>{const{__scopeScrollArea:n,sizes:r,hasThumb:o,onThumbChange:a,onThumbPointerUp:i,onThumbPointerDown:c,onThumbPositionChange:u,onDragScroll:d,onWheelScroll:l,onResize:f,...p}=e,m=Z(ie,n),[v,h]=s.useState(null),g=j(t,T=>h(T)),y=s.useRef(null),b=s.useRef(""),x=m.viewport,C=r.content-r.viewport,S=V(l),P=V(u),E=Ot(f,10);function M(T){if(y.current){const N=T.clientX-y.current.left,_=T.clientY-y.current.top;d({x:N,y:_})}}return s.useEffect(()=>{const T=N=>{const _=N.target;v?.contains(_)&&S(N,C)};return document.addEventListener("wheel",T,{passive:!1}),()=>document.removeEventListener("wheel",T,{passive:!1})},[x,v,C,S]),s.useEffect(P,[r,P]),Ne(v,E),Ne(m.content,E),w.jsx(yu,{scope:n,scrollbar:v,hasThumb:o,onThumbChange:V(a),onThumbPointerUp:V(i),onThumbPositionChange:P,onThumbPointerDown:V(c),children:w.jsx(B.div,{...p,ref:g,style:{position:"absolute",...p.style},onPointerDown:A(e.onPointerDown,T=>{T.button===0&&(T.target.setPointerCapture(T.pointerId),y.current=v.getBoundingClientRect(),b.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",m.viewport&&(m.viewport.style.scrollBehavior="auto"),M(T))}),onPointerMove:A(e.onPointerMove,M),onPointerUp:A(e.onPointerUp,T=>{const N=T.target;N.hasPointerCapture(T.pointerId)&&N.releasePointerCapture(T.pointerId),document.body.style.webkitUserSelect=b.current,m.viewport&&(m.viewport.style.scrollBehavior=""),y.current=null})})})}),vt="ScrollAreaThumb",ia=s.forwardRef((e,t)=>{const{forceMount:n,...r}=e,o=aa(vt,e.__scopeScrollArea);return w.jsx(ee,{present:n||o.hasThumb,children:w.jsx(bu,{ref:t,...r})})}),bu=s.forwardRef((e,t)=>{const{__scopeScrollArea:n,style:r,...o}=e,a=Z(vt,n),i=aa(vt,n),{onThumbPositionChange:c}=i,u=j(t,f=>i.onThumbChange(f)),d=s.useRef(void 0),l=Ot(()=>{d.current&&(d.current(),d.current=void 0)},100);return s.useEffect(()=>{const f=a.viewport;if(f){const p=()=>{if(l(),!d.current){const m=Su(f,c);d.current=m,c()}};return c(),f.addEventListener("scroll",p),()=>f.removeEventListener("scroll",p)}},[a.viewport,l,c]),w.jsx(B.div,{"data-state":i.hasThumb?"visible":"hidden",...o,ref:u,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...r},onPointerDownCapture:A(e.onPointerDownCapture,f=>{const m=f.target.getBoundingClientRect(),v=f.clientX-m.left,h=f.clientY-m.top;i.onThumbPointerDown({x:v,y:h})}),onPointerUp:A(e.onPointerUp,i.onThumbPointerUp)})});ia.displayName=vt;var En="ScrollAreaCorner",ca=s.forwardRef((e,t)=>{const n=Z(En,e.__scopeScrollArea),r=!!(n.scrollbarX&&n.scrollbarY);return n.type!=="scroll"&&r?w.jsx(xu,{...e,ref:t}):null});ca.displayName=En;var xu=s.forwardRef((e,t)=>{const{__scopeScrollArea:n,...r}=e,o=Z(En,n),[a,i]=s.useState(0),[c,u]=s.useState(0),d=!!(a&&c);return Ne(o.scrollbarX,()=>{const l=o.scrollbarX?.offsetHeight||0;o.onCornerHeightChange(l),u(l)}),Ne(o.scrollbarY,()=>{const l=o.scrollbarY?.offsetWidth||0;o.onCornerWidthChange(l),i(l)}),d?w.jsx(B.div,{...r,ref:t,style:{width:a,height:c,position:"absolute",right:o.dir==="ltr"?0:void 0,left:o.dir==="rtl"?0:void 0,bottom:0,...e.style}}):null});function gt(e){return e?parseInt(e,10):0}function la(e,t){const n=e/t;return isNaN(n)?0:n}function Dt(e){const t=la(e.viewport,e.content),n=e.scrollbar.paddingStart+e.scrollbar.paddingEnd,r=(e.scrollbar.size-n)*t;return Math.max(r,18)}function Cu(e,t,n,r="ltr"){const o=Dt(n),a=o/2,i=t||a,c=o-i,u=n.scrollbar.paddingStart+i,d=n.scrollbar.size-n.scrollbar.paddingEnd-c,l=n.content-n.viewport,f=r==="ltr"?[0,l]:[l*-1,0];return ua([u,d],f)(e)}function tr(e,t,n="ltr"){const r=Dt(t),o=t.scrollbar.paddingStart+t.scrollbar.paddingEnd,a=t.scrollbar.size-o,i=t.content-t.viewport,c=a-r,u=n==="ltr"?[0,i]:[i*-1,0],d=fu(e,u);return ua([0,i],[0,c])(d)}function ua(e,t){return n=>{if(e[0]===e[1]||t[0]===t[1])return t[0];const r=(t[1]-t[0])/(e[1]-e[0]);return t[0]+r*(n-e[0])}}function da(e,t){return e>0&&e<t}var Su=(e,t=()=>{})=>{let n={left:e.scrollLeft,top:e.scrollTop},r=0;return function o(){const a={left:e.scrollLeft,top:e.scrollTop},i=n.left!==a.left,c=n.top!==a.top;(i||c)&&t(),n=a,r=window.requestAnimationFrame(o)}(),()=>window.cancelAnimationFrame(r)};function Ot(e,t){const n=V(e),r=s.useRef(0);return s.useEffect(()=>()=>window.clearTimeout(r.current),[]),s.useCallback(()=>{window.clearTimeout(r.current),r.current=window.setTimeout(n,t)},[n,t])}function Ne(e,t){const n=V(t);ce(()=>{let r=0;if(e){const o=new ResizeObserver(()=>{cancelAnimationFrame(r),r=window.requestAnimationFrame(n)});return o.observe(e),()=>{window.cancelAnimationFrame(r),o.unobserve(e)}}},[e,n])}var Eu=ea,Ru=na,nr=ra,rr=ia,Pu=ca,[_t,Bd]=be("Tooltip",[At]),It=At(),fa="TooltipProvider",Mu=700,Jt="tooltip.open",[Au,Rn]=_t(fa),pa=e=>{const{__scopeTooltip:t,delayDuration:n=Mu,skipDelayDuration:r=300,disableHoverableContent:o=!1,children:a}=e,i=s.useRef(!0),c=s.useRef(!1),u=s.useRef(0);return s.useEffect(()=>{const d=u.current;return()=>window.clearTimeout(d)},[]),w.jsx(Au,{scope:t,isOpenDelayedRef:i,delayDuration:n,onOpen:s.useCallback(()=>{window.clearTimeout(u.current),i.current=!1},[]),onClose:s.useCallback(()=>{window.clearTimeout(u.current),u.current=window.setTimeout(()=>i.current=!0,r)},[r]),isPointerInTransitRef:c,onPointerInTransitChange:s.useCallback(d=>{c.current=d},[]),disableHoverableContent:o,children:a})};pa.displayName=fa;var Ge="Tooltip",[Tu,Qe]=_t(Ge),ma=e=>{const{__scopeTooltip:t,children:n,open:r,defaultOpen:o,onOpenChange:a,disableHoverableContent:i,delayDuration:c}=e,u=Rn(Ge,e.__scopeTooltip),d=It(t),[l,f]=s.useState(null),p=Ae(),m=s.useRef(0),v=i??u.disableHoverableContent,h=c??u.delayDuration,g=s.useRef(!1),[y,b]=xt({prop:r,defaultProp:o??!1,onChange:E=>{E?(u.onOpen(),document.dispatchEvent(new CustomEvent(Jt))):u.onClose(),a?.(E)},caller:Ge}),x=s.useMemo(()=>y?g.current?"delayed-open":"instant-open":"closed",[y]),C=s.useCallback(()=>{window.clearTimeout(m.current),m.current=0,g.current=!1,b(!0)},[b]),S=s.useCallback(()=>{window.clearTimeout(m.current),m.current=0,b(!1)},[b]),P=s.useCallback(()=>{window.clearTimeout(m.current),m.current=window.setTimeout(()=>{g.current=!0,b(!0),m.current=0},h)},[h,b]);return s.useEffect(()=>()=>{m.current&&(window.clearTimeout(m.current),m.current=0)},[]),w.jsx(pn,{...d,children:w.jsx(Tu,{scope:t,contentId:p,open:y,stateAttribute:x,trigger:l,onTriggerChange:f,onTriggerEnter:s.useCallback(()=>{u.isOpenDelayedRef.current?P():C()},[u.isOpenDelayedRef,P,C]),onTriggerLeave:s.useCallback(()=>{v?S():(window.clearTimeout(m.current),m.current=0)},[S,v]),onOpen:C,onClose:S,disableHoverableContent:v,children:n})})};ma.displayName=Ge;var en="TooltipTrigger",ha=s.forwardRef((e,t)=>{const{__scopeTooltip:n,...r}=e,o=Qe(en,n),a=Rn(en,n),i=It(n),c=s.useRef(null),u=j(t,c,o.onTriggerChange),d=s.useRef(!1),l=s.useRef(!1),f=s.useCallback(()=>d.current=!1,[]);return s.useEffect(()=>()=>document.removeEventListener("pointerup",f),[f]),w.jsx(Jr,{asChild:!0,...i,children:w.jsx(B.button,{"aria-describedby":o.open?o.contentId:void 0,"data-state":o.stateAttribute,...r,ref:u,onPointerMove:A(e.onPointerMove,p=>{p.pointerType!=="touch"&&!l.current&&!a.isPointerInTransitRef.current&&(o.onTriggerEnter(),l.current=!0)}),onPointerLeave:A(e.onPointerLeave,()=>{o.onTriggerLeave(),l.current=!1}),onPointerDown:A(e.onPointerDown,()=>{o.open&&o.onClose(),d.current=!0,document.addEventListener("pointerup",f,{once:!0})}),onFocus:A(e.onFocus,()=>{d.current||o.onOpen()}),onBlur:A(e.onBlur,o.onClose),onClick:A(e.onClick,o.onClose)})})});ha.displayName=en;var Pn="TooltipPortal",[Nu,Du]=_t(Pn,{forceMount:void 0}),va=e=>{const{__scopeTooltip:t,forceMount:n,children:r,container:o}=e,a=Qe(Pn,t);return w.jsx(Nu,{scope:t,forceMount:n,children:w.jsx(ee,{present:n||a.open,children:w.jsx(St,{asChild:!0,container:o,children:r})})})};va.displayName=Pn;var De="TooltipContent",ga=s.forwardRef((e,t)=>{const n=Du(De,e.__scopeTooltip),{forceMount:r=n.forceMount,side:o="top",...a}=e,i=Qe(De,e.__scopeTooltip);return w.jsx(ee,{present:r||i.open,children:i.disableHoverableContent?w.jsx(wa,{side:o,...a,ref:t}):w.jsx(Ou,{side:o,...a,ref:t})})}),Ou=s.forwardRef((e,t)=>{const n=Qe(De,e.__scopeTooltip),r=Rn(De,e.__scopeTooltip),o=s.useRef(null),a=j(t,o),[i,c]=s.useState(null),{trigger:u,onClose:d}=n,l=o.current,{onPointerInTransitChange:f}=r,p=s.useCallback(()=>{c(null),f(!1)},[f]),m=s.useCallback((v,h)=>{const g=v.currentTarget,y={x:v.clientX,y:v.clientY},b=ku(y,g.getBoundingClientRect()),x=ju(y,b),C=Fu(h.getBoundingClientRect()),S=Bu([...x,...C]);c(S),f(!0)},[f]);return s.useEffect(()=>()=>p(),[p]),s.useEffect(()=>{if(u&&l){const v=g=>m(g,l),h=g=>m(g,u);return u.addEventListener("pointerleave",v),l.addEventListener("pointerleave",h),()=>{u.removeEventListener("pointerleave",v),l.removeEventListener("pointerleave",h)}}},[u,l,m,p]),s.useEffect(()=>{if(i){const v=h=>{const g=h.target,y={x:h.clientX,y:h.clientY},b=u?.contains(g)||l?.contains(g),x=!$u(y,i);b?p():x&&(p(),d())};return document.addEventListener("pointermove",v),()=>document.removeEventListener("pointermove",v)}},[u,l,i,d,p]),w.jsx(wa,{...e,ref:a})}),[_u,Iu]=_t(Ge,{isInside:!1}),Lu=Ua("TooltipContent"),wa=s.forwardRef((e,t)=>{const{__scopeTooltip:n,children:r,"aria-label":o,onEscapeKeyDown:a,onPointerDownOutside:i,...c}=e,u=Qe(De,n),d=It(n),{onClose:l}=u;return s.useEffect(()=>(document.addEventListener(Jt,l),()=>document.removeEventListener(Jt,l)),[l]),s.useEffect(()=>{if(u.trigger){const f=p=>{p.target?.contains(u.trigger)&&l()};return window.addEventListener("scroll",f,{capture:!0}),()=>window.removeEventListener("scroll",f,{capture:!0})}},[u.trigger,l]),w.jsx(nn,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:a,onPointerDownOutside:i,onFocusOutside:f=>f.preventDefault(),onDismiss:l,children:w.jsxs(eo,{"data-state":u.stateAttribute,...d,...c,ref:t,style:{...c.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[w.jsx(Lu,{children:r}),w.jsx(_u,{scope:n,isInside:!0,children:w.jsx(Ja,{id:u.contentId,role:"tooltip",children:o||r})})]})})});ga.displayName=De;var ya="TooltipArrow",ba=s.forwardRef((e,t)=>{const{__scopeTooltip:n,...r}=e,o=It(n);return Iu(ya,n).isInside?null:w.jsx(to,{...o,...r,ref:t})});ba.displayName=ya;function ku(e,t){const n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),a=Math.abs(t.left-e.x);switch(Math.min(n,r,o,a)){case a:return"left";case o:return"right";case n:return"top";case r:return"bottom";default:throw new Error("unreachable")}}function ju(e,t,n=5){const r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n});break}return r}function Fu(e){const{top:t,right:n,bottom:r,left:o}=e;return[{x:o,y:t},{x:n,y:t},{x:n,y:r},{x:o,y:r}]}function $u(e,t){const{x:n,y:r}=e;let o=!1;for(let a=0,i=t.length-1;a<t.length;i=a++){const c=t[a],u=t[i],d=c.x,l=c.y,f=u.x,p=u.y;l>r!=p>r&&n<(f-d)*(r-l)/(p-l)+d&&(o=!o)}return o}function Bu(e){const t=e.slice();return t.sort((n,r)=>n.x<r.x?-1:n.x>r.x?1:n.y<r.y?-1:n.y>r.y?1:0),Wu(t)}function Wu(e){if(e.length<=1)return e.slice();const t=[];for(let r=0;r<e.length;r++){const o=e[r];for(;t.length>=2;){const a=t[t.length-1],i=t[t.length-2];if((a.x-i.x)*(o.y-i.y)>=(a.y-i.y)*(o.x-i.x))t.pop();else break}t.push(o)}t.pop();const n=[];for(let r=e.length-1;r>=0;r--){const o=e[r];for(;n.length>=2;){const a=n[n.length-1],i=n[n.length-2];if((a.x-i.x)*(o.y-i.y)>=(a.y-i.y)*(o.x-i.x))n.pop();else break}n.push(o)}return n.pop(),t.length===1&&n.length===1&&t[0].x===n[0].x&&t[0].y===n[0].y?t:t.concat(n)}var Hu=pa,Wd=ma,Hd=ha,zd=va,Ud=ga,Vd=ba;function zu(e){switch(e){case"tomato":case"red":case"ruby":case"crimson":case"pink":case"plum":case"purple":case"violet":return"mauve";case"iris":case"indigo":case"blue":case"sky":case"cyan":return"slate";case"teal":case"jade":case"mint":case"green":return"sage";case"grass":case"lime":return"olive";case"yellow":case"amber":case"orange":case"brown":case"gold":case"bronze":return"sand";case"gray":return"gray"}}const Uu=["solid","translucent"],Vu=["90%","95%","100%","105%","110%"],K={hasBackground:{default:!0},appearance:{default:"inherit"},accentColor:{values:Va,default:"indigo"},grayColor:{values:Ga,default:"auto"},panelBackground:{values:Uu,default:"translucent"},radius:{values:es,default:"medium"},scaling:{values:Vu,default:"100%"}},Re=()=>{},wt=s.createContext(void 0);function Gu(){const e=s.useContext(wt);if(e===void 0)throw new Error("`useThemeContext` must be used within a `Theme`");return e}const Mn=s.forwardRef((e,t)=>s.useContext(wt)===void 0?s.createElement(Hu,{delayDuration:200},s.createElement(wr,{dir:"ltr"},s.createElement(xa,{...e,ref:t}))):s.createElement(An,{...e,ref:t}));Mn.displayName="Theme";const xa=s.forwardRef((e,t)=>{const{appearance:n=K.appearance.default,accentColor:r=K.accentColor.default,grayColor:o=K.grayColor.default,panelBackground:a=K.panelBackground.default,radius:i=K.radius.default,scaling:c=K.scaling.default,hasBackground:u=K.hasBackground.default,...d}=e,[l,f]=s.useState(n);s.useEffect(()=>f(n),[n]);const[p,m]=s.useState(r);s.useEffect(()=>m(r),[r]);const[v,h]=s.useState(o);s.useEffect(()=>h(o),[o]);const[g,y]=s.useState(a);s.useEffect(()=>y(a),[a]);const[b,x]=s.useState(i);s.useEffect(()=>x(i),[i]);const[C,S]=s.useState(c);return s.useEffect(()=>S(c),[c]),s.createElement(An,{...d,ref:t,isRoot:!0,hasBackground:u,appearance:l,accentColor:p,grayColor:v,panelBackground:g,radius:b,scaling:C,onAppearanceChange:f,onAccentColorChange:m,onGrayColorChange:h,onPanelBackgroundChange:y,onRadiusChange:x,onScalingChange:S})});xa.displayName="ThemeRoot";const An=s.forwardRef((e,t)=>{const n=s.useContext(wt),{asChild:r,isRoot:o,hasBackground:a,appearance:i=n?.appearance??K.appearance.default,accentColor:c=n?.accentColor??K.accentColor.default,grayColor:u=n?.resolvedGrayColor??K.grayColor.default,panelBackground:d=n?.panelBackground??K.panelBackground.default,radius:l=n?.radius??K.radius.default,scaling:f=n?.scaling??K.scaling.default,onAppearanceChange:p=Re,onAccentColorChange:m=Re,onGrayColorChange:v=Re,onPanelBackgroundChange:h=Re,onRadiusChange:g=Re,onScalingChange:y=Re,...b}=e,x=r?cr:"div",C=u==="auto"?zu(c):u,S=e.appearance==="light"||e.appearance==="dark",P=a===void 0?o||S:a;return s.createElement(wt.Provider,{value:s.useMemo(()=>({appearance:i,accentColor:c,grayColor:u,resolvedGrayColor:C,panelBackground:d,radius:l,scaling:f,onAppearanceChange:p,onAccentColorChange:m,onGrayColorChange:v,onPanelBackgroundChange:h,onRadiusChange:g,onScalingChange:y}),[i,c,u,C,d,l,f,p,m,v,h,g,y])},s.createElement(x,{"data-is-root-theme":o?"true":"false","data-accent-color":c,"data-gray-color":C,"data-has-background":P?"true":"false","data-panel-background":d,"data-radius":l,"data-scaling":f,ref:t,...b,className:$("radix-themes",{light:i==="light",dark:i==="dark"},b.className)}))});An.displayName="ThemeImpl";const Ku=e=>{if(!s.isValidElement(e))throw Error(`Expected a single React Element child, but got: ${s.Children.toArray(e).map(t=>typeof t=="object"&&"type"in t&&typeof t.type=="string"?t.type:typeof t).join(", ")}`);return e},Yu=["1","2","3"],Xu=["solid","soft","surface","outline"],qu={...Oe,size:{type:"enum",className:"rt-r-size",values:Yu,default:"1",responsive:!0},variant:{type:"enum",className:"rt-variant",values:Xu,default:"soft"},...lr,...tn,...fr},Zu=s.forwardRef((e,t)=>{const{asChild:n,className:r,color:o,radius:a,...i}=_e(e,qu,yt),c=n?cr:"span";return s.createElement(c,{"data-accent-color":o,"data-radius":a,...i,ref:t,className:$("rt-reset","rt-Badge",r)})});Zu.displayName="Badge";const Qu=["div","span"],Ju=["none","inline","inline-block","block","contents"],ed={as:{type:"enum",values:Qu,default:"div"},...Oe,display:{type:"enum",className:"rt-r-display",values:Ju,responsive:!0}},td=s.forwardRef((e,t)=>{const{className:n,asChild:r,as:o="div",...a}=_e(e,ed,mr,yt);return s.createElement(r?pr:o,{...a,ref:t,className:$("rt-Box",n)})});td.displayName="Box";const nd=["div","span"],rd=["none","inline-grid","grid"],od=["1","2","3","4","5","6","7","8","9"],ad=["1","2","3","4","5","6","7","8","9"],sd=["row","column","dense","row-dense","column-dense"],id=["start","center","end","baseline","stretch"],cd=["start","center","end","between"],Ca={as:{type:"enum",values:nd,default:"div"},...Oe,display:{type:"enum",className:"rt-r-display",values:rd,responsive:!0},areas:{type:"string",className:"rt-r-gta",customProperties:["--grid-template-areas"],responsive:!0},columns:{type:"enum | string",className:"rt-r-gtc",customProperties:["--grid-template-columns"],values:od,parseValue:or,responsive:!0},rows:{type:"enum | string",className:"rt-r-gtr",customProperties:["--grid-template-rows"],values:ad,parseValue:or,responsive:!0},flow:{type:"enum",className:"rt-r-gaf",values:sd,responsive:!0},align:{type:"enum",className:"rt-r-ai",values:id,responsive:!0},justify:{type:"enum",className:"rt-r-jc",values:cd,parseValue:ld,responsive:!0},...ts};function or(e){return Ca.columns.values.includes(e)?e:e?.match(/^\d+$/)?`repeat(${e}, minmax(0, 1fr))`:e}function ld(e){return e==="between"?"space-between":e}const ud=s.forwardRef((e,t)=>{const{className:n,asChild:r,as:o="div",...a}=_e(e,Ca,mr,yt);return s.createElement(r?pr:o,{...a,ref:t,className:$("rt-Grid",n)})});ud.displayName="Grid";const dd=H.forwardRef((e,t)=>H.createElement("svg",{width:"9",height:"9",viewBox:"0 0 9 9",fill:"currentcolor",xmlns:"http://www.w3.org/2000/svg",...e,ref:t},H.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M0.75 4.5C0.75 4.08579 1.08579 3.75 1.5 3.75H7.5C7.91421 3.75 8.25 4.08579 8.25 4.5C8.25 4.91421 7.91421 5.25 7.5 5.25H1.5C1.08579 5.25 0.75 4.91421 0.75 4.5Z"})));dd.displayName="ThickDividerHorizontalIcon";const Tn=H.forwardRef((e,t)=>H.createElement("svg",{width:"9",height:"9",viewBox:"0 0 9 9",fill:"currentcolor",xmlns:"http://www.w3.org/2000/svg",...e,ref:t},H.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M8.53547 0.62293C8.88226 0.849446 8.97976 1.3142 8.75325 1.66099L4.5083 8.1599C4.38833 8.34356 4.19397 8.4655 3.9764 8.49358C3.75883 8.52167 3.53987 8.45309 3.3772 8.30591L0.616113 5.80777C0.308959 5.52987 0.285246 5.05559 0.563148 4.74844C0.84105 4.44128 1.31533 4.41757 1.62249 4.69547L3.73256 6.60459L7.49741 0.840706C7.72393 0.493916 8.18868 0.396414 8.53547 0.62293Z"})));Tn.displayName="ThickCheckIcon";const Sa=H.forwardRef((e,t)=>H.createElement("svg",{width:"9",height:"9",viewBox:"0 0 9 9",fill:"currentcolor",xmlns:"http://www.w3.org/2000/svg",...e,ref:t},H.createElement("path",{d:"M0.135232 3.15803C0.324102 2.95657 0.640521 2.94637 0.841971 3.13523L4.5 6.56464L8.158 3.13523C8.3595 2.94637 8.6759 2.95657 8.8648 3.15803C9.0536 3.35949 9.0434 3.67591 8.842 3.86477L4.84197 7.6148C4.64964 7.7951 4.35036 7.7951 4.15803 7.6148L0.158031 3.86477C-0.0434285 3.67591 -0.0536285 3.35949 0.135232 3.15803Z"})));Sa.displayName="ChevronDownIcon";const Ea=H.forwardRef((e,t)=>H.createElement("svg",{width:"9",height:"9",viewBox:"0 0 9 9",fill:"currentcolor",xmlns:"http://www.w3.org/2000/svg",...e,ref:t},H.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M3.23826 0.201711C3.54108 -0.0809141 4.01567 -0.0645489 4.29829 0.238264L7.79829 3.98826C8.06724 4.27642 8.06724 4.72359 7.79829 5.01174L4.29829 8.76174C4.01567 9.06455 3.54108 9.08092 3.23826 8.79829C2.93545 8.51567 2.91909 8.04108 3.20171 7.73826L6.22409 4.5L3.20171 1.26174C2.91909 0.958928 2.93545 0.484337 3.23826 0.201711Z"})));Ea.displayName="ThickChevronRightIcon";const fd=["1","2","3"],$e={...Oe,size:{values:fd,default:"1"},...fr,scrollbars:{default:"both"}};function pd(e){const{m:t,mx:n,my:r,mt:o,mr:a,mb:i,ml:c,...u}=e;return{m:t,mx:n,my:r,mt:o,mr:a,mb:i,ml:c,rest:u}}const we=yt.m.values;function md(e){const[t,n]=ge({className:"rt-r-m",customProperties:["--margin"],propValues:we,value:e.m}),[r,o]=ge({className:"rt-r-mx",customProperties:["--margin-left","--margin-right"],propValues:we,value:e.mx}),[a,i]=ge({className:"rt-r-my",customProperties:["--margin-top","--margin-bottom"],propValues:we,value:e.my}),[c,u]=ge({className:"rt-r-mt",customProperties:["--margin-top"],propValues:we,value:e.mt}),[d,l]=ge({className:"rt-r-mr",customProperties:["--margin-right"],propValues:we,value:e.mr}),[f,p]=ge({className:"rt-r-mb",customProperties:["--margin-bottom"],propValues:we,value:e.mb}),[m,v]=ge({className:"rt-r-ml",customProperties:["--margin-left"],propValues:we,value:e.ml});return[$(t,r,a,c,d,f,m),ur(n,o,i,u,l,p,v)]}const Nn=s.forwardRef((e,t)=>{const{rest:n,...r}=pd(e),[o,a]=md(r),{asChild:i,children:c,className:u,style:d,type:l,scrollHideDelay:f=l!=="scroll"?0:void 0,dir:p,size:m=$e.size.default,radius:v=$e.radius.default,scrollbars:h=$e.scrollbars.default,...g}=n;return s.createElement(Eu,{type:l,scrollHideDelay:f,className:$("rt-ScrollAreaRoot",o,u),style:ur(a,d),asChild:i},ns({asChild:i,children:c},y=>s.createElement(s.Fragment,null,s.createElement(Ru,{...g,ref:t,className:"rt-ScrollAreaViewport"},y),s.createElement("div",{className:"rt-ScrollAreaViewportFocusRing"}),h!=="vertical"?s.createElement(nr,{"data-radius":v,orientation:"horizontal",className:$("rt-ScrollAreaScrollbar",Dn({className:"rt-r-size",value:m,propValues:$e.size.values}))},s.createElement(rr,{className:"rt-ScrollAreaThumb"})):null,h!=="horizontal"?s.createElement(nr,{"data-radius":v,orientation:"vertical",className:$("rt-ScrollAreaScrollbar",Dn({className:"rt-r-size",value:m,propValues:$e.size.values}))},s.createElement(rr,{className:"rt-ScrollAreaThumb"})):null,h==="both"?s.createElement(Pu,{className:"rt-ScrollAreaCorner"}):null)))});Nn.displayName="ScrollArea";const hd=["1","2"],vd=["solid","soft"],We={size:{type:"enum",className:"rt-r-size",values:hd,default:"2",responsive:!0},variant:{type:"enum",className:"rt-variant",values:vd,default:"solid"},...bt,...tn},gd={...Oe,...bt},wd={...bt},yd={...bt},Ra=e=>s.createElement(Jl,{...e});Ra.displayName="DropdownMenu.Root";const Pa=s.forwardRef(({children:e,...t},n)=>s.createElement(eu,{...t,ref:n,asChild:!0},Ku(e)));Pa.displayName="DropdownMenu.Trigger";const Ma=s.createContext({}),Aa=s.forwardRef((e,t)=>{const n=Gu(),{size:r=We.size.default,variant:o=We.variant.default,highContrast:a=We.highContrast.default}=e,{className:i,children:c,color:u,container:d,forceMount:l,...f}=_e(e,We),p=u||n.accentColor;return s.createElement(Zo,{container:d,forceMount:l},s.createElement(Mn,{asChild:!0},s.createElement(tu,{"data-accent-color":p,align:"start",sideOffset:4,collisionPadding:10,...f,asChild:!1,ref:t,className:$("rt-PopperContent","rt-BaseMenuContent","rt-DropdownMenuContent",i)},s.createElement(Nn,{type:"auto"},s.createElement("div",{className:$("rt-BaseMenuViewport","rt-DropdownMenuViewport")},s.createElement(Ma.Provider,{value:s.useMemo(()=>({size:r,variant:o,color:p,highContrast:a}),[r,o,p,a])},c))))))});Aa.displayName="DropdownMenu.Content";const Ta=s.forwardRef(({className:e,...t},n)=>s.createElement(ru,{...t,asChild:!1,ref:n,className:$("rt-BaseMenuLabel","rt-DropdownMenuLabel",e)}));Ta.displayName="DropdownMenu.Label";const Na=s.forwardRef((e,t)=>{const{className:n,children:r,color:o=gd.color.default,shortcut:a,...i}=e;return s.createElement(ou,{"data-accent-color":o,...i,ref:t,className:$("rt-reset","rt-BaseMenuItem","rt-DropdownMenuItem",n)},s.createElement(Ka,null,r),a&&s.createElement("div",{className:"rt-BaseMenuShortcut rt-DropdownMenuShortcut"},a))});Na.displayName="DropdownMenu.Item";const Da=s.forwardRef(({className:e,...t},n)=>s.createElement(nu,{...t,asChild:!1,ref:n,className:$("rt-BaseMenuGroup","rt-DropdownMenuGroup",e)}));Da.displayName="DropdownMenu.Group";const Oa=s.forwardRef(({className:e,...t},n)=>s.createElement(su,{...t,asChild:!1,ref:n,className:$("rt-BaseMenuRadioGroup","rt-DropdownMenuRadioGroup",e)}));Oa.displayName="DropdownMenu.RadioGroup";const _a=s.forwardRef((e,t)=>{const{children:n,className:r,color:o=yd.color.default,...a}=e;return s.createElement(iu,{...a,asChild:!1,ref:t,"data-accent-color":o,className:$("rt-BaseMenuItem","rt-BaseMenuRadioItem","rt-DropdownMenuItem","rt-DropdownMenuRadioItem",r)},n,s.createElement(Qo,{className:"rt-BaseMenuItemIndicator rt-DropdownMenuItemIndicator"},s.createElement(Tn,{className:"rt-BaseMenuItemIndicatorIcon rt-DropdownMenuItemIndicatorIcon"})))});_a.displayName="DropdownMenu.RadioItem";const Ia=s.forwardRef((e,t)=>{const{children:n,className:r,shortcut:o,color:a=wd.color.default,...i}=e;return s.createElement(au,{...i,asChild:!1,ref:t,"data-accent-color":a,className:$("rt-BaseMenuItem","rt-BaseMenuCheckboxItem","rt-DropdownMenuItem","rt-DropdownMenuCheckboxItem",r)},n,s.createElement(Qo,{className:"rt-BaseMenuItemIndicator rt-DropdownMenuItemIndicator"},s.createElement(Tn,{className:"rt-BaseMenuItemIndicatorIcon rt-ContextMenuItemIndicatorIcon"})),o&&s.createElement("div",{className:"rt-BaseMenuShortcut rt-DropdownMenuShortcut"},o))});Ia.displayName="DropdownMenu.CheckboxItem";const La=e=>s.createElement(lu,{...e});La.displayName="DropdownMenu.Sub";const ka=s.forwardRef((e,t)=>{const{className:n,children:r,...o}=e;return s.createElement(uu,{...o,asChild:!1,ref:t,className:$("rt-BaseMenuItem","rt-BaseMenuSubTrigger","rt-DropdownMenuItem","rt-DropdownMenuSubTrigger",n)},r,s.createElement("div",{className:"rt-BaseMenuShortcut rt-DropdownMenuShortcut"},s.createElement(Ea,{className:"rt-BaseMenuSubTriggerIcon rt-DropdownMenuSubtriggerIcon"})))});ka.displayName="DropdownMenu.SubTrigger";const ja=s.forwardRef((e,t)=>{const{size:n,variant:r,color:o,highContrast:a}=s.useContext(Ma),{className:i,children:c,container:u,forceMount:d,...l}=_e({size:n,variant:r,color:o,highContrast:a,...e},We);return s.createElement(Zo,{container:u,forceMount:d},s.createElement(Mn,{asChild:!0},s.createElement(du,{"data-accent-color":o,alignOffset:-Number(n)*4,sideOffset:1,collisionPadding:10,...l,asChild:!1,ref:t,className:$("rt-PopperContent","rt-BaseMenuContent","rt-BaseMenuSubContent","rt-DropdownMenuContent","rt-DropdownMenuSubContent",i)},s.createElement(Nn,{type:"auto"},s.createElement("div",{className:$("rt-BaseMenuViewport","rt-DropdownMenuViewport")},c)))))});ja.displayName="DropdownMenu.SubContent";const Fa=s.forwardRef(({className:e,...t},n)=>s.createElement(cu,{...t,asChild:!1,ref:n,className:$("rt-BaseMenuSeparator","rt-DropdownMenuSeparator",e)}));Fa.displayName="DropdownMenu.Separator";const Gd=Object.freeze(Object.defineProperty({__proto__:null,CheckboxItem:Ia,Content:Aa,Group:Da,Item:Na,Label:Ta,RadioGroup:Oa,RadioItem:_a,Root:Ra,Separator:Fa,Sub:La,SubContent:ja,SubTrigger:ka,Trigger:Pa,TriggerIcon:Sa},Symbol.toStringTag,{value:"Module"})),bd=["1","2","3","4","5","6","7","8","9"],xd=["auto","always","hover","none"],Cd={...Oe,size:{type:"enum",className:"rt-r-size",values:bd,responsive:!0},...Za,...qa,...Xa,...Ya,underline:{type:"enum",className:"rt-underline",values:xd,default:"auto"},...lr,...tn},Sd=s.forwardRef((e,t)=>{const{children:n,className:r,color:o,asChild:a,...i}=_e(e,Cd);return s.createElement(Qa,{...i,"data-accent-color":o,ref:t,asChild:!0,className:$("rt-reset","rt-Link",r)},a?n:s.createElement("a",null,n))});Sd.displayName="Link";/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ed=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),Rd=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(t,n,r)=>r?r.toUpperCase():n.toLowerCase()),ar=e=>{const t=Rd(e);return t.charAt(0).toUpperCase()+t.slice(1)},$a=(...e)=>e.filter((t,n,r)=>!!t&&t.trim()!==""&&r.indexOf(t)===n).join(" ").trim(),Pd=e=>{for(const t in e)if(t.startsWith("aria-")||t==="role"||t==="title")return!0};/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var Md={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ad=s.forwardRef(({color:e="currentColor",size:t=24,strokeWidth:n=2,absoluteStrokeWidth:r,className:o="",children:a,iconNode:i,...c},u)=>s.createElement("svg",{ref:u,...Md,width:t,height:t,stroke:e,strokeWidth:r?Number(n)*24/Number(t):n,className:$a("lucide",o),...!a&&!Pd(c)&&{"aria-hidden":"true"},...c},[...i.map(([d,l])=>s.createElement(d,l)),...Array.isArray(a)?a:[a]]));/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Kd=(e,t)=>{const n=s.forwardRef(({className:r,...o},a)=>s.createElement(Ad,{ref:a,iconNode:t,className:$a(`lucide-${Ed(ar(e))}`,`lucide-${e}`,r),...o}));return n.displayName=ar(e),n};export{gd as $,Cl as A,Ct as B,Al as C,nn as D,hr as E,xr as F,Rl as G,ro as H,Dl as I,Kc as J,Gc as K,Pl as L,gi as M,fu as N,Ku as O,ee as P,Mn as Q,Nr as R,Ol as S,kd as T,Ca as U,Tn as V,ud as W,wd as X,Gu as Y,We as Z,Nn as _,Ld as a,yd as a0,Ea as a1,jd as a2,td as a3,K as a4,zu as a5,Wd as a6,Hd as a7,zd as a8,Ud as a9,Vd as aa,Zu as ab,Sa as ac,Gd as ad,Sd as ae,wt as af,dd as ag,Kd as ah,Ra as ai,Pa as aj,Aa as ak,Na as al,Fa as am,Eu as an,Ru as ao,nr as ap,rr as aq,A as b,be as c,St as d,Ae as e,Ns as f,V as g,vi as h,ce as i,lo as j,Sl as k,El as l,Ml as m,Tl as n,Nl as o,xl as p,Il as q,kl as r,Ll as s,_l as t,xt as u,At as v,pn as w,Jr as x,eo as y,to as z};
