import{a as e}from"./chunk-QMGIS6GS-suYYFPSk.js";import{u as j,c as ft,d as mt,e as be,v as _e,y as te,r as ht}from"./text-DCkbNTq3.js";import{d as vt,c as St,i as G,E as gt,b as N,e as Ie,v as Me,B as xt,u as Pe,w as wt,x as Ct,h as yt,f as It,R as Tt,F as Et,D as Nt,g as Pt,y as Rt,N as Re,z as bt,Y as _t,Q as Mt,an as At,ao as Ot,ap as Dt,aq as Lt,V as jt,ac as Vt}from"./createLucideIcon-BOfs0RvG.js";import{P as M,V as kt,a as Bt}from"./button-Ccwu8jNm.js";import{a as Ae}from"./index-XOwJfM4g.js";import{j as u}from"./jsx-runtime-D_zvdyIk.js";function Ht(o){const n=e.useRef({value:o,previous:o});return e.useMemo(()=>(n.current.value!==o&&(n.current.previous=n.current.value,n.current.value=o),n.current.previous),[o])}var Ft=[" ","Enter","ArrowUp","ArrowDown"],Wt=[" ","Enter"],Q="Select",[ie,de,zt]=gt(Q),[oe,Do]=St(Q,[zt,Me]),ue=Me(),[Ut,$]=oe(Q),[Kt,Gt]=oe(Q),Oe=o=>{const{__scopeSelect:n,children:t,open:l,defaultOpen:s,onOpenChange:d,value:r,defaultValue:a,onValueChange:c,dir:p,name:S,autoComplete:w,disabled:P,required:T,form:y}=o,i=ue(n),[h,g]=e.useState(null),[m,v]=e.useState(null),[W,A]=e.useState(!1),ne=xt(p),[R,D]=Pe({prop:l,defaultProp:s??!1,onChange:d,caller:Q}),[z,q]=Pe({prop:r,defaultProp:a,onChange:c,caller:Q}),V=e.useRef(null),k=h?y||!!h.closest("form"):!0,[U,B]=e.useState(new Set),H=Array.from(U).map(b=>b.props.value).join(";");return u.jsx(wt,{...i,children:u.jsxs(Ut,{required:T,scope:n,trigger:h,onTriggerChange:g,valueNode:m,onValueNodeChange:v,valueNodeHasChildren:W,onValueNodeHasChildrenChange:A,contentId:Ie(),value:z,onValueChange:q,open:R,onOpenChange:D,dir:ne,triggerPointerDownPosRef:V,disabled:P,children:[u.jsx(ie.Provider,{scope:n,children:u.jsx(Kt,{scope:o.__scopeSelect,onNativeOptionAdd:e.useCallback(b=>{B(L=>new Set(L).add(b))},[]),onNativeOptionRemove:e.useCallback(b=>{B(L=>{const F=new Set(L);return F.delete(b),F})},[]),children:t})}),k?u.jsxs(ot,{"aria-hidden":!0,required:T,tabIndex:-1,name:S,autoComplete:w,value:z,onChange:b=>q(b.target.value),disabled:P,form:y,children:[z===void 0?u.jsx("option",{value:""}):null,Array.from(U)]},H):null]})})};Oe.displayName=Q;var De="SelectTrigger",Le=e.forwardRef((o,n)=>{const{__scopeSelect:t,disabled:l=!1,...s}=o,d=ue(t),r=$(De,t),a=r.disabled||l,c=j(n,r.onTriggerChange),p=de(t),S=e.useRef("touch"),[w,P,T]=rt(i=>{const h=p().filter(v=>!v.disabled),g=h.find(v=>v.value===r.value),m=st(h,i,g);m!==void 0&&r.onValueChange(m.value)}),y=i=>{a||(r.onOpenChange(!0),T()),i&&(r.triggerPointerDownPosRef.current={x:Math.round(i.pageX),y:Math.round(i.pageY)})};return u.jsx(Ct,{asChild:!0,...d,children:u.jsx(M.button,{type:"button",role:"combobox","aria-controls":r.contentId,"aria-expanded":r.open,"aria-required":r.required,"aria-autocomplete":"none",dir:r.dir,"data-state":r.open?"open":"closed",disabled:a,"data-disabled":a?"":void 0,"data-placeholder":nt(r.value)?"":void 0,...s,ref:c,onClick:N(s.onClick,i=>{i.currentTarget.focus(),S.current!=="mouse"&&y(i)}),onPointerDown:N(s.onPointerDown,i=>{S.current=i.pointerType;const h=i.target;h.hasPointerCapture(i.pointerId)&&h.releasePointerCapture(i.pointerId),i.button===0&&i.ctrlKey===!1&&i.pointerType==="mouse"&&(y(i),i.preventDefault())}),onKeyDown:N(s.onKeyDown,i=>{const h=w.current!=="";!(i.ctrlKey||i.altKey||i.metaKey)&&i.key.length===1&&P(i.key),!(h&&i.key===" ")&&Ft.includes(i.key)&&(y(),i.preventDefault())})})})});Le.displayName=De;var je="SelectValue",Ve=e.forwardRef((o,n)=>{const{__scopeSelect:t,className:l,style:s,children:d,placeholder:r="",...a}=o,c=$(je,t),{onValueNodeHasChildrenChange:p}=c,S=d!==void 0,w=j(n,c.onValueNodeChange);return G(()=>{p(S)},[p,S]),u.jsx(M.span,{...a,ref:w,style:{pointerEvents:"none"},children:nt(c.value)?u.jsx(u.Fragment,{children:r}):d})});Ve.displayName=je;var $t="SelectIcon",ke=e.forwardRef((o,n)=>{const{__scopeSelect:t,children:l,...s}=o;return u.jsx(M.span,{"aria-hidden":!0,...s,ref:n,children:l||"▼"})});ke.displayName=$t;var Yt="SelectPortal",Be=o=>u.jsx(vt,{asChild:!0,...o});Be.displayName=Yt;var J="SelectContent",He=e.forwardRef((o,n)=>{const t=$(J,o.__scopeSelect),[l,s]=e.useState();if(G(()=>{s(new DocumentFragment)},[]),!t.open){const d=l;return d?Ae.createPortal(u.jsx(Fe,{scope:o.__scopeSelect,children:u.jsx(ie.Slot,{scope:o.__scopeSelect,children:u.jsx("div",{children:o.children})})}),d):null}return u.jsx(We,{...o,ref:n})});He.displayName=J;var O=10,[Fe,Y]=oe(J),qt="SelectContentImpl",Xt=ft("SelectContent.RemoveScroll"),We=e.forwardRef((o,n)=>{const{__scopeSelect:t,position:l="item-aligned",onCloseAutoFocus:s,onEscapeKeyDown:d,onPointerDownOutside:r,side:a,sideOffset:c,align:p,alignOffset:S,arrowPadding:w,collisionBoundary:P,collisionPadding:T,sticky:y,hideWhenDetached:i,avoidCollisions:h,...g}=o,m=$(J,t),[v,W]=e.useState(null),[A,ne]=e.useState(null),R=j(n,f=>W(f)),[D,z]=e.useState(null),[q,V]=e.useState(null),k=de(t),[U,B]=e.useState(!1),H=e.useRef(!1);e.useEffect(()=>{if(v)return yt(v)},[v]),It();const b=e.useCallback(f=>{const[I,..._]=k().map(E=>E.ref.current),[x]=_.slice(-1),C=document.activeElement;for(const E of f)if(E===C||(E?.scrollIntoView({block:"nearest"}),E===I&&A&&(A.scrollTop=0),E===x&&A&&(A.scrollTop=A.scrollHeight),E?.focus(),document.activeElement!==C))return},[k,A]),L=e.useCallback(()=>b([D,v]),[b,D,v]);e.useEffect(()=>{U&&L()},[U,L]);const{onOpenChange:F,triggerPointerDownPosRef:K}=m;e.useEffect(()=>{if(v){let f={x:0,y:0};const I=x=>{f={x:Math.abs(Math.round(x.pageX)-(K.current?.x??0)),y:Math.abs(Math.round(x.pageY)-(K.current?.y??0))}},_=x=>{f.x<=10&&f.y<=10?x.preventDefault():v.contains(x.target)||F(!1),document.removeEventListener("pointermove",I),K.current=null};return K.current!==null&&(document.addEventListener("pointermove",I),document.addEventListener("pointerup",_,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",I),document.removeEventListener("pointerup",_,{capture:!0})}}},[v,F,K]),e.useEffect(()=>{const f=()=>F(!1);return window.addEventListener("blur",f),window.addEventListener("resize",f),()=>{window.removeEventListener("blur",f),window.removeEventListener("resize",f)}},[F]);const[pe,ae]=rt(f=>{const I=k().filter(C=>!C.disabled),_=I.find(C=>C.ref.current===document.activeElement),x=st(I,f,_);x&&setTimeout(()=>x.ref.current.focus())}),fe=e.useCallback((f,I,_)=>{const x=!H.current&&!_;(m.value!==void 0&&m.value===I||x)&&(z(f),x&&(H.current=!0))},[m.value]),me=e.useCallback(()=>v?.focus(),[v]),ee=e.useCallback((f,I,_)=>{const x=!H.current&&!_;(m.value!==void 0&&m.value===I||x)&&V(f)},[m.value]),le=l==="popper"?ge:ze,re=le===ge?{side:a,sideOffset:c,align:p,alignOffset:S,arrowPadding:w,collisionBoundary:P,collisionPadding:T,sticky:y,hideWhenDetached:i,avoidCollisions:h}:{};return u.jsx(Fe,{scope:t,content:v,viewport:A,onViewportChange:ne,itemRefCallback:fe,selectedItem:D,onItemLeave:me,itemTextRefCallback:ee,focusSelectedItem:L,selectedItemText:q,position:l,isPositioned:U,searchRef:pe,children:u.jsx(Tt,{as:Xt,allowPinchZoom:!0,children:u.jsx(Et,{asChild:!0,trapped:m.open,onMountAutoFocus:f=>{f.preventDefault()},onUnmountAutoFocus:N(s,f=>{m.trigger?.focus({preventScroll:!0}),f.preventDefault()}),children:u.jsx(Nt,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:d,onPointerDownOutside:r,onFocusOutside:f=>f.preventDefault(),onDismiss:()=>m.onOpenChange(!1),children:u.jsx(le,{role:"listbox",id:m.contentId,"data-state":m.open?"open":"closed",dir:m.dir,onContextMenu:f=>f.preventDefault(),...g,...re,onPlaced:()=>B(!0),ref:R,style:{display:"flex",flexDirection:"column",outline:"none",...g.style},onKeyDown:N(g.onKeyDown,f=>{const I=f.ctrlKey||f.altKey||f.metaKey;if(f.key==="Tab"&&f.preventDefault(),!I&&f.key.length===1&&ae(f.key),["ArrowUp","ArrowDown","Home","End"].includes(f.key)){let x=k().filter(C=>!C.disabled).map(C=>C.ref.current);if(["ArrowUp","End"].includes(f.key)&&(x=x.slice().reverse()),["ArrowUp","ArrowDown"].includes(f.key)){const C=f.target,E=x.indexOf(C);x=x.slice(E+1)}setTimeout(()=>b(x)),f.preventDefault()}})})})})})})});We.displayName=qt;var Zt="SelectItemAlignedPosition",ze=e.forwardRef((o,n)=>{const{__scopeSelect:t,onPlaced:l,...s}=o,d=$(J,t),r=Y(J,t),[a,c]=e.useState(null),[p,S]=e.useState(null),w=j(n,R=>S(R)),P=de(t),T=e.useRef(!1),y=e.useRef(!0),{viewport:i,selectedItem:h,selectedItemText:g,focusSelectedItem:m}=r,v=e.useCallback(()=>{if(d.trigger&&d.valueNode&&a&&p&&i&&h&&g){const R=d.trigger.getBoundingClientRect(),D=p.getBoundingClientRect(),z=d.valueNode.getBoundingClientRect(),q=g.getBoundingClientRect();if(d.dir!=="rtl"){const C=q.left-D.left,E=z.left-C,X=R.left-E,Z=R.width+X,he=Math.max(Z,D.width),ve=window.innerWidth-O,Se=Re(E,[O,Math.max(O,ve-he)]);a.style.minWidth=Z+"px",a.style.left=Se+"px"}else{const C=D.right-q.right,E=window.innerWidth-z.right-C,X=window.innerWidth-R.right-E,Z=R.width+X,he=Math.max(Z,D.width),ve=window.innerWidth-O,Se=Re(E,[O,Math.max(O,ve-he)]);a.style.minWidth=Z+"px",a.style.right=Se+"px"}const V=P(),k=window.innerHeight-O*2,U=i.scrollHeight,B=window.getComputedStyle(p),H=parseInt(B.borderTopWidth,10),b=parseInt(B.paddingTop,10),L=parseInt(B.borderBottomWidth,10),F=parseInt(B.paddingBottom,10),K=H+b+U+F+L,pe=Math.min(h.offsetHeight*5,K),ae=window.getComputedStyle(i),fe=parseInt(ae.paddingTop,10),me=parseInt(ae.paddingBottom,10),ee=R.top+R.height/2-O,le=k-ee,re=h.offsetHeight/2,f=h.offsetTop+re,I=H+b+f,_=K-I;if(I<=ee){const C=V.length>0&&h===V[V.length-1].ref.current;a.style.bottom="0px";const E=p.clientHeight-i.offsetTop-i.offsetHeight,X=Math.max(le,re+(C?me:0)+E+L),Z=I+X;a.style.height=Z+"px"}else{const C=V.length>0&&h===V[0].ref.current;a.style.top="0px";const X=Math.max(ee,H+i.offsetTop+(C?fe:0)+re)+_;a.style.height=X+"px",i.scrollTop=I-ee+i.offsetTop}a.style.margin=`${O}px 0`,a.style.minHeight=pe+"px",a.style.maxHeight=k+"px",l?.(),requestAnimationFrame(()=>T.current=!0)}},[P,d.trigger,d.valueNode,a,p,i,h,g,d.dir,l]);G(()=>v(),[v]);const[W,A]=e.useState();G(()=>{p&&A(window.getComputedStyle(p).zIndex)},[p]);const ne=e.useCallback(R=>{R&&y.current===!0&&(v(),m?.(),y.current=!1)},[v,m]);return u.jsx(Jt,{scope:t,contentWrapper:a,shouldExpandOnScrollRef:T,onScrollButtonChange:ne,children:u.jsx("div",{ref:c,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:W},children:u.jsx(M.div,{...s,ref:w,style:{boxSizing:"border-box",maxHeight:"100%",...s.style}})})})});ze.displayName=Zt;var Qt="SelectPopperPosition",ge=e.forwardRef((o,n)=>{const{__scopeSelect:t,align:l="start",collisionPadding:s=O,...d}=o,r=ue(t);return u.jsx(Rt,{...r,...d,ref:n,align:l,collisionPadding:s,style:{boxSizing:"border-box",...d.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});ge.displayName=Qt;var[Jt,Te]=oe(J,{}),xe="SelectViewport",Ue=e.forwardRef((o,n)=>{const{__scopeSelect:t,nonce:l,...s}=o,d=Y(xe,t),r=Te(xe,t),a=j(n,d.onViewportChange),c=e.useRef(0);return u.jsxs(u.Fragment,{children:[u.jsx("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:l}),u.jsx(ie.Slot,{scope:t,children:u.jsx(M.div,{"data-radix-select-viewport":"",role:"presentation",...s,ref:a,style:{position:"relative",flex:1,overflow:"hidden auto",...s.style},onScroll:N(s.onScroll,p=>{const S=p.currentTarget,{contentWrapper:w,shouldExpandOnScrollRef:P}=r;if(P?.current&&w){const T=Math.abs(c.current-S.scrollTop);if(T>0){const y=window.innerHeight-O*2,i=parseFloat(w.style.minHeight),h=parseFloat(w.style.height),g=Math.max(i,h);if(g<y){const m=g+T,v=Math.min(y,m),W=m-v;w.style.height=v+"px",w.style.bottom==="0px"&&(S.scrollTop=W>0?W:0,w.style.justifyContent="flex-end")}}}c.current=S.scrollTop})})})]})});Ue.displayName=xe;var Ke="SelectGroup",[eo,to]=oe(Ke),Ge=e.forwardRef((o,n)=>{const{__scopeSelect:t,...l}=o,s=Ie();return u.jsx(eo,{scope:t,id:s,children:u.jsx(M.div,{role:"group","aria-labelledby":s,...l,ref:n})})});Ge.displayName=Ke;var $e="SelectLabel",Ye=e.forwardRef((o,n)=>{const{__scopeSelect:t,...l}=o,s=to($e,t);return u.jsx(M.div,{id:s.id,...l,ref:n})});Ye.displayName=$e;var ce="SelectItem",[oo,qe]=oe(ce),Xe=e.forwardRef((o,n)=>{const{__scopeSelect:t,value:l,disabled:s=!1,textValue:d,...r}=o,a=$(ce,t),c=Y(ce,t),p=a.value===l,[S,w]=e.useState(d??""),[P,T]=e.useState(!1),y=j(n,m=>c.itemRefCallback?.(m,l,s)),i=Ie(),h=e.useRef("touch"),g=()=>{s||(a.onValueChange(l),a.onOpenChange(!1))};if(l==="")throw new Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return u.jsx(oo,{scope:t,value:l,disabled:s,textId:i,isSelected:p,onItemTextChange:e.useCallback(m=>{w(v=>v||(m?.textContent??"").trim())},[]),children:u.jsx(ie.ItemSlot,{scope:t,value:l,disabled:s,textValue:S,children:u.jsx(M.div,{role:"option","aria-labelledby":i,"data-highlighted":P?"":void 0,"aria-selected":p&&P,"data-state":p?"checked":"unchecked","aria-disabled":s||void 0,"data-disabled":s?"":void 0,tabIndex:s?void 0:-1,...r,ref:y,onFocus:N(r.onFocus,()=>T(!0)),onBlur:N(r.onBlur,()=>T(!1)),onClick:N(r.onClick,()=>{h.current!=="mouse"&&g()}),onPointerUp:N(r.onPointerUp,()=>{h.current==="mouse"&&g()}),onPointerDown:N(r.onPointerDown,m=>{h.current=m.pointerType}),onPointerMove:N(r.onPointerMove,m=>{h.current=m.pointerType,s?c.onItemLeave?.():h.current==="mouse"&&m.currentTarget.focus({preventScroll:!0})}),onPointerLeave:N(r.onPointerLeave,m=>{m.currentTarget===document.activeElement&&c.onItemLeave?.()}),onKeyDown:N(r.onKeyDown,m=>{c.searchRef?.current!==""&&m.key===" "||(Wt.includes(m.key)&&g(),m.key===" "&&m.preventDefault())})})})})});Xe.displayName=ce;var se="SelectItemText",Ze=e.forwardRef((o,n)=>{const{__scopeSelect:t,className:l,style:s,...d}=o,r=$(se,t),a=Y(se,t),c=qe(se,t),p=Gt(se,t),[S,w]=e.useState(null),P=j(n,g=>w(g),c.onItemTextChange,g=>a.itemTextRefCallback?.(g,c.value,c.disabled)),T=S?.textContent,y=e.useMemo(()=>u.jsx("option",{value:c.value,disabled:c.disabled,children:T},c.value),[c.disabled,c.value,T]),{onNativeOptionAdd:i,onNativeOptionRemove:h}=p;return G(()=>(i(y),()=>h(y)),[i,h,y]),u.jsxs(u.Fragment,{children:[u.jsx(M.span,{id:c.textId,...d,ref:P}),c.isSelected&&r.valueNode&&!r.valueNodeHasChildren?Ae.createPortal(d.children,r.valueNode):null]})});Ze.displayName=se;var Qe="SelectItemIndicator",Je=e.forwardRef((o,n)=>{const{__scopeSelect:t,...l}=o;return qe(Qe,t).isSelected?u.jsx(M.span,{"aria-hidden":!0,...l,ref:n}):null});Je.displayName=Qe;var we="SelectScrollUpButton",no=e.forwardRef((o,n)=>{const t=Y(we,o.__scopeSelect),l=Te(we,o.__scopeSelect),[s,d]=e.useState(!1),r=j(n,l.onScrollButtonChange);return G(()=>{if(t.viewport&&t.isPositioned){let a=function(){const p=c.scrollTop>0;d(p)};const c=t.viewport;return a(),c.addEventListener("scroll",a),()=>c.removeEventListener("scroll",a)}},[t.viewport,t.isPositioned]),s?u.jsx(et,{...o,ref:r,onAutoScroll:()=>{const{viewport:a,selectedItem:c}=t;a&&c&&(a.scrollTop=a.scrollTop-c.offsetHeight)}}):null});no.displayName=we;var Ce="SelectScrollDownButton",ro=e.forwardRef((o,n)=>{const t=Y(Ce,o.__scopeSelect),l=Te(Ce,o.__scopeSelect),[s,d]=e.useState(!1),r=j(n,l.onScrollButtonChange);return G(()=>{if(t.viewport&&t.isPositioned){let a=function(){const p=c.scrollHeight-c.clientHeight,S=Math.ceil(c.scrollTop)<p;d(S)};const c=t.viewport;return a(),c.addEventListener("scroll",a),()=>c.removeEventListener("scroll",a)}},[t.viewport,t.isPositioned]),s?u.jsx(et,{...o,ref:r,onAutoScroll:()=>{const{viewport:a,selectedItem:c}=t;a&&c&&(a.scrollTop=a.scrollTop+c.offsetHeight)}}):null});ro.displayName=Ce;var et=e.forwardRef((o,n)=>{const{__scopeSelect:t,onAutoScroll:l,...s}=o,d=Y("SelectScrollButton",t),r=e.useRef(null),a=de(t),c=e.useCallback(()=>{r.current!==null&&(window.clearInterval(r.current),r.current=null)},[]);return e.useEffect(()=>()=>c(),[c]),G(()=>{a().find(S=>S.ref.current===document.activeElement)?.ref.current?.scrollIntoView({block:"nearest"})},[a]),u.jsx(M.div,{"aria-hidden":!0,...s,ref:n,style:{flexShrink:0,...s.style},onPointerDown:N(s.onPointerDown,()=>{r.current===null&&(r.current=window.setInterval(l,50))}),onPointerMove:N(s.onPointerMove,()=>{d.onItemLeave?.(),r.current===null&&(r.current=window.setInterval(l,50))}),onPointerLeave:N(s.onPointerLeave,()=>{c()})})}),so="SelectSeparator",tt=e.forwardRef((o,n)=>{const{__scopeSelect:t,...l}=o;return u.jsx(M.div,{"aria-hidden":!0,...l,ref:n})});tt.displayName=so;var ye="SelectArrow",ao=e.forwardRef((o,n)=>{const{__scopeSelect:t,...l}=o,s=ue(t),d=$(ye,t),r=Y(ye,t);return d.open&&r.position==="popper"?u.jsx(bt,{...s,...l,ref:n}):null});ao.displayName=ye;var lo="SelectBubbleInput",ot=e.forwardRef(({__scopeSelect:o,value:n,...t},l)=>{const s=e.useRef(null),d=j(l,s),r=Ht(n);return e.useEffect(()=>{const a=s.current;if(!a)return;const c=window.HTMLSelectElement.prototype,S=Object.getOwnPropertyDescriptor(c,"value").set;if(r!==n&&S){const w=new Event("change",{bubbles:!0});S.call(a,n),a.dispatchEvent(w)}},[r,n]),u.jsx(M.select,{...t,style:{...kt,...t.style},ref:d,defaultValue:n})});ot.displayName=lo;function nt(o){return o===""||o===void 0}function rt(o){const n=Pt(o),t=e.useRef(""),l=e.useRef(0),s=e.useCallback(r=>{const a=t.current+r;n(a),function c(p){t.current=p,window.clearTimeout(l.current),p!==""&&(l.current=window.setTimeout(()=>c(""),1e3))}(a)},[n]),d=e.useCallback(()=>{t.current="",window.clearTimeout(l.current)},[]);return e.useEffect(()=>()=>window.clearTimeout(l.current),[]),[t,s,d]}function st(o,n,t){const s=n.length>1&&Array.from(n).every(p=>p===n[0])?n[0]:n,d=t?o.indexOf(t):-1;let r=co(o,Math.max(d,0));s.length===1&&(r=r.filter(p=>p!==t));const c=r.find(p=>p.textValue.toLowerCase().startsWith(s.toLowerCase()));return c!==t?c:void 0}function co(o,n){return o.map((t,l)=>o[(n+l)%o.length])}var io=Oe,uo=Le,po=Ve,fo=ke,mo=Be,ho=He,vo=Ue,So=Ge,go=Ye,xo=Xe,wo=Ze,Co=Je,yo=tt;const Io=["1","2","3"],Ee={size:{type:"enum",className:"rt-r-size",values:Io,default:"2",responsive:!0}},To=["classic","surface","soft","ghost"],Eo={variant:{type:"enum",className:"rt-variant",values:To,default:"surface"},...be,...Bt,placeholder:{type:"string"}},No=["solid","soft"],Po={variant:{type:"enum",className:"rt-variant",values:No,default:"solid"},...be,...mt},Ne=e.createContext({}),at=o=>{const{children:n,size:t=Ee.size.default,...l}=o;return e.createElement(io,{...l},e.createElement(Ne.Provider,{value:e.useMemo(()=>({size:t}),[t])},n))};at.displayName="Select.Root";const lt=e.forwardRef((o,n)=>{const t=e.useContext(Ne),{children:l,className:s,color:d,radius:r,placeholder:a,...c}=_e({size:t?.size,...o},{size:Ee.size},Eo,ht);return e.createElement(uo,{asChild:!0},e.createElement("button",{"data-accent-color":d,"data-radius":r,...c,ref:n,className:te("rt-reset","rt-SelectTrigger",s)},e.createElement("span",{className:"rt-SelectTriggerInner"},e.createElement(po,{placeholder:a},l)),e.createElement(fo,{asChild:!0},e.createElement(Vt,{className:"rt-SelectIcon"}))))});lt.displayName="Select.Trigger";const ct=e.forwardRef((o,n)=>{const t=e.useContext(Ne),{className:l,children:s,color:d,container:r,...a}=_e({size:t?.size,...o},{size:Ee.size},Po),c=_t(),p=d||c.accentColor;return e.createElement(mo,{container:r},e.createElement(Mt,{asChild:!0},e.createElement(ho,{"data-accent-color":p,sideOffset:4,...a,asChild:!1,ref:n,className:te({"rt-PopperContent":a.position==="popper"},"rt-SelectContent",l)},e.createElement(At,{type:"auto",className:"rt-ScrollAreaRoot"},e.createElement(vo,{asChild:!0,className:"rt-SelectViewport"},e.createElement(Ot,{className:"rt-ScrollAreaViewport",style:{overflowY:void 0}},s)),e.createElement(Dt,{className:"rt-ScrollAreaScrollbar rt-r-size-1",orientation:"vertical"},e.createElement(Lt,{className:"rt-ScrollAreaThumb"}))))))});ct.displayName="Select.Content";const it=e.forwardRef((o,n)=>{const{className:t,children:l,...s}=o;return e.createElement(xo,{...s,asChild:!1,ref:n,className:te("rt-SelectItem",t)},e.createElement(Co,{className:"rt-SelectItemIndicator"},e.createElement(jt,{className:"rt-SelectItemIndicatorIcon"})),e.createElement(wo,null,l))});it.displayName="Select.Item";const dt=e.forwardRef(({className:o,...n},t)=>e.createElement(So,{...n,asChild:!1,ref:t,className:te("rt-SelectGroup",o)}));dt.displayName="Select.Group";const ut=e.forwardRef(({className:o,...n},t)=>e.createElement(go,{...n,asChild:!1,ref:t,className:te("rt-SelectLabel",o)}));ut.displayName="Select.Label";const pt=e.forwardRef(({className:o,...n},t)=>e.createElement(yo,{...n,asChild:!1,ref:t,className:te("rt-SelectSeparator",o)}));pt.displayName="Select.Separator";const Lo=Object.freeze(Object.defineProperty({__proto__:null,Content:ct,Group:dt,Item:it,Label:ut,Root:at,Separator:pt,Trigger:lt},Symbol.toStringTag,{value:"Module"}));export{at as C,lt as a,ct as g,Lo as s,Ht as u,it as v,dt as y};
