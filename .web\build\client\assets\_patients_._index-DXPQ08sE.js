import{w as E,a as e,v as k}from"./chunk-QMGIS6GS-suYYFPSk.js";import{f as c,E as d,c as i,h as P,i as u}from"./state-B1hYtTsq.js";import{B,F as A,a as j,b as D,c as W,d as R,e as $,f as I,k as M}from"./stateful_components-_envzdXf.js";import{D as T}from"./index-CKoiYi4C.js";import{j as t}from"./emotion-react.browser.esm-BNSIgtcs.js";import{U as L}from"./user-plus-BRYdVFIJ.js";import{ah as S,a3 as f,ae as b,W as y,ab as O}from"./createLucideIcon-BOfs0RvG.js";import{c as _,r as m,o}from"./button-Ccwu8jNm.js";import{p as w}from"./container-Do-LxYxS.js";import{p as s}from"./text-DCkbNTq3.js";import{o as h}from"./card-BFhh40Od.js";import{u as x}from"./text-field-UsP3CXyu.js";import{C,a as v,g as F,y as N,v as g}from"./select-hLIrycZp.js";import"./jsx-runtime-D_zvdyIk.js";import"./index-XOwJfM4g.js";/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const q=[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]],H=S("chevron-left",q);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const U=[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]],V=S("chevron-right",U);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const G=[["path",{d:"m13.5 8.5-5 5",key:"1cs55j"}],["path",{d:"m8.5 8.5 5 5",key:"a8mexj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]],X=S("search-x",G);function J(){const a=e.useContext(c.reflex___state____state__states___patient_state____patient_state);return t(e.Fragment,{},a.current_page_rx_state_<a.total_pages_rx_state_?t(e.Fragment,{},t(et,{})):t(e.Fragment,{}))}function K(){const a=e.useContext(c.reflex___state____state__states___auth_state____auth_state);return t(e.Fragment,{},a.is_authenticated_rx_state_?t(e.Fragment,{},t(_,{align:"start",className:"rx-Stack",css:{width:"100%",minHeight:"100vh"},direction:"column",gap:"0"},t(f,{css:{width:"100%",background:"white",borderBottom:"1px solid",borderColor:"gray.200",p:4,position:"sticky",top:"0",zIndex:"1000"}},t(w,{css:{padding:"16px",maxWidth:"1200px"},size:"3"},t(_,{align:"center",className:"rx-Stack",css:{width:"100%"},direction:"row",gap:"3"},t(_,{align:"start",className:"rx-Stack",direction:"row",gap:"3"},t(B,{css:{color:"blue.600"},size:32}),t(m,{css:{color:"blue.600"},size:"6"},"Psychiatry EMR")),t(A,{}),t(_,{css:{flex:1,justifySelf:"stretch",alignSelf:"stretch"}}),t(j,{})))),t(_,{align:"start",className:"rx-Stack",direction:"column",gap:"2"},t(D,{}),t(W,{}),t(R,{}),t($,{})),t(f,{css:{width:"100%",flex:"1"}},t(_,{align:"start",className:"rx-Stack",css:{width:"100%",minHeight:"100vh",background:"gray.50"},direction:"column",gap:"3"},t(f,{css:{width:"100%",background:"white",borderBottom:"1px solid",borderColor:"gray.200",p:4}},t(w,{css:{padding:"16px",maxWidth:"1200px"},size:"3"},t(_,{align:"center",className:"rx-Stack",css:{width:"100%"},direction:"row",gap:"3"},t(_,{align:"start",className:"rx-Stack",direction:"row",gap:"2"},t(b,{asChild:!0,css:{color:"blue.600","&:hover":{color:"var(--accent-8)"}}},t(k,{to:"/dashboard"},"Dashboard")),t(s,{as:"p",css:{color:"gray.400"}},"/"),t(s,{as:"p",css:{fontWeight:"bold"}},"Patient Search")),t(_,{css:{flex:1,justifySelf:"stretch",alignSelf:"stretch"}}),t(o,{color:"blue",size:"3"},t(_,{align:"start",className:"rx-Stack",direction:"row",gap:"2"},t(L,{size:20}),t(s,{as:"p"},"New Patient")))))),t(w,{css:{padding:"16px",maxWidth:"1200px",p:6},size:"3"},t(_,{align:"start",className:"rx-Stack",css:{width:"100%"},direction:"column",gap:"6"},t(h,{css:{p:6,width:"100%"}},t(_,{align:"start",className:"rx-Stack",css:{width:"100%"},direction:"column",gap:"4"},t(m,{css:{mb:4},size:"6"},"Patient Search"),t(_,{align:"start",className:"rx-Stack",css:{width:"100%"},direction:"row",gap:"3"},t(st,{}),t(rt,{})),t(_,{align:"start",className:"rx-Stack",css:{width:"100%"},direction:"column",gap:"3"},t(Z,{}),t(tt,{})))),t(_,{align:"start",className:"rx-Stack",css:{width:"100%"},direction:"column",gap:"4"},t(it,{}),t(gt,{})))))),t(f,{css:{width:"100%",background:"gray.100",borderTop:"1px solid",borderColor:"gray.200",p:4,mt:"auto"}},t(w,{css:{padding:"16px",maxWidth:"1200px"},size:"3"},t(_,{align:"center",className:"rx-Stack",css:{width:"100%"},direction:"row",gap:"3"},t(s,{as:"p",css:{color:"gray.600",fontSize:"sm"}},"© 2024 Psychiatry EMR. All rights reserved."),t(_,{css:{flex:1,justifySelf:"stretch",alignSelf:"stretch"}}),t(_,{align:"start",className:"rx-Stack",direction:"row",gap:"2"},t(s,{as:"p",css:{color:"gray.500",fontSize:"xs"}},"Version 1.0.0"),t(s,{as:"p",css:{color:"gray.400"}},"•"),t(b,{asChild:!0,css:{color:"gray.500",fontSize:"xs","&:hover":{color:"var(--accent-8)"}}},t(k,{to:"/privacy"},"Privacy Policy")),t(s,{as:"p",css:{color:"gray.400"}},"•"),t(b,{asChild:!0,css:{color:"gray.500",fontSize:"xs","&:hover":{color:"var(--accent-8)"}}},t(k,{to:"/support"},"Support")))))))):t(e.Fragment,{},t(_,{align:"center",className:"rx-Stack",css:{p:8},direction:"column",gap:"4"},t(m,{size:"6"},"Authentication Required"),t(s,{as:"p"},"Please log in to access this page."),t(I,{}))))}function Q(){const a=e.useContext(c.reflex___state____state__states___patient_state____patient_state),[n,p]=e.useContext(d);return t(y,{columns:"1",css:{width:"100%"},gap:"4"},a.search_results_rx_state_.map((r,l)=>t(h,{css:{p:4,width:"100%","&:hover":{shadow:"md"}},key:l},t(_,{align:"start",className:"rx-Stack",css:{width:"100%"},direction:"row",gap:"4"},t(_,{align:"start",className:"rx-Stack",css:{flex:"1"},direction:"column",gap:"2"},t(_,{align:"start",className:"rx-Stack",direction:"row",gap:"3"},t(m,{size:"5"},r.name),t(O,{color:"blue",size:"1"},"ID: "+r.id)),t(y,{columns:"2",css:{width:"100%"},gap:"2"},t(e.Fragment,{},u(r.dob)?t(e.Fragment,{},t(s,{as:"p",css:{color:"gray.600"}},"DOB: "+r.dob)):t(e.Fragment,{},t(s,{as:"p",css:{color:"gray.600"}},"DOB: Not provided"))),t(e.Fragment,{},u(r.phone)?t(e.Fragment,{},t(s,{as:"p",css:{color:"gray.600"}},"Phone: "+r.phone)):t(e.Fragment,{},t(s,{as:"p",css:{color:"gray.600"}},"Phone: Not provided"))),t(e.Fragment,{},u(r.email)?t(e.Fragment,{},t(s,{as:"p",css:{color:"gray.600"}},"Email: "+r.email)):t(e.Fragment,{},t(s,{as:"p",css:{color:"gray.600"}},"Email: Not provided"))),t(e.Fragment,{},u(r.created_at)?t(e.Fragment,{},t(s,{as:"p",css:{color:"gray.600"}},"Created: "+r.created_at)):t(e.Fragment,{},t(s,{as:"p",css:{color:"gray.600"}},"Created: Not provided"))))),t(_,{align:"start",className:"rx-Stack",css:{minWidth:"150px"},direction:"column",gap:"2"},t(o,{color:"blue",css:{width:"100%"},onClick:z=>n([i("reflex___state____state.states___patient_state____patient_state.load_patient",{patient_id:r.id},{})],[z],{}),size:"2"},"View Details"),t(o,{color:"green",css:{width:"100%"},size:"2"},"New Assessment"),t(o,{color:"gray",css:{width:"100%"},size:"2"},"Edit Patient"))))))}function Y(){const a=e.useContext(c.reflex___state____state__states___patient_state____patient_state);return t(s,{as:"p",css:{color:"gray.600"}},"Page "+a.current_page_rx_state_+" of "+a.total_pages_rx_state_)}function Z(){const[a,n]=e.useContext(d),p=e.useCallback(r=>a([i("reflex___state____state.states___patient_state____patient_state.toggle_advanced_filters",{},{})],[r],{}),[a,i]);return t(o,{css:{width:"100%"},onClick:p,size:"2",variant:"ghost"},t(_,{align:"start",className:"rx-Stack",direction:"row",gap:"2"},t(s,{as:"p"},"Advanced Filters"),t(M,{size:16})))}function tt(){const a=e.useContext(c.reflex___state____state__states___patient_state____patient_state);return t(e.Fragment,{},a.show_advanced_filters_rx_state_?t(e.Fragment,{},t(_,{align:"start",className:"rx-Stack",css:{width:"100%",p:4,border:"1px solid",borderColor:"gray.200",borderRadius:"md",background:"gray.50"},direction:"column",gap:"4"},t(y,{columns:"4",css:{width:"100%"},gap:"4"},t(_,{align:"start",className:"rx-Stack",direction:"column",gap:"2"},t(s,{as:"p",css:{fontWeight:"bold"}},"Age Range"),t(_,{align:"start",className:"rx-Stack",css:{width:"100%"},direction:"row",gap:"2"},t(x,{css:{width:"100%"},placeholder:"Min age",type:"number"}),t(s,{as:"p"},"to"),t(x,{css:{width:"100%"},placeholder:"Max age",type:"number"}))),t(_,{align:"start",className:"rx-Stack",direction:"column",gap:"2"},t(s,{as:"p",css:{fontWeight:"bold"}},"Gender"),t(C,{},t(v,{css:{width:"100%"},placeholder:"Select gender"}),t(F,{},t(N,{},"",t(g,{value:"Any"},"Any"),t(g,{value:"Male"},"Male"),t(g,{value:"Female"},"Female"),t(g,{value:"Non-binary"},"Non-binary"),t(g,{value:"Other"},"Other"))))),t(_,{align:"start",className:"rx-Stack",direction:"column",gap:"2"},t(s,{as:"p",css:{fontWeight:"bold"}},"Registration Date"),t(_,{align:"start",className:"rx-Stack",css:{width:"100%"},direction:"row",gap:"2"},t(x,{css:{width:"100%"},type:"date"}),t(s,{as:"p"},"to"),t(x,{css:{width:"100%"},type:"date"}))),t(_,{align:"start",className:"rx-Stack",direction:"column",gap:"2"},t(s,{as:"p",css:{fontWeight:"bold"}},"Status"),t(C,{},t(v,{css:{width:"100%"},placeholder:"Select status"}),t(F,{},t(N,{},"",t(g,{value:"All"},"All"),t(g,{value:"Active"},"Active"),t(g,{value:"Inactive"},"Inactive")))))),t(_,{align:"start",className:"rx-Stack",direction:"row",gap:"2"},t(o,{color:"blue",size:"2"},"Apply Filters"),t(o,{size:"2",variant:"outline"},"Clear Filters")))):t(e.Fragment,{}))}function et(){const a=e.useContext(c.reflex___state____state__states___patient_state____patient_state),[n,p]=e.useContext(d),r=e.useCallback(l=>n([i("reflex___state____state.states___patient_state____patient_state.go_to_page",{page:a.total_pages_rx_state_},{})],[l],{}),[n,i,a]);return t(o,{onClick:r,size:"2",variant:"outline"},a.total_pages_rx_state_)}function at(){const a=e.useContext(c.reflex___state____state__states___patient_state____patient_state);return t(e.Fragment,{},a.current_page_rx_state_>3?t(e.Fragment,{},t(s,{as:"p",css:{color:"gray.400"}},"...")):t(e.Fragment,{}))}function _t(){const[a,n]=e.useContext(d),p=e.useCallback(r=>a([i("reflex___state____state.states___patient_state____patient_state.go_to_page",{page:1},{})],[r],{}),[a,i]);return t(o,{onClick:p,size:"2",variant:"outline"},"1")}function st(){const a=e.useContext(c.reflex___state____state__states___patient_state____patient_state),[n,p]=e.useContext(d),r=e.useCallback(l=>n([i("reflex___state____state.states___patient_state____patient_state.set_search_term",{value:l.target.value},{})],[l],{}),[n,i]);return t(T,{css:{width:"100%"},debounceTimeout:300,element:x,onChange:r,placeholder:"Search by name, phone, email, or patient ID...",size:"3",value:P(a.search_term_rx_state_)?a.search_term_rx_state_:""})}function rt(){const a=e.useContext(c.reflex___state____state__states___patient_state____patient_state),[n,p]=e.useContext(d),r=e.useCallback(l=>n([i("reflex___state____state.states___patient_state____patient_state.search_patients",{},{})],[l],{}),[n,i]);return t(o,{color:"blue",loading:a.is_loading_rx_state_,onClick:r,size:"3"},"Search")}function nt(){const a=e.useContext(c.reflex___state____state__states___patient_state____patient_state);return t(e.Fragment,{},a.current_page_rx_state_>1?t(e.Fragment,{},t(_t,{})):t(e.Fragment,{}))}function ct(){const a=e.useContext(c.reflex___state____state__states___patient_state____patient_state);return t(s,{as:"p",css:{color:"gray.600"}},"Showing page "+a.current_page_rx_state_+" of "+a.total_pages_rx_state_)}function ot(){const a=e.useContext(c.reflex___state____state__states___patient_state____patient_state);return t(o,{color:"blue",size:"2"},a.current_page_rx_state_)}function it(){const a=e.useContext(c.reflex___state____state__states___patient_state____patient_state);return t(e.Fragment,{},u(a.search_results_rx_state_)?t(e.Fragment,{},t(h,{css:{p:4,width:"100%"}},t(_,{align:"center",className:"rx-Stack",css:{width:"100%"},direction:"row",gap:"3"},t(_,{align:"start",className:"rx-Stack",direction:"column",gap:"1"},t(pt,{}),t(ct,{})),t(_,{css:{flex:1,justifySelf:"stretch",alignSelf:"stretch"}}),t(_,{align:"center",className:"rx-Stack",direction:"row",gap:"2"},t(s,{as:"p",css:{fontSize:"sm"}},"View:"),t(_,{align:"start",className:"rx-Stack",direction:"row",gap:"1"},t(o,{size:"2",variant:"outline"},"List"),t(o,{size:"2",variant:"solid"},"Cards")))))):t(e.Fragment,{}))}function lt(){const a=e.useContext(c.reflex___state____state__states___patient_state____patient_state),[n,p]=e.useContext(d),r=e.useCallback(l=>n([i("reflex___state____state.states___patient_state____patient_state.next_page",{},{})],[l],{}),[n,i]);return t(o,{disabled:a.current_page_rx_state_===a.total_pages_rx_state_,onClick:r,size:"2",variant:"outline"},t(V,{size:16}))}function pt(){const a=e.useContext(c.reflex___state____state__states___patient_state____patient_state);return t(m,{size:"5"},"Search Results ("+a.total_results_rx_state_+")")}function gt(){const a=e.useContext(c.reflex___state____state__states___patient_state____patient_state);return t(e.Fragment,{},u(a.search_results_rx_state_)?t(e.Fragment,{},t(_,{align:"start",className:"rx-Stack",css:{width:"100%"},direction:"column",gap:"4"},t(Q,{}),t(h,{css:{p:3,width:"100%"}},t(_,{align:"center",className:"rx-Stack",css:{width:"100%"},direction:"row",gap:"3"},t(Y,{}),t(_,{css:{flex:1,justifySelf:"stretch",alignSelf:"stretch"}}),t(_,{align:"start",className:"rx-Stack",direction:"row",gap:"2"},t(ut,{}),t(_,{align:"start",className:"rx-Stack",direction:"row",gap:"1"},t(nt,{}),t(at,{}),t(ot,{}),t(dt,{}),t(J,{})),t(lt,{})))))):t(e.Fragment,{},t(h,{css:{p:8,width:"100%",textAlign:"center"}},t(_,{align:"center",className:"rx-Stack",direction:"column",gap:"3"},t(X,{css:{color:"gray.400"},size:48}),t(m,{css:{color:"gray.600"},size:"5"},"No patients found"),t(s,{as:"p",css:{color:"gray.500"}},"Try adjusting your search terms or filters"),t(o,{color:"blue",css:{mt:4},size:"3"},"Create New Patient")))))}function dt(){const a=e.useContext(c.reflex___state____state__states___patient_state____patient_state);return t(e.Fragment,{},a.current_page_rx_state_<a.total_pages_rx_state_-2?t(e.Fragment,{},t(s,{as:"p",css:{color:"gray.400"}},"...")):t(e.Fragment,{}))}function ut(){const a=e.useContext(c.reflex___state____state__states___patient_state____patient_state),[n,p]=e.useContext(d),r=e.useCallback(l=>n([i("reflex___state____state.states___patient_state____patient_state.prev_page",{},{})],[l],{}),[n,i]);return t(o,{disabled:a.current_page_rx_state_===1,onClick:r,size:"2",variant:"outline"},t(H,{size:16}))}const Pt=E(function(){return t(e.Fragment,{},t(K,{}),t("title",{},"Patient Search - Psychiatry EMR"),t("meta",{content:"favicon.ico",property:"og:image"}))});export{Pt as default};
