import{a as e}from"./chunk-QMGIS6GS-suYYFPSk.js";import{e as u,v as p,r as R,y as m,b as S}from"./text-DCkbNTq3.js";import{P as f,d as T,a as P,b as c,l as $}from"./button-Ccwu8jNm.js";var v=f;v.dispatchDiscreteCustomEvent=T;v.Root=f;const b=["1","2","3"],E=["classic","surface","soft"],_={size:{type:"enum",className:"rt-r-size",values:b,default:"2",responsive:!0},variant:{type:"enum",className:"rt-variant",values:E,default:"surface"},...u,...P},w=["left","right"],z={side:{type:"enum",values:w},...u,gap:$.gap,px:c.px,pl:c.pl,pr:c.pr},x=e.forwardRef((s,r)=>{const t=e.useRef(null),{children:o,className:l,color:i,radius:g,style:F,...h}=p(s,_,R);return e.createElement("div",{"data-accent-color":i,"data-radius":g,style:F,className:m("rt-TextFieldRoot",l),onPointerDown:N=>{const n=N.target;if(n.closest("input, button, a"))return;const a=t.current;if(!a)return;const d=n.closest(`
            .rt-TextFieldSlot[data-side='right'],
            .rt-TextFieldSlot:not([data-side='right']) ~ .rt-TextFieldSlot:not([data-side='left'])
          `)?a.value.length:0;requestAnimationFrame(()=>{try{a.setSelectionRange(d,d)}catch{}a.focus()})}},e.createElement("input",{spellCheck:"false",...h,ref:S(t,r),className:"rt-reset rt-TextFieldInput"}),o)});x.displayName="TextField.Root";const y=e.forwardRef((s,r)=>{const{className:t,color:o,side:l,...i}=p(s,z);return e.createElement("div",{"data-accent-color":o,"data-side":l,...i,ref:r,className:m("rt-TextFieldSlot",t)})});y.displayName="TextField.Slot";const O=Object.freeze(Object.defineProperty({__proto__:null,Root:x,Slot:y},Symbol.toStringTag,{value:"Module"}));export{v as P,O as t,x as u};
