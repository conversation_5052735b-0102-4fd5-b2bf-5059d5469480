import{a as e}from"./chunk-QMGIS6GS-suYYFPSk.js";import{c as R,d as $,e as C,f as L,g as z,i as B,n as H,t as D,o as N,v,r as f,S as h,y as m,h as V,s as k}from"./text-DCkbNTq3.js";import{a as O}from"./index-XOwJfM4g.js";import{j as E}from"./jsx-runtime-D_zvdyIk.js";var Y=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],A=Y.reduce((s,t)=>{const r=R(`Primitive.${t}`),a=e.forwardRef((n,i)=>{const{asChild:o,...p}=n,c=o?r:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),E.jsx(c,{...p,ref:i})});return a.displayName=`Primitive.${t}`,{...s,[t]:a}},{});function be(s,t){s&&O.flushSync(()=>s.dispatchEvent(t))}var F=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),W="VisuallyHidden",P=e.forwardRef((s,t)=>E.jsx(A.span,{...s,ref:t,style:{...F,...s.style}}));P.displayName=W;var I=P;const X={width:{type:"string",className:"rt-r-w",customProperties:["--width"],responsive:!0},minWidth:{type:"string",className:"rt-r-min-w",customProperties:["--min-width"],responsive:!0},maxWidth:{type:"string",className:"rt-r-max-w",customProperties:["--max-width"],responsive:!0}},_={height:{type:"string",className:"rt-r-h",customProperties:["--height"],responsive:!0},minHeight:{type:"string",className:"rt-r-min-h",customProperties:["--min-height"],responsive:!0},maxHeight:{type:"string",className:"rt-r-max-h",customProperties:["--max-height"],responsive:!0}},G=["h1","h2","h3","h4","h5","h6"],M=["1","2","3","4","5","6","7","8","9"],T={as:{type:"enum",values:G,default:"h1"},...N,size:{type:"enum",className:"rt-r-size",values:M,default:"6",responsive:!0},...D,...H,...B,...z,...L,...C,...$},U=e.forwardRef((s,t)=>{const{children:r,className:a,asChild:n,as:i="h1",color:o,...p}=v(s,T,f);return e.createElement(h,{"data-accent-color":o,...p,ref:t,className:m("rt-Heading",a)},n?r:e.createElement(i,null,r))});U.displayName="Heading";const q=["none","small","medium","large","full"],J={radius:{type:"enum",values:q,default:void 0}},K=h,xe=V,l=["0","1","2","3","4","5","6","7","8","9"],Q={p:{type:"enum | string",className:"rt-r-p",customProperties:["--p"],values:l,responsive:!0},px:{type:"enum | string",className:"rt-r-px",customProperties:["--pl","--pr"],values:l,responsive:!0},py:{type:"enum | string",className:"rt-r-py",customProperties:["--pt","--pb"],values:l,responsive:!0},pt:{type:"enum | string",className:"rt-r-pt",customProperties:["--pt"],values:l,responsive:!0},pr:{type:"enum | string",className:"rt-r-pr",customProperties:["--pr"],values:l,responsive:!0},pb:{type:"enum | string",className:"rt-r-pb",customProperties:["--pb"],values:l,responsive:!0},pl:{type:"enum | string",className:"rt-r-pl",customProperties:["--pl"],values:l,responsive:!0}},g=["visible","hidden","clip","scroll","auto"],Z=["static","relative","absolute","fixed","sticky"],u=["0","1","2","3","4","5","6","7","8","9","-1","-2","-3","-4","-5","-6","-7","-8","-9"],ee=["0","1"],se=["0","1"],te={...Q,...X,..._,position:{type:"enum",className:"rt-r-position",values:Z,responsive:!0},inset:{type:"enum | string",className:"rt-r-inset",customProperties:["--inset"],values:u,responsive:!0},top:{type:"enum | string",className:"rt-r-top",customProperties:["--top"],values:u,responsive:!0},right:{type:"enum | string",className:"rt-r-right",customProperties:["--right"],values:u,responsive:!0},bottom:{type:"enum | string",className:"rt-r-bottom",customProperties:["--bottom"],values:u,responsive:!0},left:{type:"enum | string",className:"rt-r-left",customProperties:["--left"],values:u,responsive:!0},overflow:{type:"enum",className:"rt-r-overflow",values:g,responsive:!0},overflowX:{type:"enum",className:"rt-r-ox",values:g,responsive:!0},overflowY:{type:"enum",className:"rt-r-oy",values:g,responsive:!0},flexBasis:{type:"string",className:"rt-r-fb",customProperties:["--flex-basis"],responsive:!0},flexShrink:{type:"enum | string",className:"rt-r-fs",customProperties:["--flex-shrink"],values:ee,responsive:!0},flexGrow:{type:"enum | string",className:"rt-r-fg",customProperties:["--flex-grow"],values:se,responsive:!0},gridArea:{type:"string",className:"rt-r-ga",customProperties:["--grid-area"],responsive:!0},gridColumn:{type:"string",className:"rt-r-gc",customProperties:["--grid-column"],responsive:!0},gridColumnStart:{type:"string",className:"rt-r-gcs",customProperties:["--grid-column-start"],responsive:!0},gridColumnEnd:{type:"string",className:"rt-r-gce",customProperties:["--grid-column-end"],responsive:!0},gridRow:{type:"string",className:"rt-r-gr",customProperties:["--grid-row"],responsive:!0},gridRowStart:{type:"string",className:"rt-r-grs",customProperties:["--grid-row-start"],responsive:!0},gridRowEnd:{type:"string",className:"rt-r-gre",customProperties:["--grid-row-end"],responsive:!0}},re=["1","2","3","4"],ae=["classic","solid","soft","surface","outline","ghost"],w={...N,size:{type:"enum",className:"rt-r-size",values:re,default:"2",responsive:!0},variant:{type:"enum",className:"rt-variant",values:ae,default:"solid"},...k,...$,...J,loading:{type:"boolean",className:"rt-loading",default:!1}},y=["0","1","2","3","4","5","6","7","8","9"],ne={gap:{type:"enum | string",className:"rt-r-gap",customProperties:["--gap"],values:y,responsive:!0},gapX:{type:"enum | string",className:"rt-r-cg",customProperties:["--column-gap"],values:y,responsive:!0},gapY:{type:"enum | string",className:"rt-r-rg",customProperties:["--row-gap"],values:y,responsive:!0}},ie=["div","span"],oe=["none","inline-flex","flex"],le=["row","column","row-reverse","column-reverse"],pe=["start","center","end","baseline","stretch"],ue=["start","center","end","between"],me=["nowrap","wrap","wrap-reverse"],ce={as:{type:"enum",values:ie,default:"div"},...N,display:{type:"enum",className:"rt-r-display",values:oe,responsive:!0},direction:{type:"enum",className:"rt-r-fd",values:le,responsive:!0},align:{type:"enum",className:"rt-r-ai",values:pe,responsive:!0},justify:{type:"enum",className:"rt-r-jc",values:ue,parseValue:de,responsive:!0},wrap:{type:"enum",className:"rt-r-fw",values:me,responsive:!0},...ne};function de(s){return s==="between"?"space-between":s}const d=e.forwardRef((s,t)=>{const{className:r,asChild:a,as:n="div",...i}=v(s,ce,te,f);return e.createElement(a?K:n,{...i,ref:t,className:m("rt-Flex",r)})});d.displayName="Flex";const ve=["1","2","3"],fe={size:{type:"enum",className:"rt-r-size",values:ve,default:"2",responsive:!0},loading:{type:"boolean",default:!0}},b=e.forwardRef((s,t)=>{const{className:r,children:a,loading:n,...i}=v(s,fe,f);if(!n)return a;const o=e.createElement("span",{...i,ref:t,className:m("rt-Spinner",r)},e.createElement("span",{className:"rt-SpinnerLeaf"}),e.createElement("span",{className:"rt-SpinnerLeaf"}),e.createElement("span",{className:"rt-SpinnerLeaf"}),e.createElement("span",{className:"rt-SpinnerLeaf"}),e.createElement("span",{className:"rt-SpinnerLeaf"}),e.createElement("span",{className:"rt-SpinnerLeaf"}),e.createElement("span",{className:"rt-SpinnerLeaf"}),e.createElement("span",{className:"rt-SpinnerLeaf"}));return a===void 0?o:e.createElement(d,{asChild:!0,position:"relative",align:"center",justify:"center"},e.createElement("span",null,e.createElement("span",{"aria-hidden":!0,style:{display:"contents",visibility:"hidden"},inert:void 0},a),e.createElement(d,{asChild:!0,align:"center",justify:"center",position:"absolute",inset:"0"},e.createElement("span",null,o))))});b.displayName="Spinner";const ge=I;function ye(s,t){if(s!==void 0)return typeof s=="string"?t(s):Object.fromEntries(Object.entries(s).map(([r,a])=>[r,t(a)]))}function Se(s){return s==="3"?"3":"2"}function Ne(s){switch(s){case"1":return"1";case"2":case"3":return"2";case"4":return"3"}}const x=e.forwardRef((s,t)=>{const{size:r=w.size.default}=s,{className:a,children:n,asChild:i,color:o,radius:p,disabled:c=s.loading,...S}=v(s,w,f),j=i?h:"button";return e.createElement(j,{"data-disabled":c||void 0,"data-accent-color":o,"data-radius":p,...S,ref:t,className:m("rt-reset","rt-BaseButton",a),disabled:c},s.loading?e.createElement(e.Fragment,null,e.createElement("span",{style:{display:"contents",visibility:"hidden"},"aria-hidden":!0},n),e.createElement(ge,null,n),e.createElement(d,{asChild:!0,align:"center",justify:"center",position:"absolute",inset:"0"},e.createElement("span",null,e.createElement(b,{size:ye(r,Ne)})))):n)});x.displayName="BaseButton";const he=e.forwardRef(({className:s,...t},r)=>e.createElement(x,{...t,ref:r,className:m("rt-Button",s)}));he.displayName="Button";export{A as P,I as R,F as V,J as a,Q as b,d as c,be as d,_ as e,K as f,xe as g,b as h,ge as i,q as j,ne as k,ce as l,x as n,he as o,Se as p,U as r,ye as s,X as t,te as u};
