2025-07-28 16:28:24,456 - main - INFO - initialize_application:132 - Initializing Psychiatry EMR v1.0.0
2025-07-28 16:28:24,472 - main - INFO - check_environment:119 - Environment configuration validated
2025-07-28 16:29:29,408 - security.encryption - INFO - initialize_encryption:83 - Encryption service initialized
2025-07-28 16:29:31,003 - services.database - INFO - get_engine:31 - Database engine initialized
2025-07-28 16:29:31,158 - main - ERROR - initialize_database:67 - Database initialization failed: email-validator is not installed, run `pip install pydantic[email]`
2025-07-28 16:29:31,160 - main - ERROR - initialize_application:162 - Application initialization failed: email-validator is not installed, run `pip install pydantic[email]`
2025-07-28 16:31:11,378 - main - INFO - initialize_application:132 - Initializing Psychiatry EMR v1.0.0
2025-07-28 16:31:11,383 - main - INFO - check_environment:119 - Environment configuration validated
2025-07-28 16:32:09,621 - security.encryption - INFO - initialize_encryption:83 - Encryption service initialized
2025-07-28 16:32:09,774 - services.database - INFO - get_engine:31 - Database engine initialized
2025-07-28 16:32:13,721 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-28 16:32:13,736 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-28 16:32:13,737 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-28 16:32:13,748 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA temp.table_info("patient")
2025-07-28 16:32:13,756 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-28 16:32:13,762 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-28 16:32:13,772 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-28 16:32:13,782 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA temp.table_info("userpatientaccess")
2025-07-28 16:32:13,786 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-28 16:32:13,789 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-28 16:32:13,798 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-28 16:32:13,801 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA temp.table_info("presentillness")
2025-07-28 16:32:13,804 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-28 16:32:13,806 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-28 16:32:13,817 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-28 16:32:13,820 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA temp.table_info("auditlog")
2025-07-28 16:32:13,821 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-28 16:32:13,822 - sqlalchemy.engine.Engine - INFO - _connection_rollback_impl:2701 - ROLLBACK
2025-07-28 16:32:13,823 - main - ERROR - initialize_database:67 - Database initialization failed: Foreign key associated with column 'patient.updated_by' could not find table 'user' with which to generate a foreign key to target column 'id'
2025-07-28 16:32:13,831 - main - ERROR - initialize_application:162 - Application initialization failed: Foreign key associated with column 'patient.updated_by' could not find table 'user' with which to generate a foreign key to target column 'id'
2025-07-28 16:34:12,253 - main - INFO - initialize_application:132 - Initializing Psychiatry EMR v1.0.0
2025-07-28 16:34:12,267 - main - INFO - check_environment:119 - Environment configuration validated
2025-07-28 16:35:22,550 - security.encryption - INFO - initialize_encryption:83 - Encryption service initialized
2025-07-28 16:35:22,627 - services.database - INFO - get_engine:31 - Database engine initialized
2025-07-28 16:35:23,319 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-28 16:35:23,320 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-28 16:35:23,321 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-28 16:35:23,328 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA temp.table_info("patient")
2025-07-28 16:35:23,332 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-28 16:35:23,334 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-28 16:35:23,385 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-28 16:35:23,401 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA temp.table_info("userpatientaccess")
2025-07-28 16:35:23,402 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-28 16:35:23,403 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-28 16:35:23,404 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-28 16:35:23,405 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA temp.table_info("presentillness")
2025-07-28 16:35:23,433 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-28 16:35:23,453 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-28 16:35:23,464 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-28 16:35:23,466 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA temp.table_info("auditlog")
2025-07-28 16:35:23,470 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-28 16:35:23,485 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - 
CREATE TABLE patient (
	id INTEGER NOT NULL, 
	name_encrypted VARCHAR NOT NULL, 
	dob DATE NOT NULL, 
	address_encrypted VARCHAR, 
	phone_encrypted VARCHAR, 
	email_encrypted VARCHAR, 
	education VARCHAR, 
	occupation VARCHAR, 
	living_situation VARCHAR, 
	created_at DATETIME NOT NULL, 
	updated_by INTEGER, 
	is_active BOOLEAN NOT NULL, 
	merged_into INTEGER, 
	PRIMARY KEY (id), 
	CONSTRAINT unique_patient_name_dob UNIQUE (name_encrypted, dob), 
	FOREIGN KEY(merged_into) REFERENCES patient (id)
)


2025-07-28 16:35:23,550 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [no key 0.06527s] ()
2025-07-28 16:35:23,694 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - CREATE INDEX ix_patient_created_at ON patient (created_at)
2025-07-28 16:35:23,697 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [no key 0.00311s] ()
2025-07-28 16:35:23,788 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - CREATE INDEX ix_patient_dob ON patient (dob)
2025-07-28 16:35:23,794 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [no key 0.00600s] ()
2025-07-28 16:35:23,916 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - 
CREATE TABLE auditlog (
	id INTEGER NOT NULL, 
	user_id INTEGER NOT NULL, 
	timestamp DATETIME NOT NULL, 
	action VARCHAR NOT NULL, 
	table_name VARCHAR NOT NULL, 
	record_id INTEGER, 
	old_values VARCHAR, 
	new_values VARCHAR, 
	ip_address VARCHAR, 
	user_agent VARCHAR, 
	session_id VARCHAR, 
	success BOOLEAN NOT NULL, 
	error_message VARCHAR, 
	PRIMARY KEY (id)
)


2025-07-28 16:35:23,936 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [no key 0.02083s] ()
2025-07-28 16:35:24,046 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - 
CREATE TABLE userpatientaccess (
	id INTEGER NOT NULL, 
	user_id INTEGER NOT NULL, 
	patient_id INTEGER NOT NULL, 
	granted_at DATETIME NOT NULL, 
	granted_by INTEGER NOT NULL, 
	is_active BOOLEAN NOT NULL, 
	PRIMARY KEY (id), 
	FOREIGN KEY(patient_id) REFERENCES patient (id)
)


2025-07-28 16:35:24,050 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [no key 0.00466s] ()
2025-07-28 16:35:24,154 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - 
CREATE TABLE presentillness (
	id INTEGER NOT NULL, 
	patient_id INTEGER NOT NULL, 
	assessment_date DATE NOT NULL, 
	chief_complaint VARCHAR NOT NULL, 
	history_present_illness VARCHAR NOT NULL, 
	primary_diagnosis VARCHAR, 
	secondary_diagnoses VARCHAR, 
	dsm5_criteria_json VARCHAR, 
	treatment_plan VARCHAR, 
	created_at DATETIME NOT NULL, 
	updated_by INTEGER NOT NULL, 
	PRIMARY KEY (id), 
	FOREIGN KEY(patient_id) REFERENCES patient (id)
)


2025-07-28 16:35:24,203 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [no key 0.04871s] ()
2025-07-28 16:35:24,321 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - CREATE INDEX ix_presentillness_assessment_date ON presentillness (assessment_date)
2025-07-28 16:35:24,332 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [no key 0.01143s] ()
2025-07-28 16:35:24,447 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-28 16:35:24,451 - main - INFO - initialize_database:57 - Database tables created/verified
2025-07-28 16:35:24,465 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-28 16:35:24,471 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - 
-- Performance indexes for common queries
CREATE INDEX IF NOT EXISTS idx_patient_dob ON patient(dob);
CREATE INDEX IF NOT EXISTS idx_patient_created_at ON patient(created_at);
CREATE INDEX IF NOT EXISTS idx_patient_active ON patient(is_active) WHERE is_active = true;

CREATE INDEX IF NOT EXISTS idx_audit_user_ts ON audit_log(user_id, timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_audit_table_record ON audit_log(table_name, record_id);

CREATE INDEX IF NOT EXISTS idx_user_access_patient ON user_patient_access(patient_id) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_user_access_user ON user_patient_access(user_id) WHERE is_active = true;

-- Partial index for active patients only (more efficient)
CREATE INDEX IF NOT EXISTS idx_patient_active_only ON patient(id) WHERE is_active = true;

2025-07-28 16:35:24,502 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [generated in 0.03681s] ()
2025-07-28 16:35:24,503 - sqlalchemy.engine.Engine - INFO - _connection_rollback_impl:2701 - ROLLBACK
2025-07-28 16:35:24,504 - main - ERROR - initialize_database:67 - Database initialization failed: (sqlite3.ProgrammingError) You can only execute one statement at a time.
[SQL: 
-- Performance indexes for common queries
CREATE INDEX IF NOT EXISTS idx_patient_dob ON patient(dob);
CREATE INDEX IF NOT EXISTS idx_patient_created_at ON patient(created_at);
CREATE INDEX IF NOT EXISTS idx_patient_active ON patient(is_active) WHERE is_active = true;

CREATE INDEX IF NOT EXISTS idx_audit_user_ts ON audit_log(user_id, timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_audit_table_record ON audit_log(table_name, record_id);

CREATE INDEX IF NOT EXISTS idx_user_access_patient ON user_patient_access(patient_id) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_user_access_user ON user_patient_access(user_id) WHERE is_active = true;

-- Partial index for active patients only (more efficient)
CREATE INDEX IF NOT EXISTS idx_patient_active_only ON patient(id) WHERE is_active = true;
]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-07-28 16:35:24,519 - main - ERROR - initialize_application:162 - Application initialization failed: (sqlite3.ProgrammingError) You can only execute one statement at a time.
[SQL: 
-- Performance indexes for common queries
CREATE INDEX IF NOT EXISTS idx_patient_dob ON patient(dob);
CREATE INDEX IF NOT EXISTS idx_patient_created_at ON patient(created_at);
CREATE INDEX IF NOT EXISTS idx_patient_active ON patient(is_active) WHERE is_active = true;

CREATE INDEX IF NOT EXISTS idx_audit_user_ts ON audit_log(user_id, timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_audit_table_record ON audit_log(table_name, record_id);

CREATE INDEX IF NOT EXISTS idx_user_access_patient ON user_patient_access(patient_id) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_user_access_user ON user_patient_access(user_id) WHERE is_active = true;

-- Partial index for active patients only (more efficient)
CREATE INDEX IF NOT EXISTS idx_patient_active_only ON patient(id) WHERE is_active = true;
]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-07-28 16:36:37,521 - main - INFO - initialize_application:136 - Initializing Psychiatry EMR v1.0.0
2025-07-28 16:36:37,522 - main - INFO - check_environment:123 - Environment configuration validated
2025-07-28 16:37:42,820 - security.encryption - INFO - initialize_encryption:83 - Encryption service initialized
2025-07-28 16:37:42,907 - services.database - INFO - get_engine:31 - Database engine initialized
2025-07-28 16:37:43,336 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-28 16:37:43,337 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-28 16:37:43,339 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-28 16:37:43,390 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-28 16:37:43,393 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-28 16:37:43,405 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-28 16:37:43,407 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-28 16:37:43,409 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-28 16:37:43,422 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-28 16:37:43,423 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-28 16:37:43,424 - main - INFO - initialize_database:57 - Database tables created/verified
2025-07-28 16:37:43,424 - main - INFO - initialize_database:68 - Skipping performance indexes for SQLite
2025-07-28 16:37:53,955 - main - WARNING - <module>:203 - Could not load settings for app configuration: App.__init__() got an unexpected keyword argument 'state'. Did you mean '_state'?
2025-07-28 16:39:32,585 - main - INFO - initialize_application:136 - Initializing Psychiatry EMR v1.0.0
2025-07-28 16:39:32,586 - main - INFO - check_environment:123 - Environment configuration validated
2025-07-29 00:05:57,063 - main - INFO - initialize_application:143 - Initializing Psychiatry EMR v1.0.0
2025-07-29 00:05:57,121 - main - INFO - check_environment:130 - Environment configuration validated
2025-07-29 00:23:09,528 - __main__ - INFO - initialize_application:143 - Initializing Psychiatry EMR v1.0.0
2025-07-29 00:23:09,529 - __main__ - INFO - check_environment:130 - Environment configuration validated
2025-07-29 00:24:30,603 - __main__ - INFO - initialize_application:143 - Initializing Psychiatry EMR v1.0.0
2025-07-29 00:24:30,604 - __main__ - INFO - check_environment:130 - Environment configuration validated
2025-07-29 01:30:04,873 - __main__ - INFO - initialize_application:143 - Initializing Psychiatry EMR v1.0.0
2025-07-29 01:30:04,904 - __main__ - INFO - check_environment:130 - Environment configuration validated
2025-07-29 01:32:12,597 - __main__ - INFO - initialize_application:143 - Initializing Psychiatry EMR v1.0.0
2025-07-29 01:32:12,599 - __main__ - INFO - check_environment:130 - Environment configuration validated
2025-07-29 01:32:12,817 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 01:32:14,277 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 01:32:14,297 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 01:32:14,301 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 01:32:14,303 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:32:14,320 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA temp.table_info("user")
2025-07-29 01:32:14,333 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:32:14,336 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 01:32:14,341 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:32:14,345 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA temp.table_info("usersession")
2025-07-29 01:32:14,359 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:32:14,366 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 01:32:14,385 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:32:14,403 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 01:32:14,420 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:32:14,469 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 01:32:14,501 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:32:14,503 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 01:32:14,553 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:32:14,601 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - 
CREATE TABLE user (
	id INTEGER NOT NULL, 
	username VARCHAR(50) NOT NULL, 
	password_hash VARCHAR(255) NOT NULL, 
	full_name VARCHAR(100) NOT NULL, 
	email VARCHAR(100), 
	role VARCHAR(20) NOT NULL, 
	is_active BOOLEAN NOT NULL, 
	is_verified BOOLEAN NOT NULL, 
	created_at DATETIME NOT NULL, 
	updated_at DATETIME, 
	last_login DATETIME, 
	password_changed_at DATETIME, 
	PRIMARY KEY (id)
)


2025-07-29 01:32:14,680 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [no key 0.07914s] ()
2025-07-29 01:32:14,929 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - CREATE UNIQUE INDEX ix_user_username ON user (username)
2025-07-29 01:32:15,113 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [no key 0.18370s] ()
2025-07-29 01:32:15,461 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - 
CREATE TABLE usersession (
	id INTEGER NOT NULL, 
	user_id INTEGER NOT NULL, 
	session_token VARCHAR(255) NOT NULL, 
	ip_address VARCHAR(45), 
	user_agent VARCHAR(500), 
	is_active BOOLEAN NOT NULL, 
	created_at DATETIME NOT NULL, 
	last_activity DATETIME NOT NULL, 
	expires_at DATETIME NOT NULL, 
	logged_out_at DATETIME, 
	PRIMARY KEY (id), 
	FOREIGN KEY(user_id) REFERENCES user (id)
)


2025-07-29 01:32:15,493 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [no key 0.03149s] ()
2025-07-29 01:32:15,735 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - CREATE INDEX ix_usersession_user_id ON usersession (user_id)
2025-07-29 01:32:15,739 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [no key 0.00463s] ()
2025-07-29 01:32:16,004 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - CREATE UNIQUE INDEX ix_usersession_session_token ON usersession (session_token)
2025-07-29 01:32:16,019 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [no key 0.01526s] ()
2025-07-29 01:32:16,235 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 01:32:16,239 - __main__ - INFO - initialize_database:64 - Database tables created/verified
2025-07-29 01:32:16,242 - __main__ - INFO - initialize_database:75 - Skipping performance indexes for SQLite
2025-07-29 01:32:24,972 - __main__ - INFO - <module>:362 - Starting Psychiatry EMR application...
2025-07-29 01:32:24,973 - __main__ - ERROR - <module>:369 - Failed to start application: 'App' object has no attribute 'run'
2025-07-29 01:32:54,616 - __main__ - INFO - initialize_application:143 - Initializing Psychiatry EMR v1.0.0
2025-07-29 01:32:54,617 - __main__ - INFO - check_environment:130 - Environment configuration validated
2025-07-29 01:32:54,812 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 01:32:54,915 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 01:32:54,975 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 01:32:55,095 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 01:32:55,122 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:32:55,317 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 01:32:55,431 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:32:55,559 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 01:32:55,865 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:32:55,941 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 01:32:56,068 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:32:56,102 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 01:32:56,128 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:32:56,265 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 01:32:56,317 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:32:56,441 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 01:32:56,499 - __main__ - INFO - initialize_database:64 - Database tables created/verified
2025-07-29 01:32:56,564 - __main__ - INFO - initialize_database:75 - Skipping performance indexes for SQLite
2025-07-29 01:33:01,691 - __main__ - INFO - <module>:362 - Starting Psychiatry EMR application...
2025-07-29 01:33:01,692 - __main__ - ERROR - <module>:369 - Failed to start application: 'App' object has no attribute 'run'
2025-07-29 01:48:00,828 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 01:48:02,696 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 01:48:02,698 - labap1py.labap1py - INFO - initialize_application:101 - Encryption service initialized
2025-07-29 01:48:03,205 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 01:48:03,443 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 01:48:03,465 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 01:48:03,467 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:48:03,491 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 01:48:03,495 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:48:03,506 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 01:48:03,509 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:48:03,511 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 01:48:03,513 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:48:03,515 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 01:48:03,516 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:48:03,520 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 01:48:03,522 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:48:03,523 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 01:48:03,524 - labap1py.labap1py - INFO - initialize_database:58 - Database tables created/verified
2025-07-29 01:48:03,524 - labap1py.labap1py - INFO - initialize_application:105 - Database initialized
2025-07-29 01:48:12,239 - concurrent.futures - ERROR - _invoke_callbacks:342 - exception calling callback for <Future at 0x1f5dce334d0 state=finished raised AttributeError>
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\sqlalchemy\engine\base.py", line 1411, in execute
    meth = statement._execute_on_connection
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'Text' object has no attribute '_execute_on_connection'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\projects\labap1py\labap1py\labap1py.py", line 220, in health_check
    conn.execute(rx.text("SELECT 1"))
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\sqlalchemy\engine\base.py", line 1413, in execute
    raise exc.ObjectNotExecutableError(statement) from err
sqlalchemy.exc.ObjectNotExecutableError: Not an executable object: {"name": "RadixThemesText", "props": ["as:\"p\""], "contents": "", "special_props": [], "children": [{"name": "", "props": [], "contents": "\"SELECT 1\"", "special_props": [], "children": [], "autofocus": false}], "autofocus": false}

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Python313\Lib\concurrent\futures\_base.py", line 340, in _invoke_callbacks
    callback(self)
    ~~~~~~~~^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\reflex\app.py", line 611, in callback
    return f.result()
           ~~~~~~~~^^
  File "C:\Python313\Lib\concurrent\futures\_base.py", line 449, in result
    return self.__get_result()
           ~~~~~~~~~~~~~~~~~^^
  File "C:\Python313\Lib\concurrent\futures\_base.py", line 401, in __get_result
    raise self._exception
  File "C:\Python313\Lib\concurrent\futures\thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\reflex\app.py", line 1215, in _compile
    self._compile_page(route, save_page=should_compile)
    ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\reflex\app.py", line 854, in _compile_page
    component = compiler.compile_unevaluated_page(
        route, self._unevaluated_pages[route], self.style, self.theme
    )
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\reflex\compiler\compiler.py", line 800, in compile_unevaluated_page
    component = into_component(page.component)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\reflex\compiler\compiler.py", line 728, in into_component
    component_called = component()
  File "C:\Users\<USER>\projects\labap1py\labap1py\labap1py.py", line 230, in health_check
    return rx.json({
           ^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\reflex\__init__.py", line 371, in __getattr__
    return getattr(name)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\reflex\utils\lazy_loader.py", line 88, in __getattr__
    raise AttributeError(msg)
AttributeError: No reflex attribute json
Happened while evaluating page 'health'
2025-07-29 01:49:05,603 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 01:49:05,605 - labap1py.labap1py - ERROR - initialize_application:113 - Application initialization failed: 
2025-07-29 01:51:05,528 - __main__ - INFO - initialize_application:143 - Initializing Psychiatry EMR v1.0.0
2025-07-29 01:51:05,529 - __main__ - INFO - check_environment:130 - Environment configuration validated
2025-07-29 01:51:05,631 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 01:51:05,899 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 01:51:05,907 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 01:51:05,909 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 01:51:05,911 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:51:05,916 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 01:51:05,917 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:51:05,918 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 01:51:05,920 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:51:05,923 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 01:51:05,924 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:51:05,926 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 01:51:05,928 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:51:05,932 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 01:51:05,937 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:51:05,939 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 01:51:05,940 - __main__ - INFO - initialize_database:64 - Database tables created/verified
2025-07-29 01:51:05,941 - __main__ - INFO - initialize_database:75 - Skipping performance indexes for SQLite
2025-07-29 01:51:07,755 - __main__ - INFO - <module>:362 - Starting Psychiatry EMR application...
2025-07-29 01:51:07,756 - __main__ - ERROR - <module>:369 - Failed to start application: 'App' object has no attribute 'run'
2025-07-29 01:54:53,228 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 01:54:53,230 - labap1py.labap1py - ERROR - initialize_application:113 - Application initialization failed: 
2025-07-29 01:55:44,023 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 01:55:44,199 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 01:55:44,200 - labap1py.labap1py - INFO - initialize_application:101 - Encryption service initialized
2025-07-29 01:55:44,255 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 01:55:44,258 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 01:55:44,259 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 01:55:44,260 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:55:44,266 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 01:55:44,273 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:55:44,276 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 01:55:44,286 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:55:44,290 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 01:55:44,293 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:55:44,294 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 01:55:44,299 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:55:44,304 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 01:55:44,306 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:55:44,308 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 01:55:44,309 - labap1py.labap1py - INFO - initialize_database:58 - Database tables created/verified
2025-07-29 01:55:44,310 - labap1py.labap1py - INFO - initialize_application:105 - Database initialized
2025-07-29 01:55:44,374 - alembic.runtime.migration - INFO - __init__:207 - Context impl SQLiteImpl.
2025-07-29 01:55:44,374 - alembic.runtime.migration - INFO - __init__:210 - Will assume non-transactional DDL.
2025-07-29 01:55:44,390 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'auditlog'
2025-07-29 01:55:44,390 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'patient'
2025-07-29 01:55:44,391 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_created_at'' on '('created_at',)'
2025-07-29 01:55:44,392 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_dob'' on '('dob',)'
2025-07-29 01:55:44,392 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'user'
2025-07-29 01:55:44,393 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_user_username'' on '('username',)'
2025-07-29 01:55:44,393 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'presentillness'
2025-07-29 01:55:44,394 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_presentillness_assessment_date'' on '('assessment_date',)'
2025-07-29 01:55:44,394 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'userpatientaccess'
2025-07-29 01:55:44,395 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'usersession'
2025-07-29 01:55:44,399 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_session_token'' on '('session_token',)'
2025-07-29 01:55:44,403 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_user_id'' on '('user_id',)'
2025-07-29 01:57:38,992 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 01:57:39,111 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 01:57:39,114 - labap1py.labap1py - INFO - initialize_application:101 - Encryption service initialized
2025-07-29 01:57:39,150 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 01:57:39,154 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 01:57:39,155 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 01:57:39,156 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:57:39,159 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 01:57:39,159 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:57:39,161 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 01:57:39,168 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:57:39,171 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 01:57:39,173 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:57:39,176 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 01:57:39,182 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:57:39,188 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 01:57:39,189 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:57:39,190 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 01:57:39,193 - labap1py.labap1py - INFO - initialize_database:58 - Database tables created/verified
2025-07-29 01:57:39,194 - labap1py.labap1py - INFO - initialize_application:105 - Database initialized
2025-07-29 01:57:39,238 - alembic.runtime.migration - INFO - __init__:207 - Context impl SQLiteImpl.
2025-07-29 01:57:39,240 - alembic.runtime.migration - INFO - __init__:210 - Will assume non-transactional DDL.
2025-07-29 01:57:39,250 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'auditlog'
2025-07-29 01:57:39,254 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'patient'
2025-07-29 01:57:39,255 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_created_at'' on '('created_at',)'
2025-07-29 01:57:39,255 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_dob'' on '('dob',)'
2025-07-29 01:57:39,256 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'user'
2025-07-29 01:57:39,257 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_user_username'' on '('username',)'
2025-07-29 01:57:39,257 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'presentillness'
2025-07-29 01:57:39,258 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_presentillness_assessment_date'' on '('assessment_date',)'
2025-07-29 01:57:39,258 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'userpatientaccess'
2025-07-29 01:57:39,260 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'usersession'
2025-07-29 01:57:39,261 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_session_token'' on '('session_token',)'
2025-07-29 01:57:39,274 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_user_id'' on '('user_id',)'
2025-07-29 01:58:22,368 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 01:58:22,478 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 01:58:22,478 - labap1py.labap1py - INFO - initialize_application:101 - Encryption service initialized
2025-07-29 01:58:22,514 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 01:58:22,519 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 01:58:22,520 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 01:58:22,521 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:58:22,523 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 01:58:22,523 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:58:22,525 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 01:58:22,530 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:58:22,535 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 01:58:22,536 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:58:22,538 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 01:58:22,539 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:58:22,540 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 01:58:22,541 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 01:58:22,547 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 01:58:22,552 - labap1py.labap1py - INFO - initialize_database:58 - Database tables created/verified
2025-07-29 01:58:22,552 - labap1py.labap1py - INFO - initialize_application:105 - Database initialized
2025-07-29 01:58:22,589 - alembic.runtime.migration - INFO - __init__:207 - Context impl SQLiteImpl.
2025-07-29 01:58:22,590 - alembic.runtime.migration - INFO - __init__:210 - Will assume non-transactional DDL.
2025-07-29 01:58:22,603 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'auditlog'
2025-07-29 01:58:22,605 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'patient'
2025-07-29 01:58:22,607 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_created_at'' on '('created_at',)'
2025-07-29 01:58:22,608 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_dob'' on '('dob',)'
2025-07-29 01:58:22,622 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'user'
2025-07-29 01:58:22,623 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_user_username'' on '('username',)'
2025-07-29 01:58:22,624 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'presentillness'
2025-07-29 01:58:22,625 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_presentillness_assessment_date'' on '('assessment_date',)'
2025-07-29 01:58:22,637 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'userpatientaccess'
2025-07-29 01:58:22,641 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'usersession'
2025-07-29 01:58:22,651 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_session_token'' on '('session_token',)'
2025-07-29 01:58:22,653 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_user_id'' on '('user_id',)'
2025-07-29 02:00:02,771 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 02:00:02,973 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 02:00:02,974 - labap1py.labap1py - INFO - initialize_application:101 - Encryption service initialized
2025-07-29 02:00:03,030 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 02:00:03,044 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 02:00:03,045 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 02:00:03,046 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 02:00:03,054 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 02:00:03,060 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 02:00:03,066 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 02:00:03,080 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 02:00:03,094 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 02:00:03,095 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 02:00:03,096 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 02:00:03,103 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 02:00:03,108 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 02:00:03,110 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 02:00:03,113 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 02:00:03,114 - labap1py.labap1py - INFO - initialize_database:58 - Database tables created/verified
2025-07-29 02:00:03,117 - labap1py.labap1py - INFO - initialize_application:105 - Database initialized
2025-07-29 02:00:03,158 - alembic.runtime.migration - INFO - __init__:207 - Context impl SQLiteImpl.
2025-07-29 02:00:03,159 - alembic.runtime.migration - INFO - __init__:210 - Will assume non-transactional DDL.
2025-07-29 02:00:03,164 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'auditlog'
2025-07-29 02:00:03,168 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'patient'
2025-07-29 02:00:03,170 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_created_at'' on '('created_at',)'
2025-07-29 02:00:03,173 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_dob'' on '('dob',)'
2025-07-29 02:00:03,174 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'user'
2025-07-29 02:00:03,176 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_user_username'' on '('username',)'
2025-07-29 02:00:03,177 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'presentillness'
2025-07-29 02:00:03,177 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_presentillness_assessment_date'' on '('assessment_date',)'
2025-07-29 02:00:03,178 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'userpatientaccess'
2025-07-29 02:00:03,178 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'usersession'
2025-07-29 02:00:03,179 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_session_token'' on '('session_token',)'
2025-07-29 02:00:03,179 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_user_id'' on '('user_id',)'
2025-07-29 02:04:15,150 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 02:04:15,474 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 02:04:15,476 - labap1py.labap1py - INFO - initialize_application:101 - Encryption service initialized
2025-07-29 02:04:15,567 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 02:04:15,573 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 02:04:15,580 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 02:04:15,582 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 02:04:15,585 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 02:04:15,590 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 02:04:15,592 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 02:04:15,596 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 02:04:15,605 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 02:04:15,607 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 02:04:15,611 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 02:04:15,625 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 02:04:15,629 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 02:04:15,630 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 02:04:15,633 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 02:04:15,645 - labap1py.labap1py - INFO - initialize_database:58 - Database tables created/verified
2025-07-29 02:04:15,646 - labap1py.labap1py - INFO - initialize_application:105 - Database initialized
2025-07-29 02:04:15,740 - alembic.runtime.migration - INFO - __init__:207 - Context impl SQLiteImpl.
2025-07-29 02:04:15,742 - alembic.runtime.migration - INFO - __init__:210 - Will assume non-transactional DDL.
2025-07-29 02:04:15,759 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'auditlog'
2025-07-29 02:04:15,762 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'patient'
2025-07-29 02:04:15,765 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_created_at'' on '('created_at',)'
2025-07-29 02:04:15,766 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_dob'' on '('dob',)'
2025-07-29 02:04:15,767 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'user'
2025-07-29 02:04:15,768 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_user_username'' on '('username',)'
2025-07-29 02:04:15,772 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'presentillness'
2025-07-29 02:04:15,775 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_presentillness_assessment_date'' on '('assessment_date',)'
2025-07-29 02:04:15,778 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'userpatientaccess'
2025-07-29 02:04:15,782 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'usersession'
2025-07-29 02:04:15,783 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_session_token'' on '('session_token',)'
2025-07-29 02:04:15,785 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_user_id'' on '('user_id',)'
2025-07-29 02:09:09,811 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 02:09:10,401 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 02:09:10,602 - labap1py.labap1py - INFO - initialize_application:101 - Encryption service initialized
2025-07-29 02:09:10,761 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 02:09:10,792 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 02:09:10,901 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 02:09:10,995 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 02:09:11,008 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 02:09:11,115 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 02:09:11,135 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 02:09:11,137 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 02:09:11,200 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 02:09:11,204 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 02:09:11,216 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 02:09:11,220 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 02:09:11,311 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 02:09:11,344 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 02:09:11,349 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 02:09:11,352 - labap1py.labap1py - INFO - initialize_database:58 - Database tables created/verified
2025-07-29 02:09:11,352 - labap1py.labap1py - INFO - initialize_application:105 - Database initialized
2025-07-29 02:09:11,414 - alembic.runtime.migration - INFO - __init__:207 - Context impl SQLiteImpl.
2025-07-29 02:09:11,415 - alembic.runtime.migration - INFO - __init__:210 - Will assume non-transactional DDL.
2025-07-29 02:09:11,444 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'auditlog'
2025-07-29 02:09:11,446 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'patient'
2025-07-29 02:09:11,447 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_created_at'' on '('created_at',)'
2025-07-29 02:09:11,450 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_dob'' on '('dob',)'
2025-07-29 02:09:11,452 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'user'
2025-07-29 02:09:11,458 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_user_username'' on '('username',)'
2025-07-29 02:09:11,462 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'presentillness'
2025-07-29 02:09:11,464 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_presentillness_assessment_date'' on '('assessment_date',)'
2025-07-29 02:09:11,465 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'userpatientaccess'
2025-07-29 02:09:11,467 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'usersession'
2025-07-29 02:09:11,469 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_session_token'' on '('session_token',)'
2025-07-29 02:09:11,483 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_user_id'' on '('user_id',)'
2025-07-29 02:14:25,613 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 02:14:26,201 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 02:14:26,270 - labap1py.labap1py - INFO - initialize_application:101 - Encryption service initialized
2025-07-29 02:14:26,483 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 02:14:26,605 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 02:14:26,724 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 02:14:26,846 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 02:14:27,004 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 02:14:27,110 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 02:14:27,249 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 02:14:27,370 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 02:14:27,507 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 02:14:27,569 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 02:14:27,663 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 02:14:27,694 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 02:14:27,777 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 02:14:27,824 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 02:14:27,928 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 02:14:27,955 - labap1py.labap1py - INFO - initialize_database:58 - Database tables created/verified
2025-07-29 02:14:27,960 - labap1py.labap1py - INFO - initialize_application:105 - Database initialized
2025-07-29 02:14:28,214 - alembic.runtime.migration - INFO - __init__:207 - Context impl SQLiteImpl.
2025-07-29 02:14:28,229 - alembic.runtime.migration - INFO - __init__:210 - Will assume non-transactional DDL.
2025-07-29 02:14:28,262 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'auditlog'
2025-07-29 02:14:28,275 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'patient'
2025-07-29 02:14:28,327 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_created_at'' on '('created_at',)'
2025-07-29 02:14:28,340 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_dob'' on '('dob',)'
2025-07-29 02:14:28,358 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'user'
2025-07-29 02:14:28,374 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_user_username'' on '('username',)'
2025-07-29 02:14:28,378 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'presentillness'
2025-07-29 02:14:28,402 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_presentillness_assessment_date'' on '('assessment_date',)'
2025-07-29 02:14:28,413 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'userpatientaccess'
2025-07-29 02:14:28,427 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'usersession'
2025-07-29 02:14:28,444 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_session_token'' on '('session_token',)'
2025-07-29 02:14:28,461 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_user_id'' on '('user_id',)'
2025-07-29 11:47:25,144 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 11:47:25,764 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 11:47:25,765 - labap1py.labap1py - INFO - initialize_application:101 - Encryption service initialized
2025-07-29 11:47:27,113 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 11:47:27,394 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 11:47:27,411 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 11:47:27,414 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 11:47:27,446 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 11:47:27,449 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 11:47:27,454 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 11:47:27,463 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 11:47:27,465 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 11:47:27,466 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 11:47:27,473 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 11:47:27,476 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 11:47:27,480 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 11:47:27,481 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 11:47:27,483 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 11:47:27,487 - labap1py.labap1py - INFO - initialize_database:58 - Database tables created/verified
2025-07-29 11:47:27,487 - labap1py.labap1py - INFO - initialize_application:105 - Database initialized
2025-07-29 11:47:27,632 - alembic.runtime.migration - INFO - __init__:207 - Context impl SQLiteImpl.
2025-07-29 11:47:27,633 - alembic.runtime.migration - INFO - __init__:210 - Will assume non-transactional DDL.
2025-07-29 11:47:27,671 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'auditlog'
2025-07-29 11:47:27,675 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'patient'
2025-07-29 11:47:27,679 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_created_at'' on '('created_at',)'
2025-07-29 11:47:27,693 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_dob'' on '('dob',)'
2025-07-29 11:47:27,703 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'user'
2025-07-29 11:47:27,706 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_user_username'' on '('username',)'
2025-07-29 11:47:27,710 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'presentillness'
2025-07-29 11:47:27,712 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_presentillness_assessment_date'' on '('assessment_date',)'
2025-07-29 11:47:27,723 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'userpatientaccess'
2025-07-29 11:47:27,724 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'usersession'
2025-07-29 11:47:27,757 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_session_token'' on '('session_token',)'
2025-07-29 11:47:27,766 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_user_id'' on '('user_id',)'
2025-07-29 11:52:23,277 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 11:52:23,401 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 11:52:23,402 - labap1py.labap1py - INFO - initialize_application:101 - Encryption service initialized
2025-07-29 11:52:23,833 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 11:52:23,838 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 11:52:23,840 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 11:52:23,844 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 11:52:23,847 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 11:52:23,848 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 11:52:23,849 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 11:52:23,851 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 11:52:23,857 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 11:52:23,861 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 11:52:23,863 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 11:52:23,865 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 11:52:23,866 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 11:52:23,867 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 11:52:23,872 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 11:52:23,877 - labap1py.labap1py - INFO - initialize_database:58 - Database tables created/verified
2025-07-29 11:52:23,878 - labap1py.labap1py - INFO - initialize_application:105 - Database initialized
2025-07-29 11:52:23,917 - alembic.runtime.migration - INFO - __init__:207 - Context impl SQLiteImpl.
2025-07-29 11:52:23,918 - alembic.runtime.migration - INFO - __init__:210 - Will assume non-transactional DDL.
2025-07-29 11:52:23,932 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'auditlog'
2025-07-29 11:52:23,933 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'patient'
2025-07-29 11:52:23,934 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_created_at'' on '('created_at',)'
2025-07-29 11:52:23,934 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_dob'' on '('dob',)'
2025-07-29 11:52:23,935 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'user'
2025-07-29 11:52:23,942 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_user_username'' on '('username',)'
2025-07-29 11:52:23,944 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'presentillness'
2025-07-29 11:52:23,945 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_presentillness_assessment_date'' on '('assessment_date',)'
2025-07-29 11:52:23,946 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'userpatientaccess'
2025-07-29 11:52:23,947 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'usersession'
2025-07-29 11:52:23,948 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_session_token'' on '('session_token',)'
2025-07-29 11:52:23,949 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_user_id'' on '('user_id',)'
2025-07-29 11:53:46,655 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 11:53:46,843 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 11:53:46,844 - labap1py.labap1py - INFO - initialize_application:101 - Encryption service initialized
2025-07-29 11:53:46,903 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 11:53:46,911 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 11:53:46,912 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 11:53:46,913 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 11:53:46,919 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 11:53:46,926 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 11:53:46,927 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 11:53:46,929 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 11:53:46,931 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 11:53:46,931 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 11:53:46,941 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 11:53:46,942 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 11:53:46,945 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 11:53:46,946 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 11:53:46,947 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 11:53:46,948 - labap1py.labap1py - INFO - initialize_database:58 - Database tables created/verified
2025-07-29 11:53:46,955 - labap1py.labap1py - INFO - initialize_application:105 - Database initialized
2025-07-29 11:53:46,994 - alembic.runtime.migration - INFO - __init__:207 - Context impl SQLiteImpl.
2025-07-29 11:53:46,995 - alembic.runtime.migration - INFO - __init__:210 - Will assume non-transactional DDL.
2025-07-29 11:53:47,005 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'auditlog'
2025-07-29 11:53:47,008 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'patient'
2025-07-29 11:53:47,009 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_created_at'' on '('created_at',)'
2025-07-29 11:53:47,009 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_dob'' on '('dob',)'
2025-07-29 11:53:47,010 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'user'
2025-07-29 11:53:47,012 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_user_username'' on '('username',)'
2025-07-29 11:53:47,013 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'presentillness'
2025-07-29 11:53:47,014 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_presentillness_assessment_date'' on '('assessment_date',)'
2025-07-29 11:53:47,015 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'userpatientaccess'
2025-07-29 11:53:47,020 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'usersession'
2025-07-29 11:53:47,023 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_session_token'' on '('session_token',)'
2025-07-29 11:53:47,024 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_user_id'' on '('user_id',)'
2025-07-29 11:56:13,027 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 11:56:13,379 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 11:56:13,432 - labap1py.labap1py - INFO - initialize_application:101 - Encryption service initialized
2025-07-29 11:56:13,649 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 11:56:13,765 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 11:56:13,892 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 11:56:14,038 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 11:56:14,114 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 11:56:14,165 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 11:56:14,212 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 11:56:14,231 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 11:56:14,283 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 11:56:14,345 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 11:56:14,364 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 11:56:14,411 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 11:56:14,464 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 11:56:14,576 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 11:56:14,627 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 11:56:14,670 - labap1py.labap1py - INFO - initialize_database:58 - Database tables created/verified
2025-07-29 11:56:14,729 - labap1py.labap1py - INFO - initialize_application:105 - Database initialized
2025-07-29 11:56:14,990 - alembic.runtime.migration - INFO - __init__:207 - Context impl SQLiteImpl.
2025-07-29 11:56:15,132 - alembic.runtime.migration - INFO - __init__:210 - Will assume non-transactional DDL.
2025-07-29 11:56:15,312 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'auditlog'
2025-07-29 11:56:15,329 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'patient'
2025-07-29 11:56:15,349 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_created_at'' on '('created_at',)'
2025-07-29 11:56:15,392 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_dob'' on '('dob',)'
2025-07-29 11:56:15,397 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'user'
2025-07-29 11:56:15,399 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_user_username'' on '('username',)'
2025-07-29 11:56:15,408 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'presentillness'
2025-07-29 11:56:15,414 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_presentillness_assessment_date'' on '('assessment_date',)'
2025-07-29 11:56:15,427 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'userpatientaccess'
2025-07-29 11:56:15,439 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'usersession'
2025-07-29 11:56:15,464 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_session_token'' on '('session_token',)'
2025-07-29 11:56:15,480 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_user_id'' on '('user_id',)'
2025-07-29 11:58:43,094 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 11:58:43,265 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 11:58:43,266 - labap1py.labap1py - INFO - initialize_application:101 - Encryption service initialized
2025-07-29 11:58:43,314 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 11:58:43,316 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 11:58:43,320 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 11:58:43,326 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 11:58:43,328 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 11:58:43,329 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 11:58:43,331 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 11:58:43,332 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 11:58:43,333 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 11:58:43,340 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 11:58:43,344 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 11:58:43,346 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 11:58:43,348 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 11:58:43,349 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 11:58:43,357 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 11:58:43,362 - labap1py.labap1py - INFO - initialize_database:58 - Database tables created/verified
2025-07-29 11:58:43,362 - labap1py.labap1py - INFO - initialize_application:105 - Database initialized
2025-07-29 11:58:43,481 - alembic.runtime.migration - INFO - __init__:207 - Context impl SQLiteImpl.
2025-07-29 11:58:43,482 - alembic.runtime.migration - INFO - __init__:210 - Will assume non-transactional DDL.
2025-07-29 11:58:43,491 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'auditlog'
2025-07-29 11:58:43,495 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'patient'
2025-07-29 11:58:43,497 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_created_at'' on '('created_at',)'
2025-07-29 11:58:43,499 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_dob'' on '('dob',)'
2025-07-29 11:58:43,503 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'user'
2025-07-29 11:58:43,504 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_user_username'' on '('username',)'
2025-07-29 11:58:43,505 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'presentillness'
2025-07-29 11:58:43,506 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_presentillness_assessment_date'' on '('assessment_date',)'
2025-07-29 11:58:43,507 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'userpatientaccess'
2025-07-29 11:58:43,512 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'usersession'
2025-07-29 11:58:43,520 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_session_token'' on '('session_token',)'
2025-07-29 11:58:43,523 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_user_id'' on '('user_id',)'
2025-07-29 12:07:41,941 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 12:07:42,470 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 12:07:42,569 - labap1py.labap1py - INFO - initialize_application:101 - Encryption service initialized
2025-07-29 12:07:43,171 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 12:07:43,314 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 12:07:43,343 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 12:07:43,354 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:07:43,390 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 12:07:43,392 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:07:43,397 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 12:07:43,409 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:07:43,413 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 12:07:43,421 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:07:43,424 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 12:07:43,426 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:07:43,442 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 12:07:43,455 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:07:43,464 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 12:07:43,476 - labap1py.labap1py - INFO - initialize_database:58 - Database tables created/verified
2025-07-29 12:07:43,477 - labap1py.labap1py - INFO - initialize_application:105 - Database initialized
2025-07-29 12:07:43,646 - alembic.runtime.migration - INFO - __init__:207 - Context impl SQLiteImpl.
2025-07-29 12:07:43,654 - alembic.runtime.migration - INFO - __init__:210 - Will assume non-transactional DDL.
2025-07-29 12:07:43,686 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'auditlog'
2025-07-29 12:07:43,691 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'patient'
2025-07-29 12:07:43,692 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_created_at'' on '('created_at',)'
2025-07-29 12:07:43,693 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_dob'' on '('dob',)'
2025-07-29 12:07:43,693 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'user'
2025-07-29 12:07:43,699 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_user_username'' on '('username',)'
2025-07-29 12:07:43,704 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'presentillness'
2025-07-29 12:07:43,707 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_presentillness_assessment_date'' on '('assessment_date',)'
2025-07-29 12:07:43,708 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'userpatientaccess'
2025-07-29 12:07:43,826 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'usersession'
2025-07-29 12:07:43,993 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_session_token'' on '('session_token',)'
2025-07-29 12:07:44,160 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_user_id'' on '('user_id',)'
2025-07-29 12:12:13,594 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 12:12:13,730 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 12:12:13,731 - labap1py.labap1py - INFO - initialize_application:101 - Encryption service initialized
2025-07-29 12:12:13,771 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 12:12:13,773 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 12:12:13,785 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 12:12:13,786 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:12:13,789 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 12:12:13,791 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:12:13,800 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 12:12:13,803 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:12:13,806 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 12:12:13,821 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:12:13,824 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 12:12:13,830 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:12:13,837 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 12:12:13,838 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:12:13,840 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 12:12:13,845 - labap1py.labap1py - INFO - initialize_database:58 - Database tables created/verified
2025-07-29 12:12:13,848 - labap1py.labap1py - INFO - initialize_application:105 - Database initialized
2025-07-29 12:12:13,888 - alembic.runtime.migration - INFO - __init__:207 - Context impl SQLiteImpl.
2025-07-29 12:12:13,889 - alembic.runtime.migration - INFO - __init__:210 - Will assume non-transactional DDL.
2025-07-29 12:12:13,904 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'auditlog'
2025-07-29 12:12:13,905 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'patient'
2025-07-29 12:12:13,906 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_created_at'' on '('created_at',)'
2025-07-29 12:12:13,906 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_dob'' on '('dob',)'
2025-07-29 12:12:13,907 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'user'
2025-07-29 12:12:13,911 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_user_username'' on '('username',)'
2025-07-29 12:12:13,913 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'presentillness'
2025-07-29 12:12:13,917 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_presentillness_assessment_date'' on '('assessment_date',)'
2025-07-29 12:12:13,920 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'userpatientaccess'
2025-07-29 12:12:13,920 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'usersession'
2025-07-29 12:12:13,921 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_session_token'' on '('session_token',)'
2025-07-29 12:12:13,921 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_user_id'' on '('user_id',)'
2025-07-29 12:14:45,251 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 12:14:45,432 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 12:14:45,433 - labap1py.labap1py - INFO - initialize_application:101 - Encryption service initialized
2025-07-29 12:14:45,603 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 12:14:45,622 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 12:14:45,636 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 12:14:45,639 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:14:45,664 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 12:14:45,754 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:14:45,836 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 12:14:45,936 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:14:45,946 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 12:14:45,978 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:14:45,996 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 12:14:46,005 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:14:46,015 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 12:14:46,069 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:14:46,089 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 12:14:46,120 - labap1py.labap1py - INFO - initialize_database:58 - Database tables created/verified
2025-07-29 12:14:46,133 - labap1py.labap1py - INFO - initialize_application:105 - Database initialized
2025-07-29 12:14:46,249 - alembic.runtime.migration - INFO - __init__:207 - Context impl SQLiteImpl.
2025-07-29 12:14:46,250 - alembic.runtime.migration - INFO - __init__:210 - Will assume non-transactional DDL.
2025-07-29 12:14:46,256 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'auditlog'
2025-07-29 12:14:46,262 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'patient'
2025-07-29 12:14:46,266 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_created_at'' on '('created_at',)'
2025-07-29 12:14:46,267 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_dob'' on '('dob',)'
2025-07-29 12:14:46,268 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'user'
2025-07-29 12:14:46,269 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_user_username'' on '('username',)'
2025-07-29 12:14:46,270 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'presentillness'
2025-07-29 12:14:46,271 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_presentillness_assessment_date'' on '('assessment_date',)'
2025-07-29 12:14:46,272 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'userpatientaccess'
2025-07-29 12:14:46,284 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'usersession'
2025-07-29 12:14:46,285 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_session_token'' on '('session_token',)'
2025-07-29 12:14:46,288 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_user_id'' on '('user_id',)'
2025-07-29 12:17:22,150 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 12:17:22,329 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 12:17:22,337 - labap1py.labap1py - INFO - initialize_application:101 - Encryption service initialized
2025-07-29 12:17:22,434 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 12:17:22,449 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 12:17:22,465 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 12:17:22,468 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:17:22,473 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 12:17:22,489 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:17:22,548 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 12:17:22,558 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:17:22,573 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 12:17:22,593 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:17:22,666 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 12:17:22,676 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:17:22,691 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 12:17:22,706 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:17:22,725 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 12:17:22,743 - labap1py.labap1py - INFO - initialize_database:58 - Database tables created/verified
2025-07-29 12:17:22,747 - labap1py.labap1py - INFO - initialize_application:105 - Database initialized
2025-07-29 12:17:22,842 - alembic.runtime.migration - INFO - __init__:207 - Context impl SQLiteImpl.
2025-07-29 12:17:22,847 - alembic.runtime.migration - INFO - __init__:210 - Will assume non-transactional DDL.
2025-07-29 12:17:22,858 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'auditlog'
2025-07-29 12:17:22,860 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'patient'
2025-07-29 12:17:22,864 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_created_at'' on '('created_at',)'
2025-07-29 12:17:22,865 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_dob'' on '('dob',)'
2025-07-29 12:17:22,870 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'user'
2025-07-29 12:17:22,873 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_user_username'' on '('username',)'
2025-07-29 12:17:22,874 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'presentillness'
2025-07-29 12:17:22,875 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_presentillness_assessment_date'' on '('assessment_date',)'
2025-07-29 12:17:22,876 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'userpatientaccess'
2025-07-29 12:17:22,877 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'usersession'
2025-07-29 12:17:22,881 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_session_token'' on '('session_token',)'
2025-07-29 12:17:22,882 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_user_id'' on '('user_id',)'
2025-07-29 12:20:04,325 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 12:20:04,460 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 12:20:04,460 - labap1py.labap1py - INFO - initialize_application:101 - Encryption service initialized
2025-07-29 12:20:04,506 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 12:20:04,512 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 12:20:04,513 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 12:20:04,514 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:20:04,520 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 12:20:04,524 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:20:04,525 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 12:20:04,527 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:20:04,528 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 12:20:04,530 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:20:04,531 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 12:20:04,539 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:20:04,542 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 12:20:04,553 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:20:04,562 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 12:20:04,576 - labap1py.labap1py - INFO - initialize_database:58 - Database tables created/verified
2025-07-29 12:20:04,578 - labap1py.labap1py - INFO - initialize_application:105 - Database initialized
2025-07-29 12:20:04,636 - alembic.runtime.migration - INFO - __init__:207 - Context impl SQLiteImpl.
2025-07-29 12:20:04,655 - alembic.runtime.migration - INFO - __init__:210 - Will assume non-transactional DDL.
2025-07-29 12:20:04,665 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'auditlog'
2025-07-29 12:20:04,679 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'patient'
2025-07-29 12:20:04,685 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_created_at'' on '('created_at',)'
2025-07-29 12:20:04,714 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_dob'' on '('dob',)'
2025-07-29 12:20:04,760 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'user'
2025-07-29 12:20:04,778 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_user_username'' on '('username',)'
2025-07-29 12:20:04,782 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'presentillness'
2025-07-29 12:20:04,792 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_presentillness_assessment_date'' on '('assessment_date',)'
2025-07-29 12:20:04,794 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'userpatientaccess'
2025-07-29 12:20:04,831 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'usersession'
2025-07-29 12:20:04,846 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_session_token'' on '('session_token',)'
2025-07-29 12:20:04,876 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_user_id'' on '('user_id',)'
2025-07-29 12:25:44,910 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 12:25:45,056 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 12:25:45,060 - labap1py.labap1py - INFO - initialize_application:101 - Encryption service initialized
2025-07-29 12:25:45,110 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 12:25:45,117 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 12:25:45,128 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 12:25:45,132 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:25:45,135 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 12:25:45,149 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:25:45,152 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 12:25:45,154 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:25:45,157 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 12:25:45,159 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:25:45,168 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 12:25:45,170 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:25:45,174 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 12:25:45,177 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:25:45,184 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 12:25:45,185 - labap1py.labap1py - INFO - initialize_database:58 - Database tables created/verified
2025-07-29 12:25:45,186 - labap1py.labap1py - INFO - initialize_application:105 - Database initialized
2025-07-29 12:25:45,244 - alembic.runtime.migration - INFO - __init__:207 - Context impl SQLiteImpl.
2025-07-29 12:25:45,246 - alembic.runtime.migration - INFO - __init__:210 - Will assume non-transactional DDL.
2025-07-29 12:25:45,262 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'auditlog'
2025-07-29 12:25:45,267 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'patient'
2025-07-29 12:25:45,269 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_created_at'' on '('created_at',)'
2025-07-29 12:25:45,270 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_dob'' on '('dob',)'
2025-07-29 12:25:45,274 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'user'
2025-07-29 12:25:45,275 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_user_username'' on '('username',)'
2025-07-29 12:25:45,277 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'presentillness'
2025-07-29 12:25:45,283 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_presentillness_assessment_date'' on '('assessment_date',)'
2025-07-29 12:25:45,284 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'userpatientaccess'
2025-07-29 12:25:45,285 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'usersession'
2025-07-29 12:25:45,286 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_session_token'' on '('session_token',)'
2025-07-29 12:25:45,288 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_user_id'' on '('user_id',)'
2025-07-29 12:29:02,174 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 12:29:02,272 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 12:29:02,272 - labap1py.labap1py - INFO - initialize_application:101 - Encryption service initialized
2025-07-29 12:29:02,297 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 12:29:02,303 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 12:29:02,304 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 12:29:02,311 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:29:02,318 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 12:29:02,332 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:29:02,336 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 12:29:02,390 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:29:02,392 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 12:29:02,396 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:29:02,399 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 12:29:02,400 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:29:02,413 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 12:29:02,416 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:29:02,420 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 12:29:02,426 - labap1py.labap1py - INFO - initialize_database:58 - Database tables created/verified
2025-07-29 12:29:02,429 - labap1py.labap1py - INFO - initialize_application:105 - Database initialized
2025-07-29 12:29:02,456 - alembic.runtime.migration - INFO - __init__:207 - Context impl SQLiteImpl.
2025-07-29 12:29:02,457 - alembic.runtime.migration - INFO - __init__:210 - Will assume non-transactional DDL.
2025-07-29 12:29:02,468 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'auditlog'
2025-07-29 12:29:02,473 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'patient'
2025-07-29 12:29:02,475 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_created_at'' on '('created_at',)'
2025-07-29 12:29:02,476 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_dob'' on '('dob',)'
2025-07-29 12:29:02,480 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'user'
2025-07-29 12:29:02,481 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_user_username'' on '('username',)'
2025-07-29 12:29:02,484 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'presentillness'
2025-07-29 12:29:02,487 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_presentillness_assessment_date'' on '('assessment_date',)'
2025-07-29 12:29:02,489 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'userpatientaccess'
2025-07-29 12:29:02,492 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'usersession'
2025-07-29 12:29:02,533 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_session_token'' on '('session_token',)'
2025-07-29 12:29:02,575 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_user_id'' on '('user_id',)'
2025-07-29 12:29:59,511 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 12:29:59,617 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 12:29:59,618 - labap1py.labap1py - INFO - initialize_application:101 - Encryption service initialized
2025-07-29 12:29:59,641 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 12:29:59,644 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 12:29:59,648 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 12:29:59,652 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:29:59,665 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 12:29:59,667 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:29:59,671 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 12:29:59,682 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:29:59,683 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 12:29:59,684 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:29:59,689 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 12:29:59,690 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:29:59,694 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 12:29:59,698 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:29:59,699 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 12:29:59,700 - labap1py.labap1py - INFO - initialize_database:58 - Database tables created/verified
2025-07-29 12:29:59,700 - labap1py.labap1py - INFO - initialize_application:105 - Database initialized
2025-07-29 12:29:59,733 - alembic.runtime.migration - INFO - __init__:207 - Context impl SQLiteImpl.
2025-07-29 12:29:59,734 - alembic.runtime.migration - INFO - __init__:210 - Will assume non-transactional DDL.
2025-07-29 12:29:59,746 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'auditlog'
2025-07-29 12:29:59,747 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'patient'
2025-07-29 12:29:59,748 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_created_at'' on '('created_at',)'
2025-07-29 12:29:59,749 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_dob'' on '('dob',)'
2025-07-29 12:29:59,749 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'user'
2025-07-29 12:29:59,750 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_user_username'' on '('username',)'
2025-07-29 12:29:59,750 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'presentillness'
2025-07-29 12:29:59,751 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_presentillness_assessment_date'' on '('assessment_date',)'
2025-07-29 12:29:59,754 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'userpatientaccess'
2025-07-29 12:29:59,755 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'usersession'
2025-07-29 12:29:59,756 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_session_token'' on '('session_token',)'
2025-07-29 12:29:59,759 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_user_id'' on '('user_id',)'
2025-07-29 12:32:06,376 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 12:32:06,599 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 12:32:06,607 - labap1py.labap1py - INFO - initialize_application:101 - Encryption service initialized
2025-07-29 12:32:06,684 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 12:32:06,687 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 12:32:06,689 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 12:32:06,692 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:32:06,698 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 12:32:06,718 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:32:06,728 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 12:32:06,744 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:32:06,760 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 12:32:06,763 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:32:06,766 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 12:32:06,776 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:32:06,778 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 12:32:06,787 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:32:06,792 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 12:32:06,794 - labap1py.labap1py - INFO - initialize_database:58 - Database tables created/verified
2025-07-29 12:32:06,799 - labap1py.labap1py - INFO - initialize_application:105 - Database initialized
2025-07-29 12:32:06,860 - alembic.runtime.migration - INFO - __init__:207 - Context impl SQLiteImpl.
2025-07-29 12:32:06,861 - alembic.runtime.migration - INFO - __init__:210 - Will assume non-transactional DDL.
2025-07-29 12:32:06,887 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'auditlog'
2025-07-29 12:32:06,890 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'patient'
2025-07-29 12:32:06,891 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_created_at'' on '('created_at',)'
2025-07-29 12:32:06,892 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_dob'' on '('dob',)'
2025-07-29 12:32:06,893 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'user'
2025-07-29 12:32:06,894 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_user_username'' on '('username',)'
2025-07-29 12:32:06,894 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'presentillness'
2025-07-29 12:32:06,903 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_presentillness_assessment_date'' on '('assessment_date',)'
2025-07-29 12:32:06,905 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'userpatientaccess'
2025-07-29 12:32:06,907 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'usersession'
2025-07-29 12:32:06,908 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_session_token'' on '('session_token',)'
2025-07-29 12:32:06,909 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_user_id'' on '('user_id',)'
2025-07-29 12:35:58,654 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 12:35:58,855 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 12:35:58,855 - labap1py.labap1py - INFO - initialize_application:101 - Encryption service initialized
2025-07-29 12:35:58,938 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 12:35:58,945 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 12:35:58,948 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 12:35:58,951 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:35:58,955 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 12:35:58,956 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:35:58,958 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 12:35:58,963 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:35:58,969 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 12:35:58,973 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:35:58,977 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 12:35:58,985 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:35:58,988 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 12:35:58,989 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 12:35:58,997 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 12:35:59,000 - labap1py.labap1py - INFO - initialize_database:58 - Database tables created/verified
2025-07-29 12:35:59,001 - labap1py.labap1py - INFO - initialize_application:105 - Database initialized
2025-07-29 12:35:59,037 - alembic.runtime.migration - INFO - __init__:207 - Context impl SQLiteImpl.
2025-07-29 12:35:59,038 - alembic.runtime.migration - INFO - __init__:210 - Will assume non-transactional DDL.
2025-07-29 12:35:59,049 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'auditlog'
2025-07-29 12:35:59,049 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'patient'
2025-07-29 12:35:59,050 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_created_at'' on '('created_at',)'
2025-07-29 12:35:59,051 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_dob'' on '('dob',)'
2025-07-29 12:35:59,052 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'user'
2025-07-29 12:35:59,053 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_user_username'' on '('username',)'
2025-07-29 12:35:59,054 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'presentillness'
2025-07-29 12:35:59,054 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_presentillness_assessment_date'' on '('assessment_date',)'
2025-07-29 12:35:59,055 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'userpatientaccess'
2025-07-29 12:35:59,056 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'usersession'
2025-07-29 12:35:59,057 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_session_token'' on '('session_token',)'
2025-07-29 12:35:59,058 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_user_id'' on '('user_id',)'
2025-07-29 15:53:16,445 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 15:53:17,374 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 15:53:17,378 - labap1py.labap1py - INFO - initialize_application:101 - Encryption service initialized
2025-07-29 15:53:17,889 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 15:53:18,091 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 15:53:18,120 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 15:53:18,126 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 15:53:18,210 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 15:53:18,215 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 15:53:18,222 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 15:53:18,230 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 15:53:18,236 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 15:53:18,240 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 15:53:18,243 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 15:53:18,246 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 15:53:18,252 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 15:53:18,254 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 15:53:18,257 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 15:53:18,259 - labap1py.labap1py - INFO - initialize_database:58 - Database tables created/verified
2025-07-29 15:53:18,259 - labap1py.labap1py - INFO - initialize_application:105 - Database initialized
2025-07-29 15:53:18,510 - alembic.runtime.migration - INFO - __init__:207 - Context impl SQLiteImpl.
2025-07-29 15:53:18,510 - alembic.runtime.migration - INFO - __init__:210 - Will assume non-transactional DDL.
2025-07-29 15:53:18,551 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'auditlog'
2025-07-29 15:53:18,554 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'patient'
2025-07-29 15:53:18,572 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_created_at'' on '('created_at',)'
2025-07-29 15:53:18,605 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_dob'' on '('dob',)'
2025-07-29 15:53:18,624 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'user'
2025-07-29 15:53:18,628 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_user_username'' on '('username',)'
2025-07-29 15:53:18,634 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'presentillness'
2025-07-29 15:53:18,638 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_presentillness_assessment_date'' on '('assessment_date',)'
2025-07-29 15:53:18,646 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'userpatientaccess'
2025-07-29 15:53:18,654 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'usersession'
2025-07-29 15:53:18,657 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_session_token'' on '('session_token',)'
2025-07-29 15:53:18,659 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_user_id'' on '('user_id',)'
2025-07-29 15:56:14,156 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 15:56:14,292 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 15:56:14,292 - labap1py.labap1py - INFO - initialize_application:101 - Encryption service initialized
2025-07-29 15:56:14,327 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 15:56:14,329 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 15:56:14,330 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 15:56:14,332 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 15:56:14,337 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 15:56:14,340 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 15:56:14,343 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 15:56:14,344 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 15:56:14,347 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 15:56:14,348 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 15:56:14,351 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 15:56:14,355 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 15:56:14,358 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 15:56:14,360 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 15:56:14,362 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 15:56:14,364 - labap1py.labap1py - INFO - initialize_database:58 - Database tables created/verified
2025-07-29 15:56:14,364 - labap1py.labap1py - INFO - initialize_application:105 - Database initialized
2025-07-29 15:56:14,399 - alembic.runtime.migration - INFO - __init__:207 - Context impl SQLiteImpl.
2025-07-29 15:56:14,400 - alembic.runtime.migration - INFO - __init__:210 - Will assume non-transactional DDL.
2025-07-29 15:56:14,412 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'auditlog'
2025-07-29 15:56:14,414 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'patient'
2025-07-29 15:56:14,415 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_created_at'' on '('created_at',)'
2025-07-29 15:56:14,417 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_dob'' on '('dob',)'
2025-07-29 15:56:14,418 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'user'
2025-07-29 15:56:14,421 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_user_username'' on '('username',)'
2025-07-29 15:56:14,422 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'presentillness'
2025-07-29 15:56:14,423 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_presentillness_assessment_date'' on '('assessment_date',)'
2025-07-29 15:56:14,425 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'userpatientaccess'
2025-07-29 15:56:14,426 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'usersession'
2025-07-29 15:56:14,427 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_session_token'' on '('session_token',)'
2025-07-29 15:56:14,429 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_user_id'' on '('user_id',)'
2025-07-29 15:59:41,135 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 15:59:41,245 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 15:59:41,245 - labap1py.labap1py - INFO - initialize_application:101 - Encryption service initialized
2025-07-29 15:59:41,273 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 15:59:41,275 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 15:59:41,276 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 15:59:41,278 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 15:59:41,281 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 15:59:41,282 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 15:59:41,284 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 15:59:41,285 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 15:59:41,287 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 15:59:41,288 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 15:59:41,290 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 15:59:41,291 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 15:59:41,293 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 15:59:41,295 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 15:59:41,297 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 15:59:41,298 - labap1py.labap1py - INFO - initialize_database:58 - Database tables created/verified
2025-07-29 15:59:41,299 - labap1py.labap1py - INFO - initialize_application:105 - Database initialized
2025-07-29 15:59:41,331 - alembic.runtime.migration - INFO - __init__:207 - Context impl SQLiteImpl.
2025-07-29 15:59:41,332 - alembic.runtime.migration - INFO - __init__:210 - Will assume non-transactional DDL.
2025-07-29 15:59:41,338 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'auditlog'
2025-07-29 15:59:41,339 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'patient'
2025-07-29 15:59:41,341 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_created_at'' on '('created_at',)'
2025-07-29 15:59:41,341 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_dob'' on '('dob',)'
2025-07-29 15:59:41,342 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'user'
2025-07-29 15:59:41,343 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_user_username'' on '('username',)'
2025-07-29 15:59:41,346 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'presentillness'
2025-07-29 15:59:41,347 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_presentillness_assessment_date'' on '('assessment_date',)'
2025-07-29 15:59:41,348 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'userpatientaccess'
2025-07-29 15:59:41,349 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'usersession'
2025-07-29 15:59:41,350 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_session_token'' on '('session_token',)'
2025-07-29 15:59:41,350 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_user_id'' on '('user_id',)'
2025-07-29 16:04:21,340 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 16:04:21,597 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 16:04:21,599 - labap1py.labap1py - INFO - initialize_application:101 - Encryption service initialized
2025-07-29 16:04:21,706 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 16:04:21,715 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 16:04:21,832 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 16:04:22,146 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:04:22,348 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 16:04:22,546 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:04:22,783 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 16:04:22,982 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:04:23,281 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 16:04:23,531 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:04:23,600 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 16:04:23,715 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:04:23,842 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 16:04:23,982 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:04:24,076 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 16:04:24,129 - labap1py.labap1py - INFO - initialize_database:58 - Database tables created/verified
2025-07-29 16:04:24,147 - labap1py.labap1py - INFO - initialize_application:105 - Database initialized
2025-07-29 16:04:24,509 - alembic.runtime.migration - INFO - __init__:207 - Context impl SQLiteImpl.
2025-07-29 16:04:24,530 - alembic.runtime.migration - INFO - __init__:210 - Will assume non-transactional DDL.
2025-07-29 16:04:24,579 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'auditlog'
2025-07-29 16:04:24,616 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'patient'
2025-07-29 16:04:24,642 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_created_at'' on '('created_at',)'
2025-07-29 16:04:24,664 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_dob'' on '('dob',)'
2025-07-29 16:04:24,678 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'user'
2025-07-29 16:04:24,697 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_user_username'' on '('username',)'
2025-07-29 16:04:24,700 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'presentillness'
2025-07-29 16:04:24,731 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_presentillness_assessment_date'' on '('assessment_date',)'
2025-07-29 16:04:24,754 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'userpatientaccess'
2025-07-29 16:04:24,850 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'usersession'
2025-07-29 16:04:24,900 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_session_token'' on '('session_token',)'
2025-07-29 16:04:24,948 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_user_id'' on '('user_id',)'
2025-07-29 16:09:43,420 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 16:09:43,897 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 16:09:43,915 - labap1py.labap1py - INFO - initialize_application:101 - Encryption service initialized
2025-07-29 16:09:44,342 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 16:09:44,407 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 16:09:44,582 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 16:09:44,717 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:09:44,891 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 16:09:44,971 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:09:45,005 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 16:09:45,035 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:09:45,169 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 16:09:45,189 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:09:45,209 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 16:09:45,224 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:09:45,235 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 16:09:45,241 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:09:45,258 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 16:09:45,271 - labap1py.labap1py - INFO - initialize_database:58 - Database tables created/verified
2025-07-29 16:09:45,273 - labap1py.labap1py - INFO - initialize_application:105 - Database initialized
2025-07-29 16:09:45,502 - alembic.runtime.migration - INFO - __init__:207 - Context impl SQLiteImpl.
2025-07-29 16:09:45,504 - alembic.runtime.migration - INFO - __init__:210 - Will assume non-transactional DDL.
2025-07-29 16:09:45,542 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'auditlog'
2025-07-29 16:09:45,555 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'patient'
2025-07-29 16:09:45,558 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_created_at'' on '('created_at',)'
2025-07-29 16:09:45,564 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_dob'' on '('dob',)'
2025-07-29 16:09:45,568 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'user'
2025-07-29 16:09:45,580 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_user_username'' on '('username',)'
2025-07-29 16:09:45,582 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'presentillness'
2025-07-29 16:09:45,583 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_presentillness_assessment_date'' on '('assessment_date',)'
2025-07-29 16:09:45,585 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'userpatientaccess'
2025-07-29 16:09:45,591 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'usersession'
2025-07-29 16:09:45,600 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_session_token'' on '('session_token',)'
2025-07-29 16:09:45,619 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_user_id'' on '('user_id',)'
2025-07-29 16:12:02,766 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 16:12:02,878 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 16:12:02,879 - labap1py.labap1py - INFO - initialize_application:101 - Encryption service initialized
2025-07-29 16:12:02,910 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 16:12:02,912 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 16:12:02,916 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 16:12:02,917 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:12:02,921 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 16:12:02,924 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:12:02,926 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 16:12:02,928 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:12:02,932 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 16:12:02,935 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:12:02,937 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 16:12:02,938 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:12:02,940 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 16:12:02,941 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:12:02,943 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 16:12:02,944 - labap1py.labap1py - INFO - initialize_database:58 - Database tables created/verified
2025-07-29 16:12:02,945 - labap1py.labap1py - INFO - initialize_application:105 - Database initialized
2025-07-29 16:12:02,972 - alembic.runtime.migration - INFO - __init__:207 - Context impl SQLiteImpl.
2025-07-29 16:12:02,973 - alembic.runtime.migration - INFO - __init__:210 - Will assume non-transactional DDL.
2025-07-29 16:12:02,985 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'auditlog'
2025-07-29 16:12:02,986 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'patient'
2025-07-29 16:12:02,987 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_created_at'' on '('created_at',)'
2025-07-29 16:12:02,988 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_dob'' on '('dob',)'
2025-07-29 16:12:02,989 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'user'
2025-07-29 16:12:02,990 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_user_username'' on '('username',)'
2025-07-29 16:12:02,991 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'presentillness'
2025-07-29 16:12:02,992 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_presentillness_assessment_date'' on '('assessment_date',)'
2025-07-29 16:12:02,993 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'userpatientaccess'
2025-07-29 16:12:02,995 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'usersession'
2025-07-29 16:12:02,996 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_session_token'' on '('session_token',)'
2025-07-29 16:12:02,996 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_user_id'' on '('user_id',)'
2025-07-29 16:13:59,253 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 16:13:59,392 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 16:13:59,393 - labap1py.labap1py - INFO - initialize_application:101 - Encryption service initialized
2025-07-29 16:13:59,449 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 16:13:59,453 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 16:13:59,457 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 16:13:59,460 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:13:59,466 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 16:13:59,468 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:13:59,471 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 16:13:59,474 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:13:59,477 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 16:13:59,482 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:13:59,485 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 16:13:59,487 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:13:59,490 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 16:13:59,491 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:13:59,494 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 16:13:59,495 - labap1py.labap1py - INFO - initialize_database:58 - Database tables created/verified
2025-07-29 16:13:59,498 - labap1py.labap1py - INFO - initialize_application:105 - Database initialized
2025-07-29 16:13:59,538 - alembic.runtime.migration - INFO - __init__:207 - Context impl SQLiteImpl.
2025-07-29 16:13:59,539 - alembic.runtime.migration - INFO - __init__:210 - Will assume non-transactional DDL.
2025-07-29 16:13:59,549 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'auditlog'
2025-07-29 16:13:59,551 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'patient'
2025-07-29 16:13:59,552 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_created_at'' on '('created_at',)'
2025-07-29 16:13:59,553 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_dob'' on '('dob',)'
2025-07-29 16:13:59,554 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'user'
2025-07-29 16:13:59,555 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_user_username'' on '('username',)'
2025-07-29 16:13:59,556 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'presentillness'
2025-07-29 16:13:59,557 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_presentillness_assessment_date'' on '('assessment_date',)'
2025-07-29 16:13:59,558 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'userpatientaccess'
2025-07-29 16:13:59,559 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'usersession'
2025-07-29 16:13:59,560 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_session_token'' on '('session_token',)'
2025-07-29 16:13:59,561 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_user_id'' on '('user_id',)'
2025-07-29 16:18:28,373 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 16:18:28,670 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 16:18:28,684 - labap1py.labap1py - INFO - initialize_application:101 - Encryption service initialized
2025-07-29 16:18:28,767 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 16:18:28,773 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 16:18:28,780 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 16:18:28,789 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:18:28,801 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 16:18:28,808 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:18:28,816 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 16:18:28,818 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:18:28,822 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 16:18:28,831 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:18:28,835 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 16:18:28,840 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:18:28,844 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 16:18:28,850 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:18:28,856 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 16:18:28,867 - labap1py.labap1py - INFO - initialize_database:58 - Database tables created/verified
2025-07-29 16:18:28,869 - labap1py.labap1py - INFO - initialize_application:105 - Database initialized
2025-07-29 16:18:28,942 - alembic.runtime.migration - INFO - __init__:207 - Context impl SQLiteImpl.
2025-07-29 16:18:28,955 - alembic.runtime.migration - INFO - __init__:210 - Will assume non-transactional DDL.
2025-07-29 16:18:28,973 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'auditlog'
2025-07-29 16:18:28,998 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'patient'
2025-07-29 16:18:29,015 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_created_at'' on '('created_at',)'
2025-07-29 16:18:29,018 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_dob'' on '('dob',)'
2025-07-29 16:18:29,019 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'user'
2025-07-29 16:18:29,021 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_user_username'' on '('username',)'
2025-07-29 16:18:29,022 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'presentillness'
2025-07-29 16:18:29,024 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_presentillness_assessment_date'' on '('assessment_date',)'
2025-07-29 16:18:29,032 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'userpatientaccess'
2025-07-29 16:18:29,034 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'usersession'
2025-07-29 16:18:29,037 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_session_token'' on '('session_token',)'
2025-07-29 16:18:29,040 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_user_id'' on '('user_id',)'
2025-07-29 16:23:06,000 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 16:23:07,493 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 16:23:07,496 - labap1py.labap1py - INFO - initialize_application:101 - Encryption service initialized
2025-07-29 16:23:07,862 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 16:23:07,946 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 16:23:07,977 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 16:23:07,981 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:23:08,023 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 16:23:08,035 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:23:08,040 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 16:23:08,044 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:23:08,056 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 16:23:08,062 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:23:08,065 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 16:23:08,071 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:23:08,079 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 16:23:08,081 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:23:08,087 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 16:23:08,089 - labap1py.labap1py - INFO - initialize_database:58 - Database tables created/verified
2025-07-29 16:23:08,090 - labap1py.labap1py - INFO - initialize_application:105 - Database initialized
2025-07-29 16:23:08,213 - alembic.runtime.migration - INFO - __init__:207 - Context impl SQLiteImpl.
2025-07-29 16:23:08,221 - alembic.runtime.migration - INFO - __init__:210 - Will assume non-transactional DDL.
2025-07-29 16:23:08,248 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'auditlog'
2025-07-29 16:23:08,251 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'patient'
2025-07-29 16:23:08,255 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_created_at'' on '('created_at',)'
2025-07-29 16:23:08,258 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_dob'' on '('dob',)'
2025-07-29 16:23:08,261 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'user'
2025-07-29 16:23:08,269 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_user_username'' on '('username',)'
2025-07-29 16:23:08,272 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'presentillness'
2025-07-29 16:23:08,276 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_presentillness_assessment_date'' on '('assessment_date',)'
2025-07-29 16:23:08,280 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'userpatientaccess'
2025-07-29 16:23:08,287 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'usersession'
2025-07-29 16:23:08,291 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_session_token'' on '('session_token',)'
2025-07-29 16:23:08,294 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_user_id'' on '('user_id',)'
2025-07-29 16:26:15,367 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 16:26:15,525 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 16:26:15,526 - labap1py.labap1py - INFO - initialize_application:101 - Encryption service initialized
2025-07-29 16:26:15,578 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 16:26:15,582 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 16:26:15,584 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 16:26:15,588 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:26:15,592 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 16:26:15,594 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:26:15,596 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 16:26:15,598 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:26:15,600 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 16:26:15,602 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:26:15,606 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 16:26:15,608 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:26:15,611 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 16:26:15,613 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:26:15,616 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 16:26:15,618 - labap1py.labap1py - INFO - initialize_database:58 - Database tables created/verified
2025-07-29 16:26:15,621 - labap1py.labap1py - INFO - initialize_application:105 - Database initialized
2025-07-29 16:26:15,662 - alembic.runtime.migration - INFO - __init__:207 - Context impl SQLiteImpl.
2025-07-29 16:26:15,663 - alembic.runtime.migration - INFO - __init__:210 - Will assume non-transactional DDL.
2025-07-29 16:26:15,674 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'auditlog'
2025-07-29 16:26:15,681 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'patient'
2025-07-29 16:26:15,685 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_created_at'' on '('created_at',)'
2025-07-29 16:26:15,692 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_dob'' on '('dob',)'
2025-07-29 16:26:15,697 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'user'
2025-07-29 16:26:15,702 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_user_username'' on '('username',)'
2025-07-29 16:26:15,706 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'presentillness'
2025-07-29 16:26:15,708 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_presentillness_assessment_date'' on '('assessment_date',)'
2025-07-29 16:26:15,711 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'userpatientaccess'
2025-07-29 16:26:15,716 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'usersession'
2025-07-29 16:26:15,719 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_session_token'' on '('session_token',)'
2025-07-29 16:26:15,722 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_user_id'' on '('user_id',)'
2025-07-29 16:33:22,954 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 16:33:23,124 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 16:33:23,125 - labap1py.labap1py - INFO - initialize_application:101 - Encryption service initialized
2025-07-29 16:33:23,250 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 16:33:23,256 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 16:33:23,259 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 16:33:23,261 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:33:23,267 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 16:33:23,273 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:33:23,279 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 16:33:23,288 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:33:23,295 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 16:33:23,296 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:33:23,309 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 16:33:23,313 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:33:23,328 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 16:33:23,332 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:33:23,337 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 16:33:23,345 - labap1py.labap1py - INFO - initialize_database:58 - Database tables created/verified
2025-07-29 16:33:23,360 - labap1py.labap1py - INFO - initialize_application:105 - Database initialized
2025-07-29 16:33:23,510 - alembic.runtime.migration - INFO - __init__:207 - Context impl SQLiteImpl.
2025-07-29 16:33:23,515 - alembic.runtime.migration - INFO - __init__:210 - Will assume non-transactional DDL.
2025-07-29 16:33:23,542 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'auditlog'
2025-07-29 16:33:23,552 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'patient'
2025-07-29 16:33:23,562 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_created_at'' on '('created_at',)'
2025-07-29 16:33:23,563 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_dob'' on '('dob',)'
2025-07-29 16:33:23,576 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'user'
2025-07-29 16:33:23,585 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_user_username'' on '('username',)'
2025-07-29 16:33:23,593 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'presentillness'
2025-07-29 16:33:23,595 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_presentillness_assessment_date'' on '('assessment_date',)'
2025-07-29 16:33:23,608 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'userpatientaccess'
2025-07-29 16:33:23,634 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'usersession'
2025-07-29 16:33:23,644 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_session_token'' on '('session_token',)'
2025-07-29 16:33:23,650 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_user_id'' on '('user_id',)'
2025-07-29 16:35:31,235 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 16:35:31,463 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 16:35:31,464 - labap1py.labap1py - INFO - initialize_application:101 - Encryption service initialized
2025-07-29 16:35:31,536 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 16:35:31,538 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 16:35:31,542 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 16:35:31,546 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:35:31,552 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 16:35:31,554 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:35:31,556 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 16:35:31,564 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:35:31,567 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 16:35:31,581 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:35:31,584 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 16:35:31,588 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:35:31,599 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 16:35:31,603 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:35:31,617 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 16:35:31,633 - labap1py.labap1py - INFO - initialize_database:58 - Database tables created/verified
2025-07-29 16:35:31,636 - labap1py.labap1py - INFO - initialize_application:105 - Database initialized
2025-07-29 16:35:31,746 - alembic.runtime.migration - INFO - __init__:207 - Context impl SQLiteImpl.
2025-07-29 16:35:31,751 - alembic.runtime.migration - INFO - __init__:210 - Will assume non-transactional DDL.
2025-07-29 16:35:31,770 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'auditlog'
2025-07-29 16:35:31,772 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'patient'
2025-07-29 16:35:31,773 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_created_at'' on '('created_at',)'
2025-07-29 16:35:31,778 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_dob'' on '('dob',)'
2025-07-29 16:35:31,781 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'user'
2025-07-29 16:35:31,784 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_user_username'' on '('username',)'
2025-07-29 16:35:31,786 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'presentillness'
2025-07-29 16:35:31,788 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_presentillness_assessment_date'' on '('assessment_date',)'
2025-07-29 16:35:31,789 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'userpatientaccess'
2025-07-29 16:35:31,798 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'usersession'
2025-07-29 16:35:31,800 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_session_token'' on '('session_token',)'
2025-07-29 16:35:31,802 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_user_id'' on '('user_id',)'
2025-07-29 16:38:18,537 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 16:38:18,787 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 16:38:18,789 - labap1py.labap1py - INFO - initialize_application:101 - Encryption service initialized
2025-07-29 16:38:18,860 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 16:38:18,862 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 16:38:18,864 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 16:38:18,865 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:38:18,879 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 16:38:18,880 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:38:18,885 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 16:38:18,887 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:38:18,889 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 16:38:18,890 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:38:18,895 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 16:38:18,903 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:38:18,907 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 16:38:18,909 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:38:18,914 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 16:38:18,928 - labap1py.labap1py - INFO - initialize_database:58 - Database tables created/verified
2025-07-29 16:38:18,930 - labap1py.labap1py - INFO - initialize_application:105 - Database initialized
2025-07-29 16:38:19,013 - alembic.runtime.migration - INFO - __init__:207 - Context impl SQLiteImpl.
2025-07-29 16:38:19,014 - alembic.runtime.migration - INFO - __init__:210 - Will assume non-transactional DDL.
2025-07-29 16:38:19,028 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'auditlog'
2025-07-29 16:38:19,029 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'patient'
2025-07-29 16:38:19,030 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_created_at'' on '('created_at',)'
2025-07-29 16:38:19,031 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_dob'' on '('dob',)'
2025-07-29 16:38:19,034 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'user'
2025-07-29 16:38:19,040 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_user_username'' on '('username',)'
2025-07-29 16:38:19,043 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'presentillness'
2025-07-29 16:38:19,045 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_presentillness_assessment_date'' on '('assessment_date',)'
2025-07-29 16:38:19,047 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'userpatientaccess'
2025-07-29 16:38:19,048 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'usersession'
2025-07-29 16:38:19,061 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_session_token'' on '('session_token',)'
2025-07-29 16:38:19,061 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_user_id'' on '('user_id',)'
2025-07-29 16:40:33,885 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 16:40:34,081 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 16:40:34,081 - labap1py.labap1py - INFO - initialize_application:101 - Encryption service initialized
2025-07-29 16:40:34,158 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 16:40:34,170 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 16:40:34,188 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 16:40:34,191 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:40:34,195 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 16:40:34,199 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:40:34,216 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 16:40:34,230 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:40:34,234 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 16:40:34,236 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:40:34,288 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 16:40:34,305 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:40:34,313 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 16:40:34,319 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:40:34,327 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 16:40:34,331 - labap1py.labap1py - INFO - initialize_database:58 - Database tables created/verified
2025-07-29 16:40:34,334 - labap1py.labap1py - INFO - initialize_application:105 - Database initialized
2025-07-29 16:40:34,411 - alembic.runtime.migration - INFO - __init__:207 - Context impl SQLiteImpl.
2025-07-29 16:40:34,412 - alembic.runtime.migration - INFO - __init__:210 - Will assume non-transactional DDL.
2025-07-29 16:40:34,431 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'auditlog'
2025-07-29 16:40:34,436 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'patient'
2025-07-29 16:40:34,443 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_created_at'' on '('created_at',)'
2025-07-29 16:40:34,447 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_dob'' on '('dob',)'
2025-07-29 16:40:34,450 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'user'
2025-07-29 16:40:34,453 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_user_username'' on '('username',)'
2025-07-29 16:40:34,461 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'presentillness'
2025-07-29 16:40:34,463 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_presentillness_assessment_date'' on '('assessment_date',)'
2025-07-29 16:40:34,465 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'userpatientaccess'
2025-07-29 16:40:34,467 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'usersession'
2025-07-29 16:40:34,471 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_session_token'' on '('session_token',)'
2025-07-29 16:40:34,479 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_user_id'' on '('user_id',)'
2025-07-29 16:45:41,629 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 16:45:41,841 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 16:45:41,844 - labap1py.labap1py - INFO - initialize_application:101 - Encryption service initialized
2025-07-29 16:45:41,908 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 16:45:41,916 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 16:45:41,917 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 16:45:41,920 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:45:41,939 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 16:45:41,956 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:45:41,962 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 16:45:41,975 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:45:41,983 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 16:45:41,988 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:45:41,990 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 16:45:41,992 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:45:42,006 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 16:45:42,021 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:45:42,027 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 16:45:42,039 - labap1py.labap1py - INFO - initialize_database:58 - Database tables created/verified
2025-07-29 16:45:42,040 - labap1py.labap1py - INFO - initialize_application:105 - Database initialized
2025-07-29 16:45:42,107 - alembic.runtime.migration - INFO - __init__:207 - Context impl SQLiteImpl.
2025-07-29 16:45:42,108 - alembic.runtime.migration - INFO - __init__:210 - Will assume non-transactional DDL.
2025-07-29 16:45:42,118 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'auditlog'
2025-07-29 16:45:42,121 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'patient'
2025-07-29 16:45:42,122 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_created_at'' on '('created_at',)'
2025-07-29 16:45:42,123 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_dob'' on '('dob',)'
2025-07-29 16:45:42,124 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'user'
2025-07-29 16:45:42,125 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_user_username'' on '('username',)'
2025-07-29 16:45:42,125 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'presentillness'
2025-07-29 16:45:42,129 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_presentillness_assessment_date'' on '('assessment_date',)'
2025-07-29 16:45:42,131 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'userpatientaccess'
2025-07-29 16:45:42,133 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'usersession'
2025-07-29 16:45:42,137 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_session_token'' on '('session_token',)'
2025-07-29 16:45:42,139 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_user_id'' on '('user_id',)'
2025-07-29 16:48:30,624 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 16:48:30,750 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 16:48:30,751 - labap1py.labap1py - INFO - initialize_application:101 - Encryption service initialized
2025-07-29 16:48:30,792 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 16:48:30,801 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 16:48:30,805 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 16:48:30,806 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:48:30,808 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 16:48:30,809 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:48:30,810 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 16:48:30,816 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:48:30,820 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 16:48:30,822 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:48:30,824 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 16:48:30,825 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:48:30,827 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 16:48:30,837 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:48:30,838 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 16:48:30,840 - labap1py.labap1py - INFO - initialize_database:58 - Database tables created/verified
2025-07-29 16:48:30,841 - labap1py.labap1py - INFO - initialize_application:105 - Database initialized
2025-07-29 16:48:31,053 - alembic.runtime.migration - INFO - __init__:207 - Context impl SQLiteImpl.
2025-07-29 16:48:31,158 - alembic.runtime.migration - INFO - __init__:210 - Will assume non-transactional DDL.
2025-07-29 16:48:31,224 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'auditlog'
2025-07-29 16:48:31,266 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'patient'
2025-07-29 16:48:31,340 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_created_at'' on '('created_at',)'
2025-07-29 16:48:31,354 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_dob'' on '('dob',)'
2025-07-29 16:48:31,356 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'user'
2025-07-29 16:48:31,358 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_user_username'' on '('username',)'
2025-07-29 16:48:31,364 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'presentillness'
2025-07-29 16:48:31,373 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_presentillness_assessment_date'' on '('assessment_date',)'
2025-07-29 16:48:31,431 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'userpatientaccess'
2025-07-29 16:48:31,443 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'usersession'
2025-07-29 16:48:31,456 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_session_token'' on '('session_token',)'
2025-07-29 16:48:31,458 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_user_id'' on '('user_id',)'
2025-07-29 16:49:53,396 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 16:49:53,594 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 16:49:53,596 - labap1py.labap1py - INFO - initialize_application:101 - Encryption service initialized
2025-07-29 16:49:53,721 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 16:49:53,723 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 16:49:53,726 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 16:49:53,732 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:49:53,736 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 16:49:53,738 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:49:53,739 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 16:49:53,742 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:49:53,747 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 16:49:53,753 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:49:53,754 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 16:49:53,755 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:49:53,756 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 16:49:53,760 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:49:53,764 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 16:49:53,769 - labap1py.labap1py - INFO - initialize_database:58 - Database tables created/verified
2025-07-29 16:49:53,770 - labap1py.labap1py - INFO - initialize_application:105 - Database initialized
2025-07-29 16:53:32,966 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 16:53:33,420 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 16:53:33,422 - labap1py.labap1py - INFO - initialize_application:101 - Encryption service initialized
2025-07-29 16:53:33,500 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 16:53:33,503 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 16:53:33,506 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 16:53:33,513 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:53:33,520 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 16:53:33,529 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:53:33,532 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 16:53:33,534 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:53:33,539 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 16:53:33,545 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:53:33,547 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 16:53:33,548 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:53:33,550 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 16:53:33,555 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:53:33,563 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 16:53:33,564 - labap1py.labap1py - INFO - initialize_database:58 - Database tables created/verified
2025-07-29 16:53:33,565 - labap1py.labap1py - INFO - initialize_application:105 - Database initialized
2025-07-29 16:53:33,639 - alembic.runtime.migration - INFO - __init__:207 - Context impl SQLiteImpl.
2025-07-29 16:53:33,640 - alembic.runtime.migration - INFO - __init__:210 - Will assume non-transactional DDL.
2025-07-29 16:53:33,649 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'auditlog'
2025-07-29 16:53:33,652 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'patient'
2025-07-29 16:53:33,658 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_created_at'' on '('created_at',)'
2025-07-29 16:53:33,658 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_dob'' on '('dob',)'
2025-07-29 16:53:33,661 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'user'
2025-07-29 16:53:33,662 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_user_username'' on '('username',)'
2025-07-29 16:53:33,664 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'presentillness'
2025-07-29 16:53:33,665 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_presentillness_assessment_date'' on '('assessment_date',)'
2025-07-29 16:53:33,668 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'userpatientaccess'
2025-07-29 16:53:33,679 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'usersession'
2025-07-29 16:53:33,681 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_session_token'' on '('session_token',)'
2025-07-29 16:53:33,682 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_user_id'' on '('user_id',)'
2025-07-29 16:54:02,094 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 16:54:02,219 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 16:54:02,220 - labap1py.labap1py - INFO - initialize_application:101 - Encryption service initialized
2025-07-29 16:54:02,275 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 16:54:02,277 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 16:54:02,278 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 16:54:02,279 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:54:02,287 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 16:54:02,289 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:54:02,290 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 16:54:02,291 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:54:02,292 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 16:54:02,293 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:54:02,295 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 16:54:02,296 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:54:02,300 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 16:54:02,306 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:54:02,308 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 16:54:02,309 - labap1py.labap1py - INFO - initialize_database:58 - Database tables created/verified
2025-07-29 16:54:02,310 - labap1py.labap1py - INFO - initialize_application:105 - Database initialized
2025-07-29 16:59:26,163 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 16:59:26,284 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 16:59:26,284 - labap1py.labap1py - INFO - initialize_application:101 - Encryption service initialized
2025-07-29 16:59:26,328 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 16:59:26,330 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 16:59:26,331 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 16:59:26,332 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:59:26,335 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 16:59:26,336 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:59:26,342 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 16:59:26,345 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:59:26,347 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 16:59:26,349 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:59:26,351 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 16:59:26,352 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:59:26,355 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 16:59:26,360 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 16:59:26,363 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 16:59:26,366 - labap1py.labap1py - INFO - initialize_database:58 - Database tables created/verified
2025-07-29 16:59:26,367 - labap1py.labap1py - INFO - initialize_application:105 - Database initialized
2025-07-29 16:59:26,400 - alembic.runtime.migration - INFO - __init__:207 - Context impl SQLiteImpl.
2025-07-29 16:59:26,400 - alembic.runtime.migration - INFO - __init__:210 - Will assume non-transactional DDL.
2025-07-29 16:59:26,418 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'auditlog'
2025-07-29 16:59:26,419 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'patient'
2025-07-29 16:59:26,429 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_created_at'' on '('created_at',)'
2025-07-29 16:59:26,434 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_dob'' on '('dob',)'
2025-07-29 16:59:26,435 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'user'
2025-07-29 16:59:26,436 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_user_username'' on '('username',)'
2025-07-29 16:59:26,441 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'presentillness'
2025-07-29 16:59:26,446 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_presentillness_assessment_date'' on '('assessment_date',)'
2025-07-29 16:59:26,448 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'userpatientaccess'
2025-07-29 16:59:26,450 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'usersession'
2025-07-29 16:59:26,451 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_session_token'' on '('session_token',)'
2025-07-29 16:59:26,452 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_user_id'' on '('user_id',)'
2025-07-29 17:04:02,121 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 17:04:02,135 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 17:04:03,425 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 17:04:03,438 - labap1py.labap1py - INFO - initialize_application:101 - Encryption service initialized
2025-07-29 17:04:04,632 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 17:04:04,652 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 17:04:04,657 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 17:04:04,672 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:04:04,686 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 17:04:04,689 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:04:04,693 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 17:04:04,704 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:04:04,706 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 17:04:04,719 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:04:04,721 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 17:04:04,722 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:04:04,724 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 17:04:04,727 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:04:04,735 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 17:04:04,736 - labap1py.labap1py - INFO - initialize_database:58 - Database tables created/verified
2025-07-29 17:04:04,737 - labap1py.labap1py - INFO - initialize_application:105 - Database initialized
2025-07-29 17:17:31,427 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 17:17:31,554 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 17:17:31,555 - labap1py.labap1py - INFO - initialize_application:101 - Encryption service initialized
2025-07-29 17:17:31,888 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 17:17:31,895 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 17:17:31,899 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 17:17:31,903 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:17:31,916 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 17:17:31,921 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:17:31,926 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 17:17:31,928 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:17:31,930 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 17:17:31,932 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:17:31,934 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 17:17:31,940 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:17:31,943 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 17:17:31,944 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:17:31,945 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 17:17:31,946 - labap1py.labap1py - INFO - initialize_database:58 - Database tables created/verified
2025-07-29 17:17:31,947 - labap1py.labap1py - INFO - initialize_application:105 - Database initialized
2025-07-29 17:17:32,050 - alembic.runtime.migration - INFO - __init__:207 - Context impl SQLiteImpl.
2025-07-29 17:17:32,050 - alembic.runtime.migration - INFO - __init__:210 - Will assume non-transactional DDL.
2025-07-29 17:17:32,253 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'auditlog'
2025-07-29 17:17:32,256 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'patient'
2025-07-29 17:17:32,258 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_created_at'' on '('created_at',)'
2025-07-29 17:17:32,259 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_dob'' on '('dob',)'
2025-07-29 17:17:32,261 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'user'
2025-07-29 17:17:32,262 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_user_username'' on '('username',)'
2025-07-29 17:17:32,264 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'presentillness'
2025-07-29 17:17:32,267 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_presentillness_assessment_date'' on '('assessment_date',)'
2025-07-29 17:17:32,273 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'userpatientaccess'
2025-07-29 17:17:32,276 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'usersession'
2025-07-29 17:17:32,286 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_session_token'' on '('session_token',)'
2025-07-29 17:17:32,293 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_user_id'' on '('user_id',)'
2025-07-29 17:18:03,736 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 17:18:03,859 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 17:18:03,860 - labap1py.labap1py - INFO - initialize_application:101 - Encryption service initialized
2025-07-29 17:18:03,884 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 17:18:03,885 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 17:18:03,886 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 17:18:03,887 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:18:03,889 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 17:18:03,890 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:18:03,893 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 17:18:03,894 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:18:03,895 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 17:18:03,898 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:18:03,900 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 17:18:03,901 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:18:03,904 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 17:18:03,905 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:18:03,906 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 17:18:03,908 - labap1py.labap1py - INFO - initialize_database:58 - Database tables created/verified
2025-07-29 17:18:03,909 - labap1py.labap1py - INFO - initialize_application:105 - Database initialized
2025-07-29 17:20:06,578 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 17:20:06,708 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 17:20:06,709 - labap1py.labap1py - INFO - initialize_application:101 - Encryption service initialized
2025-07-29 17:20:06,737 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 17:20:06,740 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 17:20:06,741 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 17:20:06,742 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:20:06,744 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 17:20:06,746 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:20:06,747 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 17:20:06,748 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:20:06,749 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 17:20:06,751 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:20:06,755 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 17:20:06,756 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:20:06,757 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 17:20:06,758 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:20:06,760 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 17:20:06,762 - labap1py.labap1py - INFO - initialize_database:58 - Database tables created/verified
2025-07-29 17:20:06,762 - labap1py.labap1py - INFO - initialize_application:105 - Database initialized
2025-07-29 17:20:52,552 - httpx - INFO - _send_single_request:1025 - HTTP Request: POST https://app.posthog.com/capture/ "HTTP/1.1 200 OK"
2025-07-29 17:21:44,019 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 17:21:44,157 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 17:21:44,157 - labap1py.labap1py - INFO - initialize_application:101 - Encryption service initialized
2025-07-29 17:21:44,181 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 17:21:44,183 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 17:21:44,184 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 17:21:44,186 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:21:44,189 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 17:21:44,190 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:21:44,192 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 17:21:44,193 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:21:44,195 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 17:21:44,196 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:21:44,198 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 17:21:44,199 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:21:44,200 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 17:21:44,201 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:21:44,203 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 17:21:44,204 - labap1py.labap1py - INFO - initialize_database:58 - Database tables created/verified
2025-07-29 17:21:44,204 - labap1py.labap1py - INFO - initialize_application:105 - Database initialized
2025-07-29 17:21:44,233 - alembic.runtime.migration - INFO - __init__:207 - Context impl SQLiteImpl.
2025-07-29 17:21:44,233 - alembic.runtime.migration - INFO - __init__:210 - Will assume non-transactional DDL.
2025-07-29 17:21:44,241 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'auditlog'
2025-07-29 17:21:44,242 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'patient'
2025-07-29 17:21:44,245 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_created_at'' on '('created_at',)'
2025-07-29 17:21:44,246 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_dob'' on '('dob',)'
2025-07-29 17:21:44,247 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'user'
2025-07-29 17:21:44,247 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_user_username'' on '('username',)'
2025-07-29 17:21:44,248 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'presentillness'
2025-07-29 17:21:44,249 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_presentillness_assessment_date'' on '('assessment_date',)'
2025-07-29 17:21:44,250 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'userpatientaccess'
2025-07-29 17:21:44,251 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'usersession'
2025-07-29 17:21:44,252 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_session_token'' on '('session_token',)'
2025-07-29 17:21:44,253 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_user_id'' on '('user_id',)'
2025-07-29 17:21:57,956 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 17:21:58,117 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 17:21:58,118 - labap1py.labap1py - INFO - initialize_application:101 - Encryption service initialized
2025-07-29 17:21:58,146 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 17:21:58,148 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 17:21:58,149 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 17:21:58,150 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:21:58,153 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 17:21:58,155 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:21:58,157 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 17:21:58,158 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:21:58,160 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 17:21:58,162 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:21:58,163 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 17:21:58,164 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:21:58,166 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 17:21:58,167 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:21:58,169 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 17:21:58,170 - labap1py.labap1py - INFO - initialize_database:58 - Database tables created/verified
2025-07-29 17:21:58,171 - labap1py.labap1py - INFO - initialize_application:105 - Database initialized
2025-07-29 17:26:12,740 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 17:26:12,895 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 17:26:12,897 - labap1py.labap1py - INFO - initialize_application:101 - Encryption service initialized
2025-07-29 17:26:12,944 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 17:26:12,946 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 17:26:12,947 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 17:26:12,947 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:26:12,953 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 17:26:12,959 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:26:12,962 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 17:26:12,962 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:26:12,963 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 17:26:12,966 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:26:12,969 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 17:26:12,972 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:26:12,974 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 17:26:12,975 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:26:12,977 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 17:26:12,978 - labap1py.labap1py - INFO - initialize_database:58 - Database tables created/verified
2025-07-29 17:26:12,979 - labap1py.labap1py - INFO - initialize_application:105 - Database initialized
2025-07-29 17:26:13,020 - alembic.runtime.migration - INFO - __init__:207 - Context impl SQLiteImpl.
2025-07-29 17:26:13,022 - alembic.runtime.migration - INFO - __init__:210 - Will assume non-transactional DDL.
2025-07-29 17:26:13,079 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'auditlog'
2025-07-29 17:26:13,114 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'patient'
2025-07-29 17:26:13,160 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_created_at'' on '('created_at',)'
2025-07-29 17:26:13,189 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_dob'' on '('dob',)'
2025-07-29 17:26:13,265 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'user'
2025-07-29 17:26:13,314 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_user_username'' on '('username',)'
2025-07-29 17:26:13,338 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'presentillness'
2025-07-29 17:26:13,376 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_presentillness_assessment_date'' on '('assessment_date',)'
2025-07-29 17:26:13,416 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'userpatientaccess'
2025-07-29 17:26:13,417 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'usersession'
2025-07-29 17:26:13,418 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_session_token'' on '('session_token',)'
2025-07-29 17:26:13,419 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_user_id'' on '('user_id',)'
2025-07-29 17:26:32,159 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 17:26:32,326 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 17:26:32,327 - labap1py.labap1py - INFO - initialize_application:101 - Encryption service initialized
2025-07-29 17:26:32,374 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 17:26:32,377 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 17:26:32,379 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 17:26:32,379 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:26:32,385 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 17:26:32,390 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:26:32,392 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 17:26:32,397 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:26:32,401 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 17:26:32,408 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:26:32,412 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 17:26:32,413 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:26:32,414 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 17:26:32,426 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:26:32,428 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 17:26:32,430 - labap1py.labap1py - INFO - initialize_database:58 - Database tables created/verified
2025-07-29 17:26:32,431 - labap1py.labap1py - INFO - initialize_application:105 - Database initialized
2025-07-29 17:28:24,211 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 17:28:24,373 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 17:28:24,373 - labap1py.labap1py - INFO - initialize_application:101 - Encryption service initialized
2025-07-29 17:28:24,408 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 17:28:24,411 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 17:28:24,412 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 17:28:24,412 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:28:24,415 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 17:28:24,417 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:28:24,419 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 17:28:24,420 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:28:24,421 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 17:28:24,422 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:28:24,424 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 17:28:24,425 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:28:24,426 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 17:28:24,427 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:28:24,429 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 17:28:24,430 - labap1py.labap1py - INFO - initialize_database:58 - Database tables created/verified
2025-07-29 17:28:24,430 - labap1py.labap1py - INFO - initialize_application:105 - Database initialized
2025-07-29 17:29:50,920 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 17:29:51,131 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 17:29:51,135 - labap1py.labap1py - INFO - initialize_application:101 - Encryption service initialized
2025-07-29 17:29:51,183 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 17:29:51,185 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 17:29:51,186 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 17:29:51,188 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:29:51,193 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 17:29:51,198 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:29:51,200 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 17:29:51,201 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:29:51,202 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 17:29:51,203 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:29:51,205 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 17:29:51,206 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:29:51,207 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 17:29:51,209 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:29:51,213 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 17:29:51,216 - labap1py.labap1py - INFO - initialize_database:58 - Database tables created/verified
2025-07-29 17:29:51,217 - labap1py.labap1py - INFO - initialize_application:105 - Database initialized
2025-07-29 17:31:29,711 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 17:31:29,924 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 17:31:29,931 - labap1py.labap1py - INFO - initialize_application:101 - Encryption service initialized
2025-07-29 17:31:30,014 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 17:31:30,019 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 17:31:30,035 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 17:31:30,045 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:31:30,053 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 17:31:30,066 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:31:30,077 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 17:31:30,093 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:31:30,109 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 17:31:30,114 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:31:30,133 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 17:31:30,148 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:31:30,163 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 17:31:30,173 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:31:30,180 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 17:31:30,193 - labap1py.labap1py - INFO - initialize_database:58 - Database tables created/verified
2025-07-29 17:31:30,195 - labap1py.labap1py - INFO - initialize_application:105 - Database initialized
2025-07-29 17:31:30,265 - alembic.runtime.migration - INFO - __init__:207 - Context impl SQLiteImpl.
2025-07-29 17:31:30,274 - alembic.runtime.migration - INFO - __init__:210 - Will assume non-transactional DDL.
2025-07-29 17:31:30,292 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'auditlog'
2025-07-29 17:31:30,297 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'patient'
2025-07-29 17:31:30,303 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_created_at'' on '('created_at',)'
2025-07-29 17:31:30,306 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_dob'' on '('dob',)'
2025-07-29 17:31:30,309 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'user'
2025-07-29 17:31:30,314 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_user_username'' on '('username',)'
2025-07-29 17:31:30,315 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'presentillness'
2025-07-29 17:31:30,315 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_presentillness_assessment_date'' on '('assessment_date',)'
2025-07-29 17:31:30,319 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'userpatientaccess'
2025-07-29 17:31:30,321 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'usersession'
2025-07-29 17:31:30,323 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_session_token'' on '('session_token',)'
2025-07-29 17:31:30,328 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_user_id'' on '('user_id',)'
2025-07-29 17:31:49,119 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 17:31:49,233 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 17:31:49,233 - labap1py.labap1py - INFO - initialize_application:101 - Encryption service initialized
2025-07-29 17:31:49,263 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 17:31:49,269 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 17:31:49,270 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 17:31:49,270 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:31:49,273 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 17:31:49,274 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:31:49,277 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 17:31:49,277 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:31:49,279 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 17:31:49,280 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:31:49,281 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 17:31:49,285 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:31:49,288 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 17:31:49,293 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:31:49,294 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 17:31:49,295 - labap1py.labap1py - INFO - initialize_database:58 - Database tables created/verified
2025-07-29 17:31:49,296 - labap1py.labap1py - INFO - initialize_application:105 - Database initialized
2025-07-29 17:35:25,003 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 17:35:25,155 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 17:35:25,156 - labap1py.labap1py - INFO - initialize_application:101 - Encryption service initialized
2025-07-29 17:35:25,209 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 17:35:25,217 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 17:35:25,223 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 17:35:25,223 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:35:25,226 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 17:35:25,228 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:35:25,232 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 17:35:25,240 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:35:25,242 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 17:35:25,243 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:35:25,245 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 17:35:25,253 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:35:25,256 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 17:35:25,258 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:35:25,260 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 17:35:25,261 - labap1py.labap1py - INFO - initialize_database:58 - Database tables created/verified
2025-07-29 17:35:25,262 - labap1py.labap1py - INFO - initialize_application:105 - Database initialized
2025-07-29 17:35:25,327 - alembic.runtime.migration - INFO - __init__:207 - Context impl SQLiteImpl.
2025-07-29 17:35:25,327 - alembic.runtime.migration - INFO - __init__:210 - Will assume non-transactional DDL.
2025-07-29 17:35:25,342 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'auditlog'
2025-07-29 17:35:25,343 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'patient'
2025-07-29 17:35:25,344 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_created_at'' on '('created_at',)'
2025-07-29 17:35:25,345 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_patient_dob'' on '('dob',)'
2025-07-29 17:35:25,355 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'user'
2025-07-29 17:35:25,356 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_user_username'' on '('username',)'
2025-07-29 17:35:25,358 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'presentillness'
2025-07-29 17:35:25,359 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_presentillness_assessment_date'' on '('assessment_date',)'
2025-07-29 17:35:25,360 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'userpatientaccess'
2025-07-29 17:35:25,361 - alembic.autogenerate.compare - INFO - _compare_tables:190 - Detected added table 'usersession'
2025-07-29 17:35:25,361 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_session_token'' on '('session_token',)'
2025-07-29 17:35:25,370 - alembic.autogenerate.compare - INFO - obj_added:685 - Detected added index ''ix_usersession_user_id'' on '('user_id',)'
2025-07-29 17:35:43,467 - labap1py.labap1py - INFO - initialize_application:95 - Initializing Psychiatry EMR application...
2025-07-29 17:35:43,593 - security.encryption - INFO - initialize_encryption:86 - Encryption service initialized
2025-07-29 17:35:43,594 - labap1py.labap1py - INFO - initialize_application:101 - Encryption service initialized
2025-07-29 17:35:43,630 - services.database - INFO - get_engine:33 - Database engine initialized
2025-07-29 17:35:43,632 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 17:35:43,633 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("user")
2025-07-29 17:35:43,634 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:35:43,635 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("usersession")
2025-07-29 17:35:43,636 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:35:43,644 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("auditlog")
2025-07-29 17:35:43,646 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:35:43,647 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("patient")
2025-07-29 17:35:43,649 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:35:43,650 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("userpatientaccess")
2025-07-29 17:35:43,651 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:35:43,653 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - PRAGMA main.table_info("presentillness")
2025-07-29 17:35:43,656 - sqlalchemy.engine.Engine - INFO - _execute_context:1842 - [raw sql] ()
2025-07-29 17:35:43,663 - sqlalchemy.engine.Engine - INFO - _connection_commit_impl:2704 - COMMIT
2025-07-29 17:35:43,664 - labap1py.labap1py - INFO - initialize_database:58 - Database tables created/verified
2025-07-29 17:35:43,664 - labap1py.labap1py - INFO - initialize_application:105 - Database initialized
2025-07-29 21:38:56,085 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 21:38:56,116 - auth.auth_service - ERROR - authenticate_user:79 - Authentication error: When initializing mapper Mapper[UserPatientAccess(userpatientaccess)], expression "relationship("Optional['User']")" seems to be using a generic class as the argument to relationship(); please state the generic argument using an annotation, e.g. "user: Mapped[Optional['User']] = relationship()"
2025-07-29 21:38:56,117 - services.audit_service - ERROR - log_action:62 - Failed to write audit log: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[User(user)]'. Original exception was: When initializing mapper Mapper[UserPatientAccess(userpatientaccess)], expression "relationship("Optional['User']")" seems to be using a generic class as the argument to relationship(); please state the generic argument using an annotation, e.g. "user: Mapped[Optional['User']] = relationship()"
2025-07-29 21:38:56,118 - states.auth_state - WARNING - login:98 - Failed login attempt: dwd
2025-07-29 21:38:56,119 - sqlalchemy.engine.Engine - INFO - _connection_rollback_impl:2701 - ROLLBACK
2025-07-29 21:39:52,796 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-29 21:39:52,803 - auth.auth_service - ERROR - authenticate_user:79 - Authentication error: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[User(user)]'. Original exception was: When initializing mapper Mapper[UserPatientAccess(userpatientaccess)], expression "relationship("Optional['User']")" seems to be using a generic class as the argument to relationship(); please state the generic argument using an annotation, e.g. "user: Mapped[Optional['User']] = relationship()"
2025-07-29 21:39:52,808 - services.audit_service - ERROR - log_action:62 - Failed to write audit log: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[User(user)]'. Original exception was: When initializing mapper Mapper[UserPatientAccess(userpatientaccess)], expression "relationship("Optional['User']")" seems to be using a generic class as the argument to relationship(); please state the generic argument using an annotation, e.g. "user: Mapped[Optional['User']] = relationship()"
2025-07-29 21:39:52,825 - states.auth_state - WARNING - login:98 - Failed login attempt: admin
2025-07-29 21:39:52,855 - sqlalchemy.engine.Engine - INFO - _connection_rollback_impl:2701 - ROLLBACK
2025-07-30 12:57:19,582 - sqlalchemy.engine.Engine - INFO - _connection_begin_impl:2698 - BEGIN (implicit)
2025-07-30 12:57:19,584 - auth.auth_service - ERROR - authenticate_user:79 - Authentication error: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[User(user)]'. Original exception was: When initializing mapper Mapper[UserPatientAccess(userpatientaccess)], expression "relationship("Optional['User']")" seems to be using a generic class as the argument to relationship(); please state the generic argument using an annotation, e.g. "user: Mapped[Optional['User']] = relationship()"
2025-07-30 12:57:19,585 - services.audit_service - ERROR - log_action:62 - Failed to write audit log: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[User(user)]'. Original exception was: When initializing mapper Mapper[UserPatientAccess(userpatientaccess)], expression "relationship("Optional['User']")" seems to be using a generic class as the argument to relationship(); please state the generic argument using an annotation, e.g. "user: Mapped[Optional['User']] = relationship()"
2025-07-30 12:57:19,588 - states.auth_state - WARNING - login:98 - Failed login attempt: admin
2025-07-30 12:57:19,589 - sqlalchemy.engine.Engine - INFO - _connection_rollback_impl:2701 - ROLLBACK
