import{a}from"./chunk-QMGIS6GS-suYYFPSk.js";import{o as l,v as m,r as i,S as n,y as u}from"./text-DCkbNTq3.js";const d=["1","2","3","4","5"],f=["surface","classic","ghost"],p={...l,size:{type:"enum",className:"rt-r-size",values:d,default:"1",responsive:!0},variant:{type:"enum",className:"rt-variant",values:f,default:"surface"}},v=a.forwardRef((e,r)=>{const{asChild:s,className:t,...o}=m(e,p,i),c=s?n:"div";return a.createElement(c,{ref:r,...o,className:u("rt-reset","rt-BaseCard","rt-Card",t)})});v.displayName="Card";export{v as o};
