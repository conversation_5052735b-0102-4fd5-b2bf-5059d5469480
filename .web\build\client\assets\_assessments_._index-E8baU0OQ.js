import{w as P,a as e,v as w}from"./chunk-QMGIS6GS-suYYFPSk.js";import{f as i,i as u,E as m,c as l,h as k}from"./state-B1hYtTsq.js";import{B as W,F as A,a as R,b as H,c as I,d as M,e as B,f as q,C as j}from"./stateful_components-_envzdXf.js";import{D as h}from"./index-CKoiYi4C.js";import{j as t}from"./emotion-react.browser.esm-BNSIgtcs.js";import{p as C}from"./container-Do-LxYxS.js";import{ah as $,a3 as v,ae as F,W as S,ab as N}from"./createLucideIcon-BOfs0RvG.js";import{c as s,r as g,o as x}from"./button-Ccwu8jNm.js";import{o as p}from"./card-BFhh40Od.js";import{r as f,s as L,b as O}from"./text-area-DvQa0X7q.js";import{C as D,a as z,g as E,y as T,v as o}from"./select-hLIrycZp.js";import{u as y}from"./text-field-UsP3CXyu.js";import{p as a}from"./text-DCkbNTq3.js";import{C as G}from"./circle-CJ1s961q.js";import"./jsx-runtime-D_zvdyIk.js";import"./index-XOwJfM4g.js";/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const U=[["circle",{cx:"10",cy:"7",r:"4",key:"e45bow"}],["path",{d:"M10.3 15H7a4 4 0 0 0-4 4v2",key:"3bnktk"}],["circle",{cx:"17",cy:"17",r:"3",key:"18b49y"}],["path",{d:"m21 21-1.9-1.9",key:"1g2n9r"}]],V=$("user-search",U);function Q(){const _=e.useContext(i.reflex___state____state__states___clinical_state____clinical_state);return t(e.Fragment,{},_.disorder_criteria_rx_state_?.duration_required?t(e.Fragment,{},t(a,{as:"p",css:{color:"orange.600",fontStyle:"italic"}},"Duration requirements apply")):t(e.Fragment,{}))}function Y(){const _=e.useContext(i.reflex___state____state__states___clinical_state____clinical_state),[c,d]=e.useContext(m),n=e.useCallback(r=>c([l("reflex___state____state.states___clinical_state____clinical_state.create_assessment",{},{})],[r],{}),[c,l]);return t(x,{color:"green",loading:_.is_loading_rx_state_,onClick:n,size:"3"},"Complete Assessment")}function J(){const _=e.useContext(i.reflex___state____state__states___clinical_state____clinical_state);return t(L,{color:"blue",css:{width:"100%"},size:"3",value:_.criteria_met_percentage_rx_state_})}function K(){const _=e.useContext(i.reflex___state____state__states___clinical_state____clinical_state);return t(p,{css:{width:"100%",p:4,border:"2px solid",borderColor:u(_.dsm5_evaluation_rx_state_?.criteria_met)?"green.200":"red.200",background:u(_.dsm5_evaluation_rx_state_?.criteria_met)?"green.50":"red.50"}},t(s,{align:"start",className:"rx-Stack",direction:"column",gap:"4"},t(g,{css:{mb:3},size:"5"},"Evaluation Results"),t(s,{align:"start",className:"rx-Stack",direction:"row",gap:"3"},t(a,{as:"p",css:{fontWeight:"bold"}},"Diagnostic Criteria:"),t(wt,{})),t(S,{columns:"2",css:{width:"100%"},gap:"4"},t(s,{align:"start",className:"rx-Stack",direction:"column",gap:"1"},t(a,{as:"p",css:{fontWeight:"bold",color:"gray.700"}},"Criteria Summary"),t(Ct,{}),t(bt,{}),t(dt,{})),t(s,{align:"start",className:"rx-Stack",direction:"column",gap:"1"},t(a,{as:"p",css:{fontWeight:"bold",color:"gray.700"}},"Confidence Metrics"),t(nt,{}),t(rt,{}))),t(st,{}),t(s,{align:"start",className:"rx-Stack",direction:"column",gap:"2"},t(a,{as:"p",css:{fontWeight:"bold",color:"blue.700"}},"Clinical Recommendations"),t(pt,{}))))}function X(){const _=e.useContext(i.reflex___state____state__states___clinical_state____clinical_state);return t(e.Fragment,{},u(_.dsm5_evaluation_rx_state_)?t(e.Fragment,{},t(K,{})):t(e.Fragment,{}))}function Z(){const _=e.useContext(i.reflex___state____state__states___clinical_state____clinical_state),[c,d]=e.useContext(m),n=e.useCallback(r=>c([l("reflex___state____state.states___clinical_state____clinical_state.update_assessment_form",{field:"assessment_date",value:r.target.value},{})],[r],{}),[c,l]);return t(h,{css:{width:"100%"},debounceTimeout:300,element:y,onChange:n,type:"date",value:k(_.assessment_form_rx_state_.assessment_date)?_.assessment_form_rx_state_.assessment_date:""})}function tt(){const _=e.useContext(i.reflex___state____state__states___clinical_state____clinical_state),[c,d]=e.useContext(m);return t(s,{align:"start",className:"rx-Stack",css:{width:"100%",p:4,border:"1px solid",borderColor:"gray.200",borderRadius:"md"},direction:"column",gap:"3"},t(g,{css:{mb:3},size:"5"},"Diagnostic Criteria"),_.disorder_criteria_rx_state_?.criteria.map((n,r)=>t(p,{css:{width:"100%",p:3,"&:hover":{background:"gray.50"}},key:r},t(s,{align:"center",className:"rx-Stack",css:{width:"100%"},direction:"row",gap:"3"},t(a,{as:"label",size:"3"},t(s,{gap:"2"},t(O,{checked:_.criterion_responses_rx_state_[n.id]?_.criterion_responses_rx_state_[n.id]:!1,onCheckedChange:b=>c([l("reflex___state____state.states___clinical_state____clinical_state.update_criterion_response",{criterion_id:n.id,value:b},{})],[b],{}),size:"3"}),"")),t(s,{align:"start",className:"rx-Stack",css:{flex:"1"},direction:"column",gap:"1"},t(s,{align:"center",className:"rx-Stack",direction:"row",gap:"2"},t(a,{as:"p",css:{fontWeight:"medium"}},n.description),t(e.Fragment,{},n.required?t(e.Fragment,{},t(N,{color:"red",size:"1"},"Required")):t(e.Fragment,{}))),t(a,{as:"p",css:{color:"gray.500",fontSize:"sm"}},"Criterion ID: "+n.id)),t(e.Fragment,{},_.criterion_responses_rx_state_[n.id]&&_.criterion_responses_rx_state_[n.id]?t(e.Fragment,{},t(j,{css:{color:"green.500"},size:20})):t(e.Fragment,{},t(G,{css:{color:"gray.300"},size:20})))))))}function et(){const _=e.useContext(i.reflex___state____state__states___clinical_state____clinical_state);return t(a,{as:"p",css:{color:"blue.600"}},_.required_criteria_status_rx_state_)}function _t(){const _=e.useContext(i.reflex___state____state__states___clinical_state____clinical_state),[c,d]=e.useContext(m),n=e.useCallback(r=>c([l("reflex___state____state.states___clinical_state____clinical_state.update_assessment_form",{field:"secondary_diagnoses",value:r.target.value},{})],[r],{}),[c,l]);return t(h,{css:{width:"100%"},debounceTimeout:300,element:y,onChange:n,placeholder:"Additional diagnoses or rule-outs...",value:k(_.assessment_form_rx_state_.secondary_diagnoses)?_.assessment_form_rx_state_.secondary_diagnoses:""})}function st(){const _=e.useContext(i.reflex___state____state__states___clinical_state____clinical_state);return t(e.Fragment,{},u(_.dsm5_evaluation_rx_state_?.met_criteria_ids)?t(e.Fragment,{},t(s,{align:"start",className:"rx-Stack",direction:"column",gap:"1"},t(a,{as:"p",css:{fontWeight:"bold",color:"green.700"}},"Met Criteria:"),t(a,{as:"p",css:{color:"green.600"}},"• Criteria evaluation in progress..."))):t(e.Fragment,{}))}function at(){const _=e.useContext(i.reflex___state____state__states___clinical_state____clinical_state);return t(e.Fragment,{},_.assessment_form_rx_state_.patient_id!==0?t(e.Fragment,{},t(s,{align:"start",className:"rx-Stack",css:{width:"100%"},direction:"column",gap:"6"},t(p,{css:{p:4,width:"100%"}},t(s,{align:"start",className:"rx-Stack",css:{width:"100%"},direction:"column",gap:"4"},t(g,{css:{mb:4},size:"5"},"Assessment Information"),t(S,{columns:"2",css:{width:"100%"},gap:"4"},t(s,{align:"start",className:"rx-Stack",direction:"column",gap:"2"},t(a,{as:"p",css:{fontWeight:"bold"}},"Assessment Date *"),t(Z,{})),t(s,{align:"start",className:"rx-Stack",direction:"column",gap:"2"},t(a,{as:"p",css:{fontWeight:"bold"}},"Assessment Type"),t(D,{},t(z,{css:{width:"100%"},placeholder:"Select assessment type"}),t(E,{},t(T,{},"",t(o,{value:"Initial Evaluation"},"Initial Evaluation"),t(o,{value:"Follow-up"},"Follow-up"),t(o,{value:"Crisis Assessment"},"Crisis Assessment"),t(o,{value:"Medication Review"},"Medication Review"),t(o,{value:"Discharge Planning"},"Discharge Planning")))))),t(s,{align:"start",className:"rx-Stack",css:{width:"100%"},direction:"column",gap:"2"},t(a,{as:"p",css:{fontWeight:"bold"}},"Chief Complaint *"),t(Tt,{})))),t(p,{css:{p:4,width:"100%"}},t(s,{align:"start",className:"rx-Stack",css:{width:"100%"},direction:"column",gap:"4"},t(g,{css:{mb:4},size:"5"},"Clinical History"),t(s,{align:"start",className:"rx-Stack",css:{width:"100%"},direction:"column",gap:"2"},t(a,{as:"p",css:{fontWeight:"bold"}},"History of Present Illness *"),t(Ft,{})),t(S,{columns:"2",css:{width:"100%"},gap:"4"},t(s,{align:"start",className:"rx-Stack",direction:"column",gap:"2"},t(a,{as:"p",css:{fontWeight:"bold"}},"Past Psychiatric History"),t(f,{css:{"& textarea":null,width:"100%",minHeight:"100px"},placeholder:"Previous mental health diagnoses, treatments, hospitalizations..."})),t(s,{align:"start",className:"rx-Stack",direction:"column",gap:"2"},t(a,{as:"p",css:{fontWeight:"bold"}},"Medical History"),t(f,{css:{"& textarea":null,width:"100%",minHeight:"100px"},placeholder:"Relevant medical conditions, medications, allergies..."})),t(s,{align:"start",className:"rx-Stack",direction:"column",gap:"2"},t(a,{as:"p",css:{fontWeight:"bold"}},"Family History"),t(f,{css:{"& textarea":null,width:"100%",minHeight:"100px"},placeholder:"Family history of mental health or medical conditions..."})),t(s,{align:"start",className:"rx-Stack",direction:"column",gap:"2"},t(a,{as:"p",css:{fontWeight:"bold"}},"Social History"),t(f,{css:{"& textarea":null,width:"100%",minHeight:"100px"},placeholder:"Substance use, relationships, work, education, trauma history..."}))))),t(p,{css:{p:4,width:"100%"}},t(s,{align:"start",className:"rx-Stack",css:{width:"100%"},direction:"column",gap:"4"},t(g,{css:{mb:4},size:"5"},"DSM-5-TR Diagnostic Evaluation"),t(s,{align:"start",className:"rx-Stack",css:{width:"100%"},direction:"column",gap:"4"},t(g,{css:{mb:4},size:"6"},"DSM-5-TR Diagnostic Criteria"),t(s,{align:"start",className:"rx-Stack",css:{width:"100%"},direction:"column",gap:"2"},t(a,{as:"p",css:{fontWeight:"bold",mb:2}},"Select Disorder"),t(gt,{})),t(kt,{})),t(Pt,{}))),t(p,{css:{p:4,width:"100%"}},t(s,{align:"start",className:"rx-Stack",css:{width:"100%"},direction:"column",gap:"4"},t(g,{css:{mb:4},size:"5"},"Treatment Planning"),t(S,{columns:"2",css:{width:"100%"},gap:"4"},t(s,{align:"start",className:"rx-Stack",direction:"column",gap:"2"},t(a,{as:"p",css:{fontWeight:"bold"}},"Primary Diagnosis"),t(ut,{})),t(s,{align:"start",className:"rx-Stack",direction:"column",gap:"2"},t(a,{as:"p",css:{fontWeight:"bold"}},"Secondary Diagnoses"),t(_t,{}))),t(s,{align:"start",className:"rx-Stack",css:{width:"100%"},direction:"column",gap:"2"},t(a,{as:"p",css:{fontWeight:"bold"}},"Treatment Plan *"),t(yt,{})),t(s,{align:"start",className:"rx-Stack",css:{width:"100%"},direction:"column",gap:"2"},t(a,{as:"p",css:{fontWeight:"bold"}},"Risk Assessment"),t(f,{css:{"& textarea":null,width:"100%",minHeight:"100px"},placeholder:"Suicide risk, violence risk, safety planning..."})))),t(p,{css:{p:4,width:"100%"}},t(s,{align:"start",className:"rx-Stack",css:{width:"100%"},direction:"row",gap:"3"},t(x,{color:"gray",size:"3"},"Save as Draft"),t(x,{color:"blue",size:"3"},"Preview Assessment"),t(s,{css:{flex:1,justifySelf:"stretch",alignSelf:"stretch"}}),t(x,{size:"3",variant:"outline"},"Cancel"),t(Y,{}))))):t(e.Fragment,{},t(p,{css:{p:8,width:"100%",textAlign:"center"}},t(s,{align:"center",className:"rx-Stack",direction:"column",gap:"3"},t(V,{css:{color:"gray.400"},size:48}),t(g,{css:{color:"gray.600"},size:"5"},"No Patient Selected"),t(a,{as:"p",css:{color:"gray.500"}},"Please select a patient to begin the clinical assessment"),t(x,{color:"blue",css:{mt:4},size:"3"},"Search Patients")))))}function it(){const _=e.useContext(i.reflex___state____state__states___patient_state____patient_state);return t(a,{as:"p",css:{color:"gray.600"}},"DOB: "+_.current_patient_rx_state_?.dob)}function nt(){const _=e.useContext(i.reflex___state____state__states___clinical_state____clinical_state);return t(a,{as:"p"},"Confidence: "+(u(_.dsm5_evaluation_rx_state_?.confidence_level)?_.dsm5_evaluation_rx_state_?.confidence_level:0))}function ct(){const _=e.useContext(i.reflex___state____state__states___clinical_state____clinical_state);return t(a,{as:"p",css:{color:"gray.600"}},"Code: "+_.disorder_criteria_rx_state_?.disorder_code)}function rt(){const _=e.useContext(i.reflex___state____state__states___clinical_state____clinical_state);return t(e.Fragment,{},u(u(_.dsm5_evaluation_rx_state_?.duration_required)?_.dsm5_evaluation_rx_state_?.duration_required:null)?t(e.Fragment,{},t(a,{as:"p"},"Duration Required: Yes")):t(e.Fragment,{},t(a,{as:"p"},"Duration Required: No")))}function lt(){const _=e.useContext(i.reflex___state____state__states___patient_state____patient_state);return t(e.Fragment,{},u(_.current_patient_rx_state_)?t(e.Fragment,{},t(s,{align:"center",className:"rx-Stack",css:{width:"100%"},direction:"row",gap:"3"},t(s,{align:"start",className:"rx-Stack",direction:"column",gap:"1"},t(Nt,{}),t(it,{}),t(ot,{})),t(s,{css:{flex:1,justifySelf:"stretch",alignSelf:"stretch"}}),t(x,{size:"2",variant:"outline"},"Change Patient"))):t(e.Fragment,{},t(s,{align:"start",className:"rx-Stack",css:{width:"100%"},direction:"column",gap:"3"},t(a,{as:"p",css:{color:"gray.600"}},"Search and select a patient to begin assessment:"),t(s,{align:"start",className:"rx-Stack",css:{width:"100%"},direction:"row",gap:"2"},t(Dt,{}),t(St,{})),t(ft,{}))))}function ot(){const _=e.useContext(i.reflex___state____state__states___patient_state____patient_state);return t(a,{as:"p",css:{color:"gray.600"}},"ID: "+_.current_patient_rx_state_?.id)}function dt(){const _=e.useContext(i.reflex___state____state__states___clinical_state____clinical_state);return t(a,{as:"p"},"Total: "+Object.keys(_.criterion_responses_rx_state_).length)}function ut(){const _=e.useContext(i.reflex___state____state__states___clinical_state____clinical_state),[c,d]=e.useContext(m),n=e.useCallback(r=>c([l("reflex___state____state.states___clinical_state____clinical_state.update_assessment_form",{field:"primary_diagnosis",value:r.target.value},{})],[r],{}),[c,l]);return t(h,{css:{width:"100%"},debounceTimeout:300,element:y,onChange:n,placeholder:"Primary diagnostic impression...",value:k(_.assessment_form_rx_state_.primary_diagnosis)?_.assessment_form_rx_state_.primary_diagnosis:""})}function mt(){const _=e.useContext(i.reflex___state____state__states___clinical_state____clinical_state);return t(g,{size:"5"},_.disorder_criteria_rx_state_?.name)}function gt(){const _=e.useContext(i.reflex___state____state__states___clinical_state____clinical_state),[c,d]=e.useContext(m),n=e.useCallback(r=>c([l("reflex___state____state.states___clinical_state____clinical_state.select_disorder",{disorder_code:r},{})],[r],{}),[c,l]);return t(D,{onValueChange:n,size:"3",value:_.selected_disorder_rx_state_},t(z,{css:{width:"100%"},placeholder:"Choose a disorder to evaluate..."}),t(E,{},t(T,{},"",t(o,{value:"Major Depressive Disorder"},"Major Depressive Disorder"),t(o,{value:"Generalized Anxiety Disorder"},"Generalized Anxiety Disorder"),t(o,{value:"Bipolar I Disorder"},"Bipolar I Disorder"),t(o,{value:"Bipolar II Disorder"},"Bipolar II Disorder"),t(o,{value:"Panic Disorder"},"Panic Disorder"),t(o,{value:"Social Anxiety Disorder"},"Social Anxiety Disorder"),t(o,{value:"PTSD"},"PTSD"),t(o,{value:"ADHD"},"ADHD"),t(o,{value:"Autism Spectrum Disorder"},"Autism Spectrum Disorder"),t(o,{value:"Schizophrenia"},"Schizophrenia"))))}function pt(){const _=e.useContext(i.reflex___state____state__states___clinical_state____clinical_state);return t(e.Fragment,{},u(_.dsm5_evaluation_rx_state_?.criteria_met)?t(e.Fragment,{},t(s,{align:"start",className:"rx-Stack",direction:"column",gap:"1"},t(a,{as:"p",css:{color:"green.600"}},"✓ Diagnostic criteria are met for this disorder"),t(a,{as:"p",css:{color:"blue.600"}},"• Consider differential diagnoses"),t(a,{as:"p",css:{color:"blue.600"}},"• Evaluate for comorbid conditions"),t(a,{as:"p",css:{color:"blue.600"}},"• Assess functional impairment"))):t(e.Fragment,{},t(s,{align:"start",className:"rx-Stack",direction:"column",gap:"1"},t(a,{as:"p",css:{color:"red.600"}},"✗ Diagnostic criteria are not fully met"),t(a,{as:"p",css:{color:"blue.600"}},"• Consider other disorders in differential"),t(a,{as:"p",css:{color:"blue.600"}},"• Re-evaluate symptoms over time"),t(a,{as:"p",css:{color:"blue.600"}},"• Consider subsyndromal presentations"))))}function xt(){const _=e.useContext(i.reflex___state____state__states___clinical_state____clinical_state);return t(a,{as:"p",css:{fontWeight:"bold"}},"Minimum criteria required: "+_.disorder_criteria_rx_state_?.minimum_criteria)}function ft(){const _=e.useContext(i.reflex___state____state__states___patient_state____patient_state);return t(e.Fragment,{},u(_.search_results_rx_state_)?t(e.Fragment,{},t(zt,{})):t(e.Fragment,{}))}function ht(){const[_,c]=e.useContext(m),d=e.useCallback(n=>_([l("reflex___state____state.states___clinical_state____clinical_state.load_disorders",{},{})],[n],{}),[_,l]);return t(x,{color:"blue",onClick:d,size:"3"},"Load Available Disorders")}function bt(){const _=e.useContext(i.reflex___state____state__states___clinical_state____clinical_state);return t(a,{as:"p"},"Required: "+_.dsm5_evaluation_rx_state_?.required_criteria)}function Ct(){const _=e.useContext(i.reflex___state____state__states___clinical_state____clinical_state);return t(a,{as:"p"},"Met: "+_.dsm5_evaluation_rx_state_?.met_criteria_count)}function vt(){const _=e.useContext(i.reflex___state____state__states___clinical_state____clinical_state);return t(N,{color:_.diagnosis_confidence_rx_state_==="High confidence"?"green":_.diagnosis_confidence_rx_state_==="Moderate confidence"?"yellow":"red"},_.diagnosis_confidence_rx_state_)}function St(){const[_,c]=e.useContext(m),d=e.useCallback(n=>_([l("reflex___state____state.states___patient_state____patient_state.search_patients",{},{})],[n],{}),[_,l]);return t(x,{color:"blue",onClick:d},"Search")}function kt(){const _=e.useContext(i.reflex___state____state__states___clinical_state____clinical_state);return t(e.Fragment,{},_.available_disorders_rx_state_.length===0?t(e.Fragment,{},t(ht,{})):t(e.Fragment,{}))}function yt(){const _=e.useContext(i.reflex___state____state__states___clinical_state____clinical_state),[c,d]=e.useContext(m),n=e.useCallback(r=>c([l("reflex___state____state.states___clinical_state____clinical_state.update_assessment_form",{field:"treatment_plan",value:r.target.value},{})],[r],{}),[c,l]);return t(h,{css:{width:"100%",minHeight:"150px"},debounceTimeout:300,element:f,onChange:n,placeholder:"Comprehensive treatment plan including interventions, medications, therapy recommendations, follow-up plans...",value:_.assessment_form_rx_state_.treatment_plan})}function wt(){const _=e.useContext(i.reflex___state____state__states___clinical_state____clinical_state);return t(N,{color:u(_.dsm5_evaluation_rx_state_?.criteria_met)?"green":"red",size:"3"},u(_.dsm5_evaluation_rx_state_?.criteria_met)?"CRITERIA MET":"CRITERIA NOT MET")}function Ft(){const _=e.useContext(i.reflex___state____state__states___clinical_state____clinical_state),[c,d]=e.useContext(m),n=e.useCallback(r=>c([l("reflex___state____state.states___clinical_state____clinical_state.update_assessment_form",{field:"history_present_illness",value:r.target.value},{})],[r],{}),[c,l]);return t(h,{css:{width:"100%",minHeight:"150px"},debounceTimeout:300,element:f,onChange:n,placeholder:"Detailed description of current symptoms, onset, duration, severity, and course...",value:_.assessment_form_rx_state_.history_present_illness})}function Nt(){const _=e.useContext(i.reflex___state____state__states___patient_state____patient_state);return t(a,{as:"p",css:{fontWeight:"bold",fontSize:"lg"}},_.current_patient_rx_state_?.name)}function Dt(){const _=e.useContext(i.reflex___state____state__states___patient_state____patient_state),[c,d]=e.useContext(m),n=e.useCallback(r=>c([l("reflex___state____state.states___patient_state____patient_state.set_search_term",{value:r.target.value},{})],[r],{}),[c,l]);return t(h,{css:{width:"100%"},debounceTimeout:300,element:y,onChange:n,placeholder:"Search patients by name or ID...",value:k(_.search_term_rx_state_)?_.search_term_rx_state_:""})}function zt(){const _=e.useContext(i.reflex___state____state__states___patient_state____patient_state),[c,d]=e.useContext(m);return t(s,{align:"start",className:"rx-Stack",css:{width:"100%"},direction:"column",gap:"2"},t(a,{as:"p",css:{fontWeight:"bold"}},"Select a patient:"),_.search_results_rx_state_.slice(void 0,5).map((n,r)=>t(x,{css:{width:"100%",justify:"start"},key:r,onClick:b=>c([l("reflex___state____state.states___patient_state____patient_state.load_patient",{patient_id:n.id},{}),l("reflex___state____state.states___clinical_state____clinical_state.update_assessment_form",{field:"patient_id",value:n.id},{})],[b],{}),variant:"outline"},n.name+" (ID: "+n.id+")")))}function Et(){const _=e.useContext(i.reflex___state____state__states___auth_state____auth_state);return t(e.Fragment,{},_.is_authenticated_rx_state_?t(e.Fragment,{},t(s,{align:"start",className:"rx-Stack",css:{width:"100%",minHeight:"100vh"},direction:"column",gap:"0"},t(v,{css:{width:"100%",background:"white",borderBottom:"1px solid",borderColor:"gray.200",p:4,position:"sticky",top:"0",zIndex:"1000"}},t(C,{css:{padding:"16px",maxWidth:"1200px"},size:"3"},t(s,{align:"center",className:"rx-Stack",css:{width:"100%"},direction:"row",gap:"3"},t(s,{align:"start",className:"rx-Stack",direction:"row",gap:"3"},t(W,{css:{color:"blue.600"},size:32}),t(g,{css:{color:"blue.600"},size:"6"},"Psychiatry EMR")),t(A,{}),t(s,{css:{flex:1,justifySelf:"stretch",alignSelf:"stretch"}}),t(R,{})))),t(s,{align:"start",className:"rx-Stack",direction:"column",gap:"2"},t(H,{}),t(I,{}),t(M,{}),t(B,{})),t(v,{css:{width:"100%",flex:"1"}},t(s,{align:"start",className:"rx-Stack",css:{width:"100%",minHeight:"100vh",background:"gray.50"},direction:"column",gap:"3"},t(v,{css:{width:"100%",background:"white",borderBottom:"1px solid",borderColor:"gray.200",p:4}},t(C,{css:{padding:"16px",maxWidth:"1200px"},size:"3"},t(s,{align:"center",className:"rx-Stack",css:{width:"100%"},direction:"row",gap:"3"},t(s,{align:"start",className:"rx-Stack",direction:"row",gap:"2"},t(F,{asChild:!0,css:{color:"blue.600","&:hover":{color:"var(--accent-8)"}}},t(w,{to:"/dashboard"},"Dashboard")),t(a,{as:"p",css:{color:"gray.400"}},"/"),t(a,{as:"p",css:{fontWeight:"bold"}},"Clinical Assessment")),t(s,{css:{flex:1,justifySelf:"stretch",alignSelf:"stretch"}}),t(s,{align:"start",className:"rx-Stack",direction:"row",gap:"2"},t(x,{size:"2",variant:"outline"},"Load Assessment"),t(x,{color:"gray",size:"2"},"Save Draft"))))),t(C,{css:{padding:"16px",maxWidth:"1200px",p:6},size:"3"},t(s,{align:"start",className:"rx-Stack",css:{width:"100%"},direction:"column",gap:"6"},t(p,{css:{p:4,width:"100%"}},t(s,{align:"start",className:"rx-Stack",css:{width:"100%"},direction:"column",gap:"3"},t(g,{css:{mb:3},size:"5"},"Patient Selection"),t(lt,{}))),t(at,{}))))),t(v,{css:{width:"100%",background:"gray.100",borderTop:"1px solid",borderColor:"gray.200",p:4,mt:"auto"}},t(C,{css:{padding:"16px",maxWidth:"1200px"},size:"3"},t(s,{align:"center",className:"rx-Stack",css:{width:"100%"},direction:"row",gap:"3"},t(a,{as:"p",css:{color:"gray.600",fontSize:"sm"}},"© 2024 Psychiatry EMR. All rights reserved."),t(s,{css:{flex:1,justifySelf:"stretch",alignSelf:"stretch"}}),t(s,{align:"start",className:"rx-Stack",direction:"row",gap:"2"},t(a,{as:"p",css:{color:"gray.500",fontSize:"xs"}},"Version 1.0.0"),t(a,{as:"p",css:{color:"gray.400"}},"•"),t(F,{asChild:!0,css:{color:"gray.500",fontSize:"xs","&:hover":{color:"var(--accent-8)"}}},t(w,{to:"/privacy"},"Privacy Policy")),t(a,{as:"p",css:{color:"gray.400"}},"•"),t(F,{asChild:!0,css:{color:"gray.500",fontSize:"xs","&:hover":{color:"var(--accent-8)"}}},t(w,{to:"/support"},"Support")))))))):t(e.Fragment,{},t(s,{align:"center",className:"rx-Stack",css:{p:8},direction:"column",gap:"4"},t(g,{size:"6"},"Authentication Required"),t(a,{as:"p"},"Please log in to access this page."),t(q,{}))))}function Tt(){const _=e.useContext(i.reflex___state____state__states___clinical_state____clinical_state),[c,d]=e.useContext(m),n=e.useCallback(r=>c([l("reflex___state____state.states___clinical_state____clinical_state.update_assessment_form",{field:"chief_complaint",value:r.target.value},{})],[r],{}),[c,l]);return t(h,{css:{width:"100%",minHeight:"100px"},debounceTimeout:300,element:f,onChange:n,placeholder:"Patient's primary concern or reason for visit...",value:_.assessment_form_rx_state_.chief_complaint})}function Pt(){const _=e.useContext(i.reflex___state____state__states___clinical_state____clinical_state);return t(e.Fragment,{},u(_.disorder_criteria_rx_state_)?t(e.Fragment,{},t(s,{align:"start",className:"rx-Stack",css:{width:"100%"},direction:"column",gap:"6"},t(p,{css:{width:"100%",p:4,background:"blue.50"}},t(s,{align:"start",className:"rx-Stack",direction:"column",gap:"2"},t(mt,{}),t(ct,{}),t(xt,{}),t(Q,{}))),t(p,{css:{width:"100%",p:4,background:"gray.50"}},t(s,{align:"start",className:"rx-Stack",css:{width:"100%"},direction:"column",gap:"3"},t(s,{align:"start",className:"rx-Stack",css:{width:"100%"},direction:"row",gap:"3"},t(a,{as:"p",css:{fontWeight:"bold"}},"Diagnostic Progress"),t(s,{css:{flex:1,justifySelf:"stretch",alignSelf:"stretch"}}),t(et,{})),t(J,{}),t(s,{align:"start",className:"rx-Stack",direction:"row",gap:"2"},t(a,{as:"p",css:{fontWeight:"medium"}},"Confidence Level:"),t(vt,{})))),t(tt,{}),t(X,{}))):t(e.Fragment,{}))}const Yt=P(function(){return t(e.Fragment,{},t(Et,{}),t("title",{},"Clinical Assessment - Psychiatry EMR"),t("meta",{content:"favicon.ico",property:"og:image"}))});export{Yt as default};
