import{a as r}from"./chunk-QMGIS6GS-suYYFPSk.js";import{o as C,v as l,r as N,S as x,y as o}from"./text-DCkbNTq3.js";import{u as $,e as E,t as H}from"./button-Ccwu8jNm.js";function W(e,t){const{asChild:a,children:i}=e;if(!a)return typeof t=="function"?t(i):t;const n=r.Children.only(i);return r.cloneElement(n,{children:typeof t=="function"?t(n.props.children):t})}const w=["1","2","3","4"],z=["none","initial"],S=["left","center","right"],V={...C,size:{type:"enum",className:"rt-r-size",values:w,default:"4",responsive:!0},display:{type:"enum",className:"rt-r-display",values:z,parseValue:I,responsive:!0},align:{type:"enum",className:"rt-r-ai",values:S,parseValue:R,responsive:!0}};function I(e){return e==="initial"?"flex":e}function R(e){return e==="left"?"start":e==="right"?"end":e}const b=r.forwardRef(({width:e,minWidth:t,maxWidth:a,height:i,minHeight:n,maxHeight:c,...m},h)=>{const{asChild:s,children:d,className:u,...p}=l(m,V,$,N),{className:f,style:y}=l({width:e,minWidth:t,maxWidth:a,height:i,minHeight:n,maxHeight:c},H,E),g=s?x:"div";return r.createElement(g,{...p,ref:h,className:o("rt-Container",u)},W({asChild:s,children:d},v=>r.createElement("div",{className:o("rt-ContainerInner",f),style:y},v)))});b.displayName="Container";export{W as d,b as p};
