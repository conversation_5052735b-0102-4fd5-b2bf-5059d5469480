window.__reactRouterManifest={"entry":{"module":"/assets/entry.client-BjDPl_tA.js","imports":["/assets/jsx-runtime-D_zvdyIk.js","/assets/chunk-QMGIS6GS-suYYFPSk.js","/assets/index-XOwJfM4g.js"],"css":[]},"routes":{"404":{"id":"404","parentId":"root","path":"404","hasAction":false,"hasLoader":false,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/_404_._index-Bd5cog6q.js","imports":["/assets/chunk-QMGIS6GS-suYYFPSk.js","/assets/state-B1hYtTsq.js","/assets/emotion-react.browser.esm-BNSIgtcs.js"],"css":[]},"root":{"id":"root","path":"","hasAction":false,"hasLoader":false,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/root-CN8mmq02.js","imports":["/assets/jsx-runtime-D_zvdyIk.js","/assets/chunk-QMGIS6GS-suYYFPSk.js","/assets/index-XOwJfM4g.js","/assets/state-B1hYtTsq.js","/assets/emotion-react.browser.esm-BNSIgtcs.js","/assets/button-Ccwu8jNm.js","/assets/text-DCkbNTq3.js","/assets/createLucideIcon-BOfs0RvG.js","/assets/container-Do-LxYxS.js","/assets/card-BFhh40Od.js","/assets/text-area-DvQa0X7q.js","/assets/text-field-UsP3CXyu.js","/assets/select-hLIrycZp.js","/assets/separator-CPIfs_Op.js"],"css":[]},"routes/[assessments]._index":{"id":"routes/[assessments]._index","parentId":"root","path":"assessments","index":true,"hasAction":false,"hasLoader":false,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/_assessments_._index-E8baU0OQ.js","imports":["/assets/chunk-QMGIS6GS-suYYFPSk.js","/assets/state-B1hYtTsq.js","/assets/stateful_components-_envzdXf.js","/assets/index-CKoiYi4C.js","/assets/emotion-react.browser.esm-BNSIgtcs.js","/assets/container-Do-LxYxS.js","/assets/createLucideIcon-BOfs0RvG.js","/assets/button-Ccwu8jNm.js","/assets/card-BFhh40Od.js","/assets/text-area-DvQa0X7q.js","/assets/select-hLIrycZp.js","/assets/text-field-UsP3CXyu.js","/assets/text-DCkbNTq3.js","/assets/circle-CJ1s961q.js","/assets/jsx-runtime-D_zvdyIk.js","/assets/index-XOwJfM4g.js"],"css":[]},"routes/[dashboard]._index":{"id":"routes/[dashboard]._index","parentId":"root","path":"dashboard","index":true,"hasAction":false,"hasLoader":false,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/_dashboard_._index-C4vLDbwm.js","imports":["/assets/chunk-QMGIS6GS-suYYFPSk.js","/assets/state-B1hYtTsq.js","/assets/stateful_components-_envzdXf.js","/assets/emotion-react.browser.esm-BNSIgtcs.js","/assets/createLucideIcon-BOfs0RvG.js","/assets/user-plus-BRYdVFIJ.js","/assets/circle-CJ1s961q.js","/assets/button-Ccwu8jNm.js","/assets/container-Do-LxYxS.js","/assets/card-BFhh40Od.js","/assets/text-DCkbNTq3.js","/assets/jsx-runtime-D_zvdyIk.js","/assets/index-XOwJfM4g.js"],"css":[]},"routes/[patients]._index":{"id":"routes/[patients]._index","parentId":"root","path":"patients","index":true,"hasAction":false,"hasLoader":false,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/_patients_._index-DXPQ08sE.js","imports":["/assets/chunk-QMGIS6GS-suYYFPSk.js","/assets/state-B1hYtTsq.js","/assets/stateful_components-_envzdXf.js","/assets/index-CKoiYi4C.js","/assets/emotion-react.browser.esm-BNSIgtcs.js","/assets/user-plus-BRYdVFIJ.js","/assets/createLucideIcon-BOfs0RvG.js","/assets/button-Ccwu8jNm.js","/assets/container-Do-LxYxS.js","/assets/text-DCkbNTq3.js","/assets/card-BFhh40Od.js","/assets/text-field-UsP3CXyu.js","/assets/select-hLIrycZp.js","/assets/jsx-runtime-D_zvdyIk.js","/assets/index-XOwJfM4g.js"],"css":[]},"routes/[health]._index":{"id":"routes/[health]._index","parentId":"root","path":"health","index":true,"hasAction":false,"hasLoader":false,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/_health_._index-mTsryIM6.js","imports":["/assets/chunk-QMGIS6GS-suYYFPSk.js","/assets/emotion-react.browser.esm-BNSIgtcs.js","/assets/text-DCkbNTq3.js","/assets/jsx-runtime-D_zvdyIk.js"],"css":[]},"routes/[login]._index":{"id":"routes/[login]._index","parentId":"root","path":"login","index":true,"hasAction":false,"hasLoader":false,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/_login_._index-CX9q6itg.js","imports":["/assets/chunk-QMGIS6GS-suYYFPSk.js","/assets/index-CKoiYi4C.js","/assets/state-B1hYtTsq.js","/assets/emotion-react.browser.esm-BNSIgtcs.js","/assets/button-Ccwu8jNm.js","/assets/card-BFhh40Od.js","/assets/text-DCkbNTq3.js","/assets/text-field-UsP3CXyu.js","/assets/index-XOwJfM4g.js","/assets/jsx-runtime-D_zvdyIk.js"],"css":[]},"routes/_index":{"id":"routes/_index","parentId":"root","index":true,"hasAction":false,"hasLoader":false,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/_index-kb8xG_OW.js","imports":["/assets/chunk-QMGIS6GS-suYYFPSk.js","/assets/state-B1hYtTsq.js","/assets/emotion-react.browser.esm-BNSIgtcs.js","/assets/separator-CPIfs_Op.js","/assets/button-Ccwu8jNm.js","/assets/container-Do-LxYxS.js","/assets/text-DCkbNTq3.js","/assets/index-XOwJfM4g.js","/assets/jsx-runtime-D_zvdyIk.js"],"css":[]},"routes/[404]._index":{"id":"routes/[404]._index","parentId":"root","path":"*","hasAction":false,"hasLoader":false,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/_404_._index-Bd5cog6q.js","imports":["/assets/chunk-QMGIS6GS-suYYFPSk.js","/assets/state-B1hYtTsq.js","/assets/emotion-react.browser.esm-BNSIgtcs.js"],"css":[]}},"url":"/assets/manifest-c1b6a5b7.js","version":"c1b6a5b7"};