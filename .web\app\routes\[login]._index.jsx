

import { Fragment, useCallback, useContext, useEffect } from "react"
import { <PERSON><PERSON> as RadixThemesButton, Card as RadixThemesCard, Flex as RadixThemesFlex, Heading as RadixThemesHeading, Text as RadixThemesText, Text<PERSON>ield as RadixThemesTextField } from "@radix-ui/themes"
import DebounceInput from "react-debounce-input"
import { EventLoopContext, StateContexts } from "$/utils/context"
import { Event, isNotNullOrUndefined, isTrue } from "$/utils/state"
import { jsx } from "@emotion/react"



function Button_324740584035598233909433632458852381100 () {
  
  const reflex___state____state__states___auth_state____auth_state = useContext(StateContexts.reflex___state____state__states___auth_state____auth_state)
  const [addEvents, connectErrors] = useContext(EventLoopContext);


  const on_click_60fb50d82dfd9be204baa0752bdfc387 = useCallback(((_e) => (addEvents([(Event("reflex___state____state.states___auth_state____auth_state.login", ({  }), ({  })))], [_e], ({  })))), [addEvents, Event])



  
  return (
    jsx(
RadixThemesButton,
{color:"blue",css:({ ["width"] : "100%" }),loading:reflex___state____state__states___auth_state____auth_state.is_loading_rx_state_,onClick:on_click_60fb50d82dfd9be204baa0752bdfc387,size:"3"},
"Login"
,)
  )
}

function Text_83621449188208791043975651714180903358 () {
  
  const reflex___state____state__states___auth_state____auth_state = useContext(StateContexts.reflex___state____state__states___auth_state____auth_state)





  
  return (
    jsx(
RadixThemesText,
{as:"p",css:({ ["color"] : "red.600" })},
reflex___state____state__states___auth_state____auth_state.error_message_rx_state_
,)
  )
}

function Fragment_31135191912617128381316688324190997604 () {
  
  const reflex___state____state__states___auth_state____auth_state = useContext(StateContexts.reflex___state____state__states___auth_state____auth_state)





  
  return (
    jsx(
Fragment,
{},
(isTrue(reflex___state____state__states___auth_state____auth_state.success_message_rx_state_) ? (jsx(
Fragment,
{},
jsx(
RadixThemesCard,
{css:({ ["width"] : "100%", ["background"] : "green.50", ["border"] : "1px solid", ["borderColor"] : "green.200" })},
jsx(Text_169008371144166888117864256652347267442,{},)
,),)) : (jsx(Fragment,{},)
)),)
  )
}

function Fragment_61108004300754007991154554505994133759 () {
  
  const reflex___state____state__states___auth_state____auth_state = useContext(StateContexts.reflex___state____state__states___auth_state____auth_state)





  
  return (
    jsx(
Fragment,
{},
(isTrue(reflex___state____state__states___auth_state____auth_state.error_message_rx_state_) ? (jsx(
Fragment,
{},
jsx(
RadixThemesCard,
{css:({ ["width"] : "100%", ["background"] : "red.50", ["border"] : "1px solid", ["borderColor"] : "red.200" })},
jsx(Text_83621449188208791043975651714180903358,{},)
,),)) : (jsx(Fragment,{},)
)),)
  )
}

function Debounceinput_264006250441323901387131323639965858808 () {
  
  const reflex___state____state__states___auth_state____auth_state = useContext(StateContexts.reflex___state____state__states___auth_state____auth_state)
  const [addEvents, connectErrors] = useContext(EventLoopContext);


  const on_change_c908689a1637ed6b8c67a1a3950a2907 = useCallback(((_e) => (addEvents([(Event("reflex___state____state.states___auth_state____auth_state.update_login_form", ({ ["field"] : "password", ["value"] : _e["target"]["value"] }), ({  })))], [_e], ({  })))), [addEvents, Event])



  
  return (
    jsx(DebounceInput,{css:({ ["width"] : "100%" }),debounceTimeout:300,element:RadixThemesTextField.Root,onChange:on_change_c908689a1637ed6b8c67a1a3950a2907,placeholder:"Password",type:"password",value:(isNotNullOrUndefined(reflex___state____state__states___auth_state____auth_state.login_password_rx_state_) ? reflex___state____state__states___auth_state____auth_state.login_password_rx_state_ : "")},)

  )
}

function Debounceinput_280080768645961709395306845982695162026 () {
  
  const reflex___state____state__states___auth_state____auth_state = useContext(StateContexts.reflex___state____state__states___auth_state____auth_state)
  const [addEvents, connectErrors] = useContext(EventLoopContext);


  const on_change_2a68e3ad1693e424419ac7915b557648 = useCallback(((_e) => (addEvents([(Event("reflex___state____state.states___auth_state____auth_state.update_login_form", ({ ["field"] : "username", ["value"] : _e["target"]["value"] }), ({  })))], [_e], ({  })))), [addEvents, Event])



  
  return (
    jsx(DebounceInput,{css:({ ["width"] : "100%" }),debounceTimeout:300,element:RadixThemesTextField.Root,onChange:on_change_2a68e3ad1693e424419ac7915b557648,placeholder:"Username",value:(isNotNullOrUndefined(reflex___state____state__states___auth_state____auth_state.login_username_rx_state_) ? reflex___state____state__states___auth_state____auth_state.login_username_rx_state_ : "")},)

  )
}

function Text_169008371144166888117864256652347267442 () {
  
  const reflex___state____state__states___auth_state____auth_state = useContext(StateContexts.reflex___state____state__states___auth_state____auth_state)





  
  return (
    jsx(
RadixThemesText,
{as:"p",css:({ ["color"] : "green.600" })},
reflex___state____state__states___auth_state____auth_state.success_message_rx_state_
,)
  )
}

export default function Component() {
    




  return (
    jsx(
Fragment,
{},
jsx(
RadixThemesFlex,
{css:({ ["display"] : "flex", ["alignItems"] : "center", ["justifyContent"] : "center", ["minHeight"] : "100vh", ["background"] : "gray.50" })},
jsx(
RadixThemesCard,
{css:({ ["maxWidth"] : "400px", ["p"] : 8 })},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",css:({ ["width"] : "100%" }),direction:"column",gap:"4"},
jsx(
RadixThemesHeading,
{css:({ ["color"] : "blue.600", ["mb"] : 6 }),size:"8"},
"Psychiatry EMR"
,),jsx(
RadixThemesText,
{as:"p",css:({ ["color"] : "gray.600", ["mb"] : 4 })},
"Secure Patient Management System"
,),jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",css:({ ["width"] : "100%" }),direction:"column",gap:"4"},
jsx(Debounceinput_280080768645961709395306845982695162026,{},)
,jsx(Debounceinput_264006250441323901387131323639965858808,{},)
,jsx(Button_324740584035598233909433632458852381100,{},)
,),jsx(Fragment_61108004300754007991154554505994133759,{},)
,jsx(Fragment_31135191912617128381316688324190997604,{},)
,),),),jsx(
"title",
{},
"Login - Psychiatry EMR"
,),jsx("meta",{content:"favicon.ico",property:"og:image"},)
,)
  )
}
