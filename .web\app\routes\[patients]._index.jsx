

import { Fragment, useCallback, useContext, useEffect } from "react"
import { EventLoopContext, StateContexts } from "$/utils/context"
import { Event, isNotNullOrUndefined, isTrue } from "$/utils/state"
import { Badge as RadixThemesBadge, Box as RadixThemesBox, <PERSON>ton as RadixThemesButton, Card as RadixThemesCard, Container as RadixThemesContainer, DropdownMenu as RadixThemesDropdownMenu, Flex as RadixThemesFlex, Grid as RadixThemesGrid, Heading as RadixThemesHeading, Link as RadixThemesLink, Select as RadixThemesSelect, Text as RadixThemesText, TextField as RadixThemesTextField } from "@radix-ui/themes"
import { Bar<PERSON>hart as LucideBarChart, BrainCircuit as LucideBrainCircuit, ChevronDown as LucideChevronDown, ChevronLeft as LucideChevronLeft, ChevronRight as LucideChevronRight, CircleHelp as LucideCircleHelp, ClipboardList as LucideClipboardList, ClipboardPlus as LucideClipboardPlus, Home as LucideHome, Key as LucideKey, LogOut as LucideLogOut, Plus as LucidePlus, SearchX as LucideSearchX, Settings as LucideSettings, User as LucideUser, UserPlus as LucideUserPlus, Users as LucideUsers } from "lucide-react"
import { Link as ReactRouterLink } from "react-router"
import DebounceInput from "react-debounce-input"
import { jsx } from "@emotion/react"



function Button_250645882605082830299317430261560605115 () {
  
  const [addEvents, connectErrors] = useContext(EventLoopContext);


  const on_click_8af29ca2da1cf1ec587abb550c968ad4 = useCallback(((_e) => (addEvents([(Event("reflex___state____state.states___patient_state____patient_state.toggle_advanced_filters", ({  }), ({  })))], [_e], ({  })))), [addEvents, Event])



  
  return (
    jsx(
RadixThemesButton,
{css:({ ["width"] : "100%" }),onClick:on_click_8af29ca2da1cf1ec587abb550c968ad4,size:"2",variant:"ghost"},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"row",gap:"2"},
jsx(
RadixThemesText,
{as:"p"},
"Advanced Filters"
,),jsx(LucideChevronDown,{size:16},)
,),)
  )
}

function Heading_115879733140338803498759051435522474844 () {
  
  const reflex___state____state__states___patient_state____patient_state = useContext(StateContexts.reflex___state____state__states___patient_state____patient_state)





  
  return (
    jsx(
RadixThemesHeading,
{size:"5"},
("Search Results ("+reflex___state____state__states___patient_state____patient_state.total_results_rx_state_+")")
,)
  )
}

function Fragment_107022719689532843450754661406007678843 () {
  
  const reflex___state____state__states___patient_state____patient_state = useContext(StateContexts.reflex___state____state__states___patient_state____patient_state)





  
  return (
    jsx(
Fragment,
{},
((reflex___state____state__states___patient_state____patient_state.current_page_rx_state_ > 3) ? (jsx(
Fragment,
{},
jsx(
RadixThemesText,
{as:"p",css:({ ["color"] : "gray.400" })},
"..."
,),)) : (jsx(Fragment,{},)
)),)
  )
}

function Fragment_273121942571186071179949915555282975994 () {
  
  const reflex___state____state__states___auth_state____auth_state = useContext(StateContexts.reflex___state____state__states___auth_state____auth_state)





  
  return (
    jsx(
Fragment,
{},
(reflex___state____state__states___auth_state____auth_state.is_authenticated_rx_state_ ? (jsx(
Fragment,
{},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",css:({ ["width"] : "100%", ["minHeight"] : "100vh" }),direction:"column",gap:"0"},
jsx(
RadixThemesBox,
{css:({ ["width"] : "100%", ["background"] : "white", ["borderBottom"] : "1px solid", ["borderColor"] : "gray.200", ["p"] : 4, ["position"] : "sticky", ["top"] : "0", ["zIndex"] : "1000" })},
jsx(
RadixThemesContainer,
{css:({ ["padding"] : "16px", ["maxWidth"] : "1200px" }),size:"3"},
jsx(
RadixThemesFlex,
{align:"center",className:"rx-Stack",css:({ ["width"] : "100%" }),direction:"row",gap:"3"},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"row",gap:"3"},
jsx(LucideBrainCircuit,{css:({ ["color"] : "blue.600" }),size:32},)
,jsx(
RadixThemesHeading,
{css:({ ["color"] : "blue.600" }),size:"6"},
"Psychiatry EMR"
,),),jsx(Fragment_252584447231808486301477474528160783206,{},)
,jsx(RadixThemesFlex,{css:({ ["flex"] : 1, ["justifySelf"] : "stretch", ["alignSelf"] : "stretch" })},)
,jsx(Fragment_7277909197666096817838437013620370764,{},)
,),),),jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"column",gap:"2"},
jsx(Fragment_240514998957387502309164644453271310886,{},)
,jsx(Fragment_192633857668537415374399445980174592197,{},)
,jsx(Fragment_72192638570632301994735261530527745264,{},)
,jsx(Fragment_44540825102101947695183164596470730595,{},)
,),jsx(
RadixThemesBox,
{css:({ ["width"] : "100%", ["flex"] : "1" })},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",css:({ ["width"] : "100%", ["minHeight"] : "100vh", ["background"] : "gray.50" }),direction:"column",gap:"3"},
jsx(
RadixThemesBox,
{css:({ ["width"] : "100%", ["background"] : "white", ["borderBottom"] : "1px solid", ["borderColor"] : "gray.200", ["p"] : 4 })},
jsx(
RadixThemesContainer,
{css:({ ["padding"] : "16px", ["maxWidth"] : "1200px" }),size:"3"},
jsx(
RadixThemesFlex,
{align:"center",className:"rx-Stack",css:({ ["width"] : "100%" }),direction:"row",gap:"3"},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"row",gap:"2"},
jsx(
RadixThemesLink,
{asChild:true,css:({ ["color"] : "blue.600", ["&:hover"] : ({ ["color"] : "var(--accent-8)" }) })},
jsx(
ReactRouterLink,
{to:"/dashboard"},
"Dashboard"
,),),jsx(
RadixThemesText,
{as:"p",css:({ ["color"] : "gray.400" })},
"/"
,),jsx(
RadixThemesText,
{as:"p",css:({ ["fontWeight"] : "bold" })},
"Patient Search"
,),),jsx(RadixThemesFlex,{css:({ ["flex"] : 1, ["justifySelf"] : "stretch", ["alignSelf"] : "stretch" })},)
,jsx(
RadixThemesButton,
{color:"blue",size:"3"},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"row",gap:"2"},
jsx(LucideUserPlus,{size:20},)
,jsx(
RadixThemesText,
{as:"p"},
"New Patient"
,),),),),),),jsx(
RadixThemesContainer,
{css:({ ["padding"] : "16px", ["maxWidth"] : "1200px", ["p"] : 6 }),size:"3"},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",css:({ ["width"] : "100%" }),direction:"column",gap:"6"},
jsx(
RadixThemesCard,
{css:({ ["p"] : 6, ["width"] : "100%" })},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",css:({ ["width"] : "100%" }),direction:"column",gap:"4"},
jsx(
RadixThemesHeading,
{css:({ ["mb"] : 4 }),size:"6"},
"Patient Search"
,),jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",css:({ ["width"] : "100%" }),direction:"row",gap:"3"},
jsx(Debounceinput_186166591175677066014321021929185363474,{},)
,jsx(Button_273318296092345016609720720900236457610,{},)
,),jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",css:({ ["width"] : "100%" }),direction:"column",gap:"3"},
jsx(Button_250645882605082830299317430261560605115,{},)
,jsx(Fragment_295457311390831555063873972742213032141,{},)
,),),),jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",css:({ ["width"] : "100%" }),direction:"column",gap:"4"},
jsx(Fragment_91402133702045140175433165178162630430,{},)
,jsx(Fragment_208200330485724081150588738507393304355,{},)
,),),),),),jsx(
RadixThemesBox,
{css:({ ["width"] : "100%", ["background"] : "gray.100", ["borderTop"] : "1px solid", ["borderColor"] : "gray.200", ["p"] : 4, ["mt"] : "auto" })},
jsx(
RadixThemesContainer,
{css:({ ["padding"] : "16px", ["maxWidth"] : "1200px" }),size:"3"},
jsx(
RadixThemesFlex,
{align:"center",className:"rx-Stack",css:({ ["width"] : "100%" }),direction:"row",gap:"3"},
jsx(
RadixThemesText,
{as:"p",css:({ ["color"] : "gray.600", ["fontSize"] : "sm" })},
"\u00a9 2024 Psychiatry EMR. All rights reserved."
,),jsx(RadixThemesFlex,{css:({ ["flex"] : 1, ["justifySelf"] : "stretch", ["alignSelf"] : "stretch" })},)
,jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"row",gap:"2"},
jsx(
RadixThemesText,
{as:"p",css:({ ["color"] : "gray.500", ["fontSize"] : "xs" })},
"Version 1.0.0"
,),jsx(
RadixThemesText,
{as:"p",css:({ ["color"] : "gray.400" })},
"\u2022"
,),jsx(
RadixThemesLink,
{asChild:true,css:({ ["color"] : "gray.500", ["fontSize"] : "xs", ["&:hover"] : ({ ["color"] : "var(--accent-8)" }) })},
jsx(
ReactRouterLink,
{to:"/privacy"},
"Privacy Policy"
,),),jsx(
RadixThemesText,
{as:"p",css:({ ["color"] : "gray.400" })},
"\u2022"
,),jsx(
RadixThemesLink,
{asChild:true,css:({ ["color"] : "gray.500", ["fontSize"] : "xs", ["&:hover"] : ({ ["color"] : "var(--accent-8)" }) })},
jsx(
ReactRouterLink,
{to:"/support"},
"Support"
,),),),),),),),)) : (jsx(
Fragment,
{},
jsx(
RadixThemesFlex,
{align:"center",className:"rx-Stack",css:({ ["p"] : 8 }),direction:"column",gap:"4"},
jsx(
RadixThemesHeading,
{size:"6"},
"Authentication Required"
,),jsx(
RadixThemesText,
{as:"p"},
"Please log in to access this page."
,),jsx(Button_169816151628875222609663720048451338759,{},)
,),))),)
  )
}

function Fragment_44540825102101947695183164596470730595 () {
  
  const reflex___state____state__states___clinical_state____clinical_state = useContext(StateContexts.reflex___state____state__states___clinical_state____clinical_state)





  
  return (
    jsx(
Fragment,
{},
(isTrue(reflex___state____state__states___clinical_state____clinical_state.error_message_rx_state_) ? (jsx(
Fragment,
{},
jsx(
RadixThemesCard,
{css:({ ["position"] : "fixed", ["top"] : "80px", ["right"] : "20px", ["zIndex"] : "9999", ["maxWidth"] : "400px", ["background"] : "red.50", ["border"] : "1px solid", ["borderColor"] : "red.200" })},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"row",gap:"2"},
jsx(LucideCircleHelp,{css:({ ["color"] : "red.500" })},)
,jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"column",gap:"1"},
jsx(
RadixThemesText,
{as:"p",css:({ ["fontWeight"] : "bold" })},
"Error"
,),jsx(Text_31998917857994533249003073964684816120,{},)
,),),),)) : (jsx(Fragment,{},)
)),)
  )
}

function Fragment_91402133702045140175433165178162630430 () {
  
  const reflex___state____state__states___patient_state____patient_state = useContext(StateContexts.reflex___state____state__states___patient_state____patient_state)





  
  return (
    jsx(
Fragment,
{},
(isTrue(reflex___state____state__states___patient_state____patient_state.search_results_rx_state_) ? (jsx(
Fragment,
{},
jsx(
RadixThemesCard,
{css:({ ["p"] : 4, ["width"] : "100%" })},
jsx(
RadixThemesFlex,
{align:"center",className:"rx-Stack",css:({ ["width"] : "100%" }),direction:"row",gap:"3"},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"column",gap:"1"},
jsx(Heading_115879733140338803498759051435522474844,{},)
,jsx(Text_239667243793233529203820163219640161245,{},)
,),jsx(RadixThemesFlex,{css:({ ["flex"] : 1, ["justifySelf"] : "stretch", ["alignSelf"] : "stretch" })},)
,jsx(
RadixThemesFlex,
{align:"center",className:"rx-Stack",direction:"row",gap:"2"},
jsx(
RadixThemesText,
{as:"p",css:({ ["fontSize"] : "sm" })},
"View:"
,),jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"row",gap:"1"},
jsx(
RadixThemesButton,
{size:"2",variant:"outline"},
"List"
,),jsx(
RadixThemesButton,
{size:"2",variant:"solid"},
"Cards"
,),),),),),)) : (jsx(Fragment,{},)
)),)
  )
}

function Button_109065842529716721849461305836408355868 () {
  
  const reflex___state____state__states___patient_state____patient_state = useContext(StateContexts.reflex___state____state__states___patient_state____patient_state)
  const [addEvents, connectErrors] = useContext(EventLoopContext);


  const on_click_86e2297c3b2829806e4b15a333297210 = useCallback(((_e) => (addEvents([(Event("reflex___state____state.states___patient_state____patient_state.next_page", ({  }), ({  })))], [_e], ({  })))), [addEvents, Event])



  
  return (
    jsx(
RadixThemesButton,
{disabled:(reflex___state____state__states___patient_state____patient_state.current_page_rx_state_ === reflex___state____state__states___patient_state____patient_state.total_pages_rx_state_),onClick:on_click_86e2297c3b2829806e4b15a333297210,size:"2",variant:"outline"},
jsx(LucideChevronRight,{size:16},)
,)
  )
}

function Fragment_274477648652398825829734173108266117027 () {
  
  const reflex___state____state__states___patient_state____patient_state = useContext(StateContexts.reflex___state____state__states___patient_state____patient_state)





  
  return (
    jsx(
Fragment,
{},
((reflex___state____state__states___patient_state____patient_state.current_page_rx_state_ < reflex___state____state__states___patient_state____patient_state.total_pages_rx_state_) ? (jsx(
Fragment,
{},
jsx(Button_307876936839829093102153773170194902655,{},)
,)) : (jsx(Fragment,{},)
)),)
  )
}

function Fragment_7277909197666096817838437013620370764 () {
  
  const reflex___state____state__states___auth_state____auth_state = useContext(StateContexts.reflex___state____state__states___auth_state____auth_state)





  
  return (
    jsx(
Fragment,
{},
(reflex___state____state__states___auth_state____auth_state.is_authenticated_rx_state_ ? (jsx(
Fragment,
{},
jsx(
RadixThemesFlex,
{align:"center",className:"rx-Stack",direction:"row",gap:"2"},
jsx(
RadixThemesButton,
{size:"2",title:"New Patient",variant:"ghost"},
jsx(LucidePlus,{size:16},)
,),jsx(
RadixThemesButton,
{size:"2",title:"New Assessment",variant:"ghost"},
jsx(LucideClipboardPlus,{size:16},)
,),jsx(
RadixThemesDropdownMenu.Root,
{},
jsx(Dropdownmenu__trigger_147570026540717198363334525227418462761,{},)
,jsx(
RadixThemesDropdownMenu.Content,
{},
jsx(
RadixThemesDropdownMenu.Item,
{},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"row",gap:"2"},
jsx(LucideUser,{size:16},)
,jsx(
RadixThemesText,
{as:"p"},
"Profile"
,),),),jsx(
RadixThemesDropdownMenu.Item,
{},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"row",gap:"2"},
jsx(LucideKey,{size:16},)
,jsx(
RadixThemesText,
{as:"p"},
"Change Password"
,),),),jsx(
RadixThemesDropdownMenu.Item,
{},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"row",gap:"2"},
jsx(LucideSettings,{size:16},)
,jsx(
RadixThemesText,
{as:"p"},
"Settings"
,),),),jsx(RadixThemesDropdownMenu.Separator,{},)
,jsx(Dropdownmenu__item_53865495627125640446257350871203294164,{},)
,),),),)) : (jsx(
Fragment,
{},
jsx(Button_213432182251270348700044919999605871278,{},)
,))),)
  )
}

function Fragment_240514998957387502309164644453271310886 () {
  
  const reflex___state____state__states___patient_state____patient_state = useContext(StateContexts.reflex___state____state__states___patient_state____patient_state)





  
  return (
    jsx(
Fragment,
{},
(isTrue(reflex___state____state__states___patient_state____patient_state.success_message_rx_state_) ? (jsx(
Fragment,
{},
jsx(
RadixThemesCard,
{css:({ ["position"] : "fixed", ["top"] : "20px", ["right"] : "20px", ["zIndex"] : "9999", ["maxWidth"] : "400px", ["background"] : "green.50", ["border"] : "1px solid", ["borderColor"] : "green.200" })},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"row",gap:"2"},
jsx(LucideCircleHelp,{css:({ ["color"] : "green.500" })},)
,jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"column",gap:"1"},
jsx(
RadixThemesText,
{as:"p",css:({ ["fontWeight"] : "bold" })},
"Success"
,),jsx(Text_48720832473938332111969035618750431193,{},)
,),),),)) : (jsx(Fragment,{},)
)),)
  )
}

function Dropdownmenu__item_53865495627125640446257350871203294164 () {
  
  const [addEvents, connectErrors] = useContext(EventLoopContext);


  const on_click_57e4c3f640fd5e0b686ad03b43a436fd = useCallback(((_e) => (addEvents([(Event("reflex___state____state.states___auth_state____auth_state.logout", ({  }), ({  })))], [_e], ({  })))), [addEvents, Event])



  
  return (
    jsx(
RadixThemesDropdownMenu.Item,
{css:({ ["color"] : "red.600" }),onClick:on_click_57e4c3f640fd5e0b686ad03b43a436fd},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"row",gap:"2"},
jsx(LucideLogOut,{size:16},)
,jsx(
RadixThemesText,
{as:"p"},
"Logout"
,),),)
  )
}

function Button_307876936839829093102153773170194902655 () {
  
  const reflex___state____state__states___patient_state____patient_state = useContext(StateContexts.reflex___state____state__states___patient_state____patient_state)
  const [addEvents, connectErrors] = useContext(EventLoopContext);


  const on_click_74c40fdefe560047e223ef2b5e016906 = useCallback(((_e) => (addEvents([(Event("reflex___state____state.states___patient_state____patient_state.go_to_page", ({ ["page"] : reflex___state____state__states___patient_state____patient_state.total_pages_rx_state_ }), ({  })))], [_e], ({  })))), [addEvents, Event, reflex___state____state__states___patient_state____patient_state])



  
  return (
    jsx(
RadixThemesButton,
{onClick:on_click_74c40fdefe560047e223ef2b5e016906,size:"2",variant:"outline"},
reflex___state____state__states___patient_state____patient_state.total_pages_rx_state_
,)
  )
}

function Text_31998917857994533249003073964684816120 () {
  
  const reflex___state____state__states___clinical_state____clinical_state = useContext(StateContexts.reflex___state____state__states___clinical_state____clinical_state)





  
  return (
    jsx(
RadixThemesText,
{as:"p"},
reflex___state____state__states___clinical_state____clinical_state.error_message_rx_state_
,)
  )
}

function Fragment_200204080443320280511938889863463545879 () {
  
  const reflex___state____state__states___patient_state____patient_state = useContext(StateContexts.reflex___state____state__states___patient_state____patient_state)





  
  return (
    jsx(
Fragment,
{},
((reflex___state____state__states___patient_state____patient_state.current_page_rx_state_ > 1) ? (jsx(
Fragment,
{},
jsx(Button_47137541927626117866499354345876475503,{},)
,)) : (jsx(Fragment,{},)
)),)
  )
}

function Text_102820030196061442617916723669586461230 () {
  
  const reflex___state____state__states___clinical_state____clinical_state = useContext(StateContexts.reflex___state____state__states___clinical_state____clinical_state)





  
  return (
    jsx(
RadixThemesText,
{as:"p"},
reflex___state____state__states___clinical_state____clinical_state.success_message_rx_state_
,)
  )
}

function Fragment_295457311390831555063873972742213032141 () {
  
  const reflex___state____state__states___patient_state____patient_state = useContext(StateContexts.reflex___state____state__states___patient_state____patient_state)





  
  return (
    jsx(
Fragment,
{},
(reflex___state____state__states___patient_state____patient_state.show_advanced_filters_rx_state_ ? (jsx(
Fragment,
{},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",css:({ ["width"] : "100%", ["p"] : 4, ["border"] : "1px solid", ["borderColor"] : "gray.200", ["borderRadius"] : "md", ["background"] : "gray.50" }),direction:"column",gap:"4"},
jsx(
RadixThemesGrid,
{columns:"4",css:({ ["width"] : "100%" }),gap:"4"},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"column",gap:"2"},
jsx(
RadixThemesText,
{as:"p",css:({ ["fontWeight"] : "bold" })},
"Age Range"
,),jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",css:({ ["width"] : "100%" }),direction:"row",gap:"2"},
jsx(RadixThemesTextField.Root,{css:({ ["width"] : "100%" }),placeholder:"Min age",type:"number"},)
,jsx(
RadixThemesText,
{as:"p"},
"to"
,),jsx(RadixThemesTextField.Root,{css:({ ["width"] : "100%" }),placeholder:"Max age",type:"number"},)
,),),jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"column",gap:"2"},
jsx(
RadixThemesText,
{as:"p",css:({ ["fontWeight"] : "bold" })},
"Gender"
,),jsx(
RadixThemesSelect.Root,
{},
jsx(RadixThemesSelect.Trigger,{css:({ ["width"] : "100%" }),placeholder:"Select gender"},)
,jsx(
RadixThemesSelect.Content,
{},
jsx(
RadixThemesSelect.Group,
{},
""
,jsx(
RadixThemesSelect.Item,
{value:"Any"},
"Any"
,),jsx(
RadixThemesSelect.Item,
{value:"Male"},
"Male"
,),jsx(
RadixThemesSelect.Item,
{value:"Female"},
"Female"
,),jsx(
RadixThemesSelect.Item,
{value:"Non-binary"},
"Non-binary"
,),jsx(
RadixThemesSelect.Item,
{value:"Other"},
"Other"
,),),),),),jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"column",gap:"2"},
jsx(
RadixThemesText,
{as:"p",css:({ ["fontWeight"] : "bold" })},
"Registration Date"
,),jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",css:({ ["width"] : "100%" }),direction:"row",gap:"2"},
jsx(RadixThemesTextField.Root,{css:({ ["width"] : "100%" }),type:"date"},)
,jsx(
RadixThemesText,
{as:"p"},
"to"
,),jsx(RadixThemesTextField.Root,{css:({ ["width"] : "100%" }),type:"date"},)
,),),jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"column",gap:"2"},
jsx(
RadixThemesText,
{as:"p",css:({ ["fontWeight"] : "bold" })},
"Status"
,),jsx(
RadixThemesSelect.Root,
{},
jsx(RadixThemesSelect.Trigger,{css:({ ["width"] : "100%" }),placeholder:"Select status"},)
,jsx(
RadixThemesSelect.Content,
{},
jsx(
RadixThemesSelect.Group,
{},
""
,jsx(
RadixThemesSelect.Item,
{value:"All"},
"All"
,),jsx(
RadixThemesSelect.Item,
{value:"Active"},
"Active"
,),jsx(
RadixThemesSelect.Item,
{value:"Inactive"},
"Inactive"
,),),),),),),jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"row",gap:"2"},
jsx(
RadixThemesButton,
{color:"blue",size:"2"},
"Apply Filters"
,),jsx(
RadixThemesButton,
{size:"2",variant:"outline"},
"Clear Filters"
,),),),)) : (jsx(Fragment,{},)
)),)
  )
}

function Debounceinput_186166591175677066014321021929185363474 () {
  
  const reflex___state____state__states___patient_state____patient_state = useContext(StateContexts.reflex___state____state__states___patient_state____patient_state)
  const [addEvents, connectErrors] = useContext(EventLoopContext);


  const on_change_624ca22a2dbf18bfd31c2f92729a1e9b = useCallback(((_e) => (addEvents([(Event("reflex___state____state.states___patient_state____patient_state.set_search_term", ({ ["value"] : _e["target"]["value"] }), ({  })))], [_e], ({  })))), [addEvents, Event])



  
  return (
    jsx(DebounceInput,{css:({ ["width"] : "100%" }),debounceTimeout:300,element:RadixThemesTextField.Root,onChange:on_change_624ca22a2dbf18bfd31c2f92729a1e9b,placeholder:"Search by name, phone, email, or patient ID...",size:"3",value:(isNotNullOrUndefined(reflex___state____state__states___patient_state____patient_state.search_term_rx_state_) ? reflex___state____state__states___patient_state____patient_state.search_term_rx_state_ : "")},)

  )
}

function Button_14200668258908739601884915054220204532 () {
  
  const reflex___state____state__states___patient_state____patient_state = useContext(StateContexts.reflex___state____state__states___patient_state____patient_state)
  const [addEvents, connectErrors] = useContext(EventLoopContext);


  const on_click_4be308893183c5f3acec8e3659df89a5 = useCallback(((_e) => (addEvents([(Event("reflex___state____state.states___patient_state____patient_state.prev_page", ({  }), ({  })))], [_e], ({  })))), [addEvents, Event])



  
  return (
    jsx(
RadixThemesButton,
{disabled:(reflex___state____state__states___patient_state____patient_state.current_page_rx_state_ === 1),onClick:on_click_4be308893183c5f3acec8e3659df89a5,size:"2",variant:"outline"},
jsx(LucideChevronLeft,{size:16},)
,)
  )
}

function Fragment_252584447231808486301477474528160783206 () {
  
  const reflex___state____state__states___auth_state____auth_state = useContext(StateContexts.reflex___state____state__states___auth_state____auth_state)





  
  return (
    jsx(
Fragment,
{},
(reflex___state____state__states___auth_state____auth_state.is_authenticated_rx_state_ ? (jsx(
Fragment,
{},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"row",gap:"6"},
jsx(
RadixThemesLink,
{asChild:true,css:({ ["color"] : "gray.700", ["fontWeight"] : "medium", ["&:hover"] : ({ ["color"] : "blue.600" }), ["&:active"] : ({ ["color"] : "blue.600", ["fontWeight"] : "bold" }) })},
jsx(
ReactRouterLink,
{to:"/dashboard"},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"row",gap:"2"},
jsx(LucideHome,{size:18},)
,jsx(
RadixThemesText,
{as:"p"},
"Dashboard"
,),),),),jsx(
RadixThemesLink,
{asChild:true,css:({ ["color"] : "gray.700", ["fontWeight"] : "medium", ["&:hover"] : ({ ["color"] : "blue.600" }), ["&:active"] : ({ ["color"] : "blue.600", ["fontWeight"] : "bold" }) })},
jsx(
ReactRouterLink,
{to:"/patients"},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"row",gap:"2"},
jsx(LucideUsers,{size:18},)
,jsx(
RadixThemesText,
{as:"p"},
"Patients"
,),),),),jsx(
RadixThemesLink,
{asChild:true,css:({ ["color"] : "gray.700", ["fontWeight"] : "medium", ["&:hover"] : ({ ["color"] : "blue.600" }), ["&:active"] : ({ ["color"] : "blue.600", ["fontWeight"] : "bold" }) })},
jsx(
ReactRouterLink,
{to:"/assessments"},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"row",gap:"2"},
jsx(LucideClipboardList,{size:18},)
,jsx(
RadixThemesText,
{as:"p"},
"Assessments"
,),),),),jsx(
RadixThemesLink,
{asChild:true,css:({ ["color"] : "gray.700", ["fontWeight"] : "medium", ["&:hover"] : ({ ["color"] : "blue.600" }), ["&:active"] : ({ ["color"] : "blue.600", ["fontWeight"] : "bold" }) })},
jsx(
ReactRouterLink,
{to:"/reports"},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"row",gap:"2"},
jsx(LucideBarChart,{size:18},)
,jsx(
RadixThemesText,
{as:"p"},
"Reports"
,),),),),jsx(Fragment_166734300427242336168950855614206449165,{},)
,),)) : (jsx(Fragment,{},)
)),)
  )
}

function Fragment_166734300427242336168950855614206449165 () {
  
  const reflex___state____state__states___auth_state____auth_state = useContext(StateContexts.reflex___state____state__states___auth_state____auth_state)





  
  return (
    jsx(
Fragment,
{},
(reflex___state____state__states___auth_state____auth_state.is_admin_rx_state_ ? (jsx(
Fragment,
{},
jsx(
RadixThemesLink,
{asChild:true,css:({ ["color"] : "gray.700", ["fontWeight"] : "medium", ["&:hover"] : ({ ["color"] : "blue.600" }), ["&:active"] : ({ ["color"] : "blue.600", ["fontWeight"] : "bold" }) })},
jsx(
ReactRouterLink,
{to:"/admin"},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"row",gap:"2"},
jsx(LucideSettings,{size:18},)
,jsx(
RadixThemesText,
{as:"p"},
"Admin"
,),),),),)) : (jsx(Fragment,{},)
)),)
  )
}

function Button_47137541927626117866499354345876475503 () {
  
  const [addEvents, connectErrors] = useContext(EventLoopContext);


  const on_click_c1ba250a037a7e813c2535216b0707b1 = useCallback(((_e) => (addEvents([(Event("reflex___state____state.states___patient_state____patient_state.go_to_page", ({ ["page"] : 1 }), ({  })))], [_e], ({  })))), [addEvents, Event])



  
  return (
    jsx(
RadixThemesButton,
{onClick:on_click_c1ba250a037a7e813c2535216b0707b1,size:"2",variant:"outline"},
"1"
,)
  )
}

function Fragment_192633857668537415374399445980174592197 () {
  
  const reflex___state____state__states___patient_state____patient_state = useContext(StateContexts.reflex___state____state__states___patient_state____patient_state)





  
  return (
    jsx(
Fragment,
{},
(isTrue(reflex___state____state__states___patient_state____patient_state.error_message_rx_state_) ? (jsx(
Fragment,
{},
jsx(
RadixThemesCard,
{css:({ ["position"] : "fixed", ["top"] : "20px", ["right"] : "20px", ["zIndex"] : "9999", ["maxWidth"] : "400px", ["background"] : "red.50", ["border"] : "1px solid", ["borderColor"] : "red.200" })},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"row",gap:"2"},
jsx(LucideCircleHelp,{css:({ ["color"] : "red.500" })},)
,jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"column",gap:"1"},
jsx(
RadixThemesText,
{as:"p",css:({ ["fontWeight"] : "bold" })},
"Error"
,),jsx(Text_230921353667065509099663485155314641824,{},)
,),),),)) : (jsx(Fragment,{},)
)),)
  )
}

function Fragment_208200330485724081150588738507393304355 () {
  
  const reflex___state____state__states___patient_state____patient_state = useContext(StateContexts.reflex___state____state__states___patient_state____patient_state)





  
  return (
    jsx(
Fragment,
{},
(isTrue(reflex___state____state__states___patient_state____patient_state.search_results_rx_state_) ? (jsx(
Fragment,
{},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",css:({ ["width"] : "100%" }),direction:"column",gap:"4"},
jsx(Grid_114489229801385025873654506791325377970,{},)
,jsx(
RadixThemesCard,
{css:({ ["p"] : 3, ["width"] : "100%" })},
jsx(
RadixThemesFlex,
{align:"center",className:"rx-Stack",css:({ ["width"] : "100%" }),direction:"row",gap:"3"},
jsx(Text_171655074574155237545678771044969612209,{},)
,jsx(RadixThemesFlex,{css:({ ["flex"] : 1, ["justifySelf"] : "stretch", ["alignSelf"] : "stretch" })},)
,jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"row",gap:"2"},
jsx(Button_14200668258908739601884915054220204532,{},)
,jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"row",gap:"1"},
jsx(Fragment_200204080443320280511938889863463545879,{},)
,jsx(Fragment_107022719689532843450754661406007678843,{},)
,jsx(Button_220799490951400676548463193946179152981,{},)
,jsx(Fragment_185792109825401676856619397376568346899,{},)
,jsx(Fragment_274477648652398825829734173108266117027,{},)
,),jsx(Button_109065842529716721849461305836408355868,{},)
,),),),),)) : (jsx(
Fragment,
{},
jsx(
RadixThemesCard,
{css:({ ["p"] : 8, ["width"] : "100%", ["textAlign"] : "center" })},
jsx(
RadixThemesFlex,
{align:"center",className:"rx-Stack",direction:"column",gap:"3"},
jsx(LucideSearchX,{css:({ ["color"] : "gray.400" }),size:48},)
,jsx(
RadixThemesHeading,
{css:({ ["color"] : "gray.600" }),size:"5"},
"No patients found"
,),jsx(
RadixThemesText,
{as:"p",css:({ ["color"] : "gray.500" })},
"Try adjusting your search terms or filters"
,),jsx(
RadixThemesButton,
{color:"blue",css:({ ["mt"] : 4 }),size:"3"},
"Create New Patient"
,),),),))),)
  )
}

function Text_48720832473938332111969035618750431193 () {
  
  const reflex___state____state__states___patient_state____patient_state = useContext(StateContexts.reflex___state____state__states___patient_state____patient_state)





  
  return (
    jsx(
RadixThemesText,
{as:"p"},
reflex___state____state__states___patient_state____patient_state.success_message_rx_state_
,)
  )
}

function Text_171655074574155237545678771044969612209 () {
  
  const reflex___state____state__states___patient_state____patient_state = useContext(StateContexts.reflex___state____state__states___patient_state____patient_state)





  
  return (
    jsx(
RadixThemesText,
{as:"p",css:({ ["color"] : "gray.600" })},
("Page "+reflex___state____state__states___patient_state____patient_state.current_page_rx_state_+" of "+reflex___state____state__states___patient_state____patient_state.total_pages_rx_state_)
,)
  )
}

function Fragment_72192638570632301994735261530527745264 () {
  
  const reflex___state____state__states___clinical_state____clinical_state = useContext(StateContexts.reflex___state____state__states___clinical_state____clinical_state)





  
  return (
    jsx(
Fragment,
{},
(isTrue(reflex___state____state__states___clinical_state____clinical_state.success_message_rx_state_) ? (jsx(
Fragment,
{},
jsx(
RadixThemesCard,
{css:({ ["position"] : "fixed", ["top"] : "80px", ["right"] : "20px", ["zIndex"] : "9999", ["maxWidth"] : "400px", ["background"] : "green.50", ["border"] : "1px solid", ["borderColor"] : "green.200" })},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"row",gap:"2"},
jsx(LucideCircleHelp,{css:({ ["color"] : "green.500" })},)
,jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"column",gap:"1"},
jsx(
RadixThemesText,
{as:"p",css:({ ["fontWeight"] : "bold" })},
"Success"
,),jsx(Text_102820030196061442617916723669586461230,{},)
,),),),)) : (jsx(Fragment,{},)
)),)
  )
}

function Dropdownmenu__trigger_147570026540717198363334525227418462761 () {
  
  const reflex___state____state__states___auth_state____auth_state = useContext(StateContexts.reflex___state____state__states___auth_state____auth_state)





  
  return (
    jsx(
RadixThemesDropdownMenu.Trigger,
{},
jsx(
RadixThemesButton,
{size:"2",variant:"ghost"},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"row",gap:"2"},
(isTrue(reflex___state____state__states___auth_state____auth_state.current_user_rx_state_) ? reflex___state____state__states___auth_state____auth_state.current_user_rx_state_?.["full_name"] : "User")
,jsx(LucideChevronDown,{size:16},)
,),),)
  )
}

function Button_169816151628875222609663720048451338759 () {
  
  const [addEvents, connectErrors] = useContext(EventLoopContext);


  const on_click_e702a5da1364a6fc9edb845ecb9a4cfa = useCallback(((_e) => (addEvents([(Event("_redirect", ({ ["path"] : "/login", ["external"] : false, ["replace"] : false }), ({  })))], [_e], ({  })))), [addEvents, Event])



  
  return (
    jsx(
RadixThemesButton,
{color:"blue",onClick:on_click_e702a5da1364a6fc9edb845ecb9a4cfa},
"Go to Login"
,)
  )
}

function Text_239667243793233529203820163219640161245 () {
  
  const reflex___state____state__states___patient_state____patient_state = useContext(StateContexts.reflex___state____state__states___patient_state____patient_state)





  
  return (
    jsx(
RadixThemesText,
{as:"p",css:({ ["color"] : "gray.600" })},
("Showing page "+reflex___state____state__states___patient_state____patient_state.current_page_rx_state_+" of "+reflex___state____state__states___patient_state____patient_state.total_pages_rx_state_)
,)
  )
}

function Grid_114489229801385025873654506791325377970 () {
  
  const reflex___state____state__states___patient_state____patient_state = useContext(StateContexts.reflex___state____state__states___patient_state____patient_state)
  const [addEvents, connectErrors] = useContext(EventLoopContext);





  
  return (
    jsx(
RadixThemesGrid,
{columns:"1",css:({ ["width"] : "100%" }),gap:"4"},
reflex___state____state__states___patient_state____patient_state.search_results_rx_state_.map((patient_rx_state_,index_d0e34536384a44ee)=>(jsx(
RadixThemesCard,
{css:({ ["p"] : 4, ["width"] : "100%", ["&:hover"] : ({ ["shadow"] : "md" }) }),key:index_d0e34536384a44ee},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",css:({ ["width"] : "100%" }),direction:"row",gap:"4"},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",css:({ ["flex"] : "1" }),direction:"column",gap:"2"},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"row",gap:"3"},
jsx(
RadixThemesHeading,
{size:"5"},
patient_rx_state_["name"]
,),jsx(
RadixThemesBadge,
{color:"blue",size:"1"},
("ID: "+patient_rx_state_["id"])
,),),jsx(
RadixThemesGrid,
{columns:"2",css:({ ["width"] : "100%" }),gap:"2"},
jsx(
Fragment,
{},
(isTrue(patient_rx_state_["dob"]) ? (jsx(
Fragment,
{},
jsx(
RadixThemesText,
{as:"p",css:({ ["color"] : "gray.600" })},
("DOB: "+patient_rx_state_["dob"])
,),)) : (jsx(
Fragment,
{},
jsx(
RadixThemesText,
{as:"p",css:({ ["color"] : "gray.600" })},
"DOB: Not provided"
,),))),),jsx(
Fragment,
{},
(isTrue(patient_rx_state_["phone"]) ? (jsx(
Fragment,
{},
jsx(
RadixThemesText,
{as:"p",css:({ ["color"] : "gray.600" })},
("Phone: "+patient_rx_state_["phone"])
,),)) : (jsx(
Fragment,
{},
jsx(
RadixThemesText,
{as:"p",css:({ ["color"] : "gray.600" })},
"Phone: Not provided"
,),))),),jsx(
Fragment,
{},
(isTrue(patient_rx_state_["email"]) ? (jsx(
Fragment,
{},
jsx(
RadixThemesText,
{as:"p",css:({ ["color"] : "gray.600" })},
("Email: "+patient_rx_state_["email"])
,),)) : (jsx(
Fragment,
{},
jsx(
RadixThemesText,
{as:"p",css:({ ["color"] : "gray.600" })},
"Email: Not provided"
,),))),),jsx(
Fragment,
{},
(isTrue(patient_rx_state_["created_at"]) ? (jsx(
Fragment,
{},
jsx(
RadixThemesText,
{as:"p",css:({ ["color"] : "gray.600" })},
("Created: "+patient_rx_state_["created_at"])
,),)) : (jsx(
Fragment,
{},
jsx(
RadixThemesText,
{as:"p",css:({ ["color"] : "gray.600" })},
"Created: Not provided"
,),))),),),),jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",css:({ ["minWidth"] : "150px" }),direction:"column",gap:"2"},
jsx(
RadixThemesButton,
{color:"blue",css:({ ["width"] : "100%" }),onClick:((_e) => (addEvents([(Event("reflex___state____state.states___patient_state____patient_state.load_patient", ({ ["patient_id"] : patient_rx_state_["id"] }), ({  })))], [_e], ({  })))),size:"2"},
"View Details"
,),jsx(
RadixThemesButton,
{color:"green",css:({ ["width"] : "100%" }),size:"2"},
"New Assessment"
,),jsx(
RadixThemesButton,
{color:"gray",css:({ ["width"] : "100%" }),size:"2"},
"Edit Patient"
,),),),))),)
  )
}

function Text_230921353667065509099663485155314641824 () {
  
  const reflex___state____state__states___patient_state____patient_state = useContext(StateContexts.reflex___state____state__states___patient_state____patient_state)





  
  return (
    jsx(
RadixThemesText,
{as:"p"},
reflex___state____state__states___patient_state____patient_state.error_message_rx_state_
,)
  )
}

function Fragment_185792109825401676856619397376568346899 () {
  
  const reflex___state____state__states___patient_state____patient_state = useContext(StateContexts.reflex___state____state__states___patient_state____patient_state)





  
  return (
    jsx(
Fragment,
{},
((reflex___state____state__states___patient_state____patient_state.current_page_rx_state_ < (reflex___state____state__states___patient_state____patient_state.total_pages_rx_state_ - 2)) ? (jsx(
Fragment,
{},
jsx(
RadixThemesText,
{as:"p",css:({ ["color"] : "gray.400" })},
"..."
,),)) : (jsx(Fragment,{},)
)),)
  )
}

function Button_220799490951400676548463193946179152981 () {
  
  const reflex___state____state__states___patient_state____patient_state = useContext(StateContexts.reflex___state____state__states___patient_state____patient_state)





  
  return (
    jsx(
RadixThemesButton,
{color:"blue",size:"2"},
reflex___state____state__states___patient_state____patient_state.current_page_rx_state_
,)
  )
}

function Button_213432182251270348700044919999605871278 () {
  
  const [addEvents, connectErrors] = useContext(EventLoopContext);


  const on_click_e702a5da1364a6fc9edb845ecb9a4cfa = useCallback(((_e) => (addEvents([(Event("_redirect", ({ ["path"] : "/login", ["external"] : false, ["replace"] : false }), ({  })))], [_e], ({  })))), [addEvents, Event])



  
  return (
    jsx(
RadixThemesButton,
{color:"blue",onClick:on_click_e702a5da1364a6fc9edb845ecb9a4cfa,size:"2"},
"Login"
,)
  )
}

function Button_273318296092345016609720720900236457610 () {
  
  const reflex___state____state__states___patient_state____patient_state = useContext(StateContexts.reflex___state____state__states___patient_state____patient_state)
  const [addEvents, connectErrors] = useContext(EventLoopContext);


  const on_click_14984ba133eefccbbc7192e1aa59461b = useCallback(((_e) => (addEvents([(Event("reflex___state____state.states___patient_state____patient_state.search_patients", ({  }), ({  })))], [_e], ({  })))), [addEvents, Event])



  
  return (
    jsx(
RadixThemesButton,
{color:"blue",loading:reflex___state____state__states___patient_state____patient_state.is_loading_rx_state_,onClick:on_click_14984ba133eefccbbc7192e1aa59461b,size:"3"},
"Search"
,)
  )
}

export default function Component() {
    




  return (
    jsx(
Fragment,
{},
jsx(Fragment_273121942571186071179949915555282975994,{},)
,jsx(
"title",
{},
"Patient Search - Psychiatry EMR"
,),jsx("meta",{content:"favicon.ico",property:"og:image"},)
,)
  )
}
