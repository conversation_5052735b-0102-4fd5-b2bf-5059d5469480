import{w,a as c,v as i}from"./chunk-QMGIS6GS-suYYFPSk.js";import{f as p,E as z,c as S}from"./state-B1hYtTsq.js";import{B as x,F as N,a as b,b as v,c as C,d as j,e as P,U as W,g as R,h as A,i as F,j as E,f as L}from"./stateful_components-_envzdXf.js";import{j as s}from"./emotion-react.browser.esm-BNSIgtcs.js";import{ah as f,a3 as g,ae as n,ai as M,aj as B,ak as T,al as h,am as $,W as u,ab as U}from"./createLucideIcon-BOfs0RvG.js";import{U as D}from"./user-plus-BRYdVFIJ.js";import{C as m}from"./circle-CJ1s961q.js";import{c as t,r,o as l}from"./button-Ccwu8jNm.js";import{p as d}from"./container-Do-LxYxS.js";import{o}from"./card-BFhh40Od.js";import{p as a}from"./text-DCkbNTq3.js";import"./jsx-runtime-D_zvdyIk.js";import"./index-XOwJfM4g.js";/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const I=[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]],H=f("clock",I);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const V=[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]],J=f("search",V);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const q=[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]],G=f("trending-up",q);function Q(){const e=c.useContext(p.reflex___state____state__states___auth_state____auth_state);return s(a,{as:"p",css:{fontWeight:"medium"}},"Welcome, "+e.current_user_rx_state_?.full_name)}function Y(){const[e,_]=c.useContext(z),y=c.useCallback(k=>e([S("reflex___state____state.states___auth_state____auth_state.logout",{},{})],[k],{}),[e,S]);return s(h,{css:{color:"red.600"},onClick:y},"Logout")}function K(){const e=c.useContext(p.reflex___state____state__states___auth_state____auth_state);return s(U,{color:"blue",size:"3"},e.current_user_rx_state_?.role.split(" ").map(_=>_.charAt(0).toUpperCase()+_.slice(1).toLowerCase()).join(" "))}function O(){const e=c.useContext(p.reflex___state____state__states___auth_state____auth_state);return s(c.Fragment,{},e.is_authenticated_rx_state_?s(c.Fragment,{},s(t,{align:"start",className:"rx-Stack",css:{width:"100%",minHeight:"100vh"},direction:"column",gap:"0"},s(g,{css:{width:"100%",background:"white",borderBottom:"1px solid",borderColor:"gray.200",p:4,position:"sticky",top:"0",zIndex:"1000"}},s(d,{css:{padding:"16px",maxWidth:"1200px"},size:"3"},s(t,{align:"center",className:"rx-Stack",css:{width:"100%"},direction:"row",gap:"3"},s(t,{align:"start",className:"rx-Stack",direction:"row",gap:"3"},s(x,{css:{color:"blue.600"},size:32}),s(r,{css:{color:"blue.600"},size:"6"},"Psychiatry EMR")),s(N,{}),s(t,{css:{flex:1,justifySelf:"stretch",alignSelf:"stretch"}}),s(b,{})))),s(t,{align:"start",className:"rx-Stack",direction:"column",gap:"2"},s(v,{}),s(C,{}),s(j,{}),s(P,{})),s(g,{css:{width:"100%",flex:"1"}},s(t,{align:"start",className:"rx-Stack",css:{width:"100%",minHeight:"100vh",background:"gray.50"},direction:"column",gap:"3"},s(g,{css:{width:"100%",background:"white",borderBottom:"1px solid",borderColor:"gray.200",p:4}},s(d,{css:{padding:"16px",maxWidth:"1200px"},size:"3"},s(t,{align:"center",className:"rx-Stack",css:{width:"100%"},direction:"row",gap:"3"},s(t,{align:"start",className:"rx-Stack",direction:"row",gap:"3"},s(x,{css:{color:"blue.600"},size:32}),s(r,{css:{color:"blue.600"},size:"6"},"Psychiatry EMR")),s(t,{align:"start",className:"rx-Stack",direction:"row",gap:"6"},s(n,{asChild:!0,css:{fontWeight:"bold","&:hover":{color:"var(--accent-8)"}}},s(i,{to:"/dashboard"},"Dashboard")),s(n,{asChild:!0,css:{"&:hover":{color:"var(--accent-8)"}}},s(i,{to:"/patients"},"Patients")),s(n,{asChild:!0,css:{"&:hover":{color:"var(--accent-8)"}}},s(i,{to:"/assessments"},"Assessments")),s(n,{asChild:!0,css:{"&:hover":{color:"var(--accent-8)"}}},s(i,{to:"/reports"},"Reports"))),s(t,{css:{flex:1,justifySelf:"stretch",alignSelf:"stretch"}}),s(t,{align:"start",className:"rx-Stack",direction:"row",gap:"3"},s(Q,{}),s(M,{},s(B,{},s(l,{size:"2",variant:"ghost"},s(W,{size:20}))),s(T,{},s(h,{},"Profile Settings"),s(h,{},"Change Password"),s($,{}),s(Y,{}))))))),s(d,{css:{padding:"16px",maxWidth:"1200px",p:6},size:"3"},s(t,{align:"start",className:"rx-Stack",css:{width:"100%"},direction:"column",gap:"6"},s(o,{css:{width:"100%",p:6,background:"gradient-to-r from-blue-50 to-purple-50"}},s(t,{align:"start",className:"rx-Stack",direction:"column",gap:"3"},s(t,{align:"center",className:"rx-Stack",css:{width:"100%"},direction:"row",gap:"3"},s(t,{align:"start",className:"rx-Stack",direction:"column",gap:"1"},s(X,{}),s(a,{as:"p",css:{color:"gray.600"}},"Today is Tuesday, July 29, 2025")),s(t,{css:{flex:1,justifySelf:"stretch",alignSelf:"stretch"}}),s(t,{align:"end",className:"rx-Stack",direction:"column",gap:"1"},s(a,{as:"p",css:{fontSize:"sm",color:"gray.500"}},"Your Role"),s(K,{}))))),s(u,{columns:"4",css:{width:"100%"},gap:"4"},s(o,{css:{p:4,width:"100%"}},s(t,{align:"start",className:"rx-Stack",direction:"column",gap:"3"},s(t,{align:"start",className:"rx-Stack",css:{width:"100%"},direction:"row",gap:"3"},s(t,{align:"start",className:"rx-Stack",direction:"column",gap:"1"},s(a,{as:"p",css:{fontSize:"sm",color:"gray.600"}},"Total Patients"),s(r,{size:"6"},"1,247")),s(t,{css:{flex:1,justifySelf:"stretch",alignSelf:"stretch"}}),s(R,{css:{color:"blue.500"},size:24})),s(a,{as:"p",css:{fontSize:"sm",color:"blue.600"}},"+12 this month"))),s(o,{css:{p:4,width:"100%"}},s(t,{align:"start",className:"rx-Stack",direction:"column",gap:"3"},s(t,{align:"start",className:"rx-Stack",css:{width:"100%"},direction:"row",gap:"3"},s(t,{align:"start",className:"rx-Stack",direction:"column",gap:"1"},s(a,{as:"p",css:{fontSize:"sm",color:"gray.600"}},"Active Assessments"),s(r,{size:"6"},"89")),s(t,{css:{flex:1,justifySelf:"stretch",alignSelf:"stretch"}}),s(A,{css:{color:"green.500"},size:24})),s(a,{as:"p",css:{fontSize:"sm",color:"green.600"}},"+5 today"))),s(o,{css:{p:4,width:"100%"}},s(t,{align:"start",className:"rx-Stack",direction:"column",gap:"3"},s(t,{align:"start",className:"rx-Stack",css:{width:"100%"},direction:"row",gap:"3"},s(t,{align:"start",className:"rx-Stack",direction:"column",gap:"1"},s(a,{as:"p",css:{fontSize:"sm",color:"gray.600"}},"Pending Reviews"),s(r,{size:"6"},"23")),s(t,{css:{flex:1,justifySelf:"stretch",alignSelf:"stretch"}}),s(H,{css:{color:"yellow.500"},size:24})),s(a,{as:"p",css:{fontSize:"sm",color:"yellow.600"}},"-3 from yesterday"))),s(o,{css:{p:4,width:"100%"}},s(t,{align:"start",className:"rx-Stack",direction:"column",gap:"3"},s(t,{align:"start",className:"rx-Stack",css:{width:"100%"},direction:"row",gap:"3"},s(t,{align:"start",className:"rx-Stack",direction:"column",gap:"1"},s(a,{as:"p",css:{fontSize:"sm",color:"gray.600"}},"This Month"),s(r,{size:"6"},"156")),s(t,{css:{flex:1,justifySelf:"stretch",alignSelf:"stretch"}}),s(G,{css:{color:"purple.500"},size:24})),s(a,{as:"p",css:{fontSize:"sm",color:"purple.600"}},"Assessments completed")))),s(u,{columns:"2",css:{width:"100%"},gap:"6"},s(o,{css:{p:4,width:"100%"}},s(t,{align:"start",className:"rx-Stack",direction:"column",gap:"4"},s(t,{align:"start",className:"rx-Stack",css:{width:"100%"},direction:"row",gap:"3"},s(r,{size:"5"},"Recent Patients"),s(t,{css:{flex:1,justifySelf:"stretch",alignSelf:"stretch"}}),s(n,{asChild:!0,css:{color:"blue.600","&:hover":{color:"var(--accent-8)"}}},s(i,{to:"/patients"},"View All"))),s(t,{align:"start",className:"rx-Stack",css:{width:"100%"},direction:"column",gap:"2"},s(t,{align:"start",className:"rx-Stack",css:{width:"100%",p:2,borderRadius:"md","&:hover":{background:"gray.50"}},direction:"row",gap:"3"},s(t,{align:"start",className:"rx-Stack",direction:"column",gap:"1"},s(a,{as:"p",css:{fontWeight:"medium"}},"Sarah Johnson"),s(a,{as:"p",css:{fontSize:"sm",color:"gray.600"}},"Follow-up scheduled")),s(t,{css:{flex:1,justifySelf:"stretch",alignSelf:"stretch"}}),s(a,{as:"p",css:{fontSize:"sm",color:"gray.500"}},"2024-01-15")),s(t,{align:"start",className:"rx-Stack",css:{width:"100%",p:2,borderRadius:"md","&:hover":{background:"gray.50"}},direction:"row",gap:"3"},s(t,{align:"start",className:"rx-Stack",direction:"column",gap:"1"},s(a,{as:"p",css:{fontWeight:"medium"}},"Michael Chen"),s(a,{as:"p",css:{fontSize:"sm",color:"gray.600"}},"Assessment completed")),s(t,{css:{flex:1,justifySelf:"stretch",alignSelf:"stretch"}}),s(a,{as:"p",css:{fontSize:"sm",color:"gray.500"}},"2024-01-14")),s(t,{align:"start",className:"rx-Stack",css:{width:"100%",p:2,borderRadius:"md","&:hover":{background:"gray.50"}},direction:"row",gap:"3"},s(t,{align:"start",className:"rx-Stack",direction:"column",gap:"1"},s(a,{as:"p",css:{fontWeight:"medium"}},"Emily Davis"),s(a,{as:"p",css:{fontSize:"sm",color:"gray.600"}},"New patient intake")),s(t,{css:{flex:1,justifySelf:"stretch",alignSelf:"stretch"}}),s(a,{as:"p",css:{fontSize:"sm",color:"gray.500"}},"2024-01-13")),s(t,{align:"start",className:"rx-Stack",css:{width:"100%",p:2,borderRadius:"md","&:hover":{background:"gray.50"}},direction:"row",gap:"3"},s(t,{align:"start",className:"rx-Stack",direction:"column",gap:"1"},s(a,{as:"p",css:{fontWeight:"medium"}},"Robert Wilson"),s(a,{as:"p",css:{fontSize:"sm",color:"gray.600"}},"Treatment plan updated")),s(t,{css:{flex:1,justifySelf:"stretch",alignSelf:"stretch"}}),s(a,{as:"p",css:{fontSize:"sm",color:"gray.500"}},"2024-01-12")),s(t,{align:"start",className:"rx-Stack",css:{width:"100%",p:2,borderRadius:"md","&:hover":{background:"gray.50"}},direction:"row",gap:"3"},s(t,{align:"start",className:"rx-Stack",direction:"column",gap:"1"},s(a,{as:"p",css:{fontWeight:"medium"}},"Lisa Anderson"),s(a,{as:"p",css:{fontSize:"sm",color:"gray.600"}},"Medication review")),s(t,{css:{flex:1,justifySelf:"stretch",alignSelf:"stretch"}}),s(a,{as:"p",css:{fontSize:"sm",color:"gray.500"}},"2024-01-11"))))),s(o,{css:{p:4,width:"100%"}},s(t,{align:"start",className:"rx-Stack",direction:"column",gap:"4"},s(r,{css:{mb:4},size:"5"},"Quick Actions"),s(t,{align:"start",className:"rx-Stack",css:{width:"100%"},direction:"column",gap:"3"},s(l,{color:"blue",css:{width:"100%",justify:"start"},size:"3"},s(t,{align:"start",className:"rx-Stack",direction:"row",gap:"2"},s(D,{size:20}),s(a,{as:"p"},"New Patient"))),s(l,{color:"green",css:{width:"100%",justify:"start"},size:"3"},s(t,{align:"start",className:"rx-Stack",direction:"row",gap:"2"},s(F,{size:20}),s(a,{as:"p"},"New Assessment"))),s(l,{color:"purple",css:{width:"100%",justify:"start"},size:"3"},s(t,{align:"start",className:"rx-Stack",direction:"row",gap:"2"},s(J,{size:20}),s(a,{as:"p"},"Search Patients"))),s(l,{color:"orange",css:{width:"100%",justify:"start"},size:"3"},s(t,{align:"start",className:"rx-Stack",direction:"row",gap:"2"},s(E,{size:20}),s(a,{as:"p"},"View Reports"))))))),s(o,{css:{p:4,width:"100%",background:"gray.50"}},s(t,{align:"start",className:"rx-Stack",direction:"column",gap:"4"},s(r,{css:{mb:4},size:"5"},"System Status"),s(u,{columns:"4",css:{width:"100%"},gap:"4"},s(t,{align:"center",className:"rx-Stack",direction:"row",gap:"2"},s(m,{css:{color:"green.500"},size:12}),s(t,{align:"start",className:"rx-Stack",direction:"column",gap:"0"},s(a,{as:"p",css:{fontWeight:"medium",fontSize:"sm"}},"Database"),s(a,{as:"p",css:{fontSize:"xs",color:"gray.600"}},"Connected"))),s(t,{align:"center",className:"rx-Stack",direction:"row",gap:"2"},s(m,{css:{color:"green.500"},size:12}),s(t,{align:"start",className:"rx-Stack",direction:"column",gap:"0"},s(a,{as:"p",css:{fontWeight:"medium",fontSize:"sm"}},"Encryption"),s(a,{as:"p",css:{fontSize:"xs",color:"gray.600"}},"Active"))),s(t,{align:"center",className:"rx-Stack",direction:"row",gap:"2"},s(m,{css:{color:"green.500"},size:12}),s(t,{align:"start",className:"rx-Stack",direction:"column",gap:"0"},s(a,{as:"p",css:{fontWeight:"medium",fontSize:"sm"}},"Backup"),s(a,{as:"p",css:{fontSize:"xs",color:"gray.600"}},"Last: 2 hours ago"))),s(t,{align:"center",className:"rx-Stack",direction:"row",gap:"2"},s(m,{css:{color:"green.500"},size:12}),s(t,{align:"start",className:"rx-Stack",direction:"column",gap:"0"},s(a,{as:"p",css:{fontWeight:"medium",fontSize:"sm"}},"Security"),s(a,{as:"p",css:{fontSize:"xs",color:"gray.600"}},"All systems normal")))))))))),s(g,{css:{width:"100%",background:"gray.100",borderTop:"1px solid",borderColor:"gray.200",p:4,mt:"auto"}},s(d,{css:{padding:"16px",maxWidth:"1200px"},size:"3"},s(t,{align:"center",className:"rx-Stack",css:{width:"100%"},direction:"row",gap:"3"},s(a,{as:"p",css:{color:"gray.600",fontSize:"sm"}},"© 2024 Psychiatry EMR. All rights reserved."),s(t,{css:{flex:1,justifySelf:"stretch",alignSelf:"stretch"}}),s(t,{align:"start",className:"rx-Stack",direction:"row",gap:"2"},s(a,{as:"p",css:{color:"gray.500",fontSize:"xs"}},"Version 1.0.0"),s(a,{as:"p",css:{color:"gray.400"}},"•"),s(n,{asChild:!0,css:{color:"gray.500",fontSize:"xs","&:hover":{color:"var(--accent-8)"}}},s(i,{to:"/privacy"},"Privacy Policy")),s(a,{as:"p",css:{color:"gray.400"}},"•"),s(n,{asChild:!0,css:{color:"gray.500",fontSize:"xs","&:hover":{color:"var(--accent-8)"}}},s(i,{to:"/support"},"Support")))))))):s(c.Fragment,{},s(t,{align:"center",className:"rx-Stack",css:{p:8},direction:"column",gap:"4"},s(r,{size:"6"},"Authentication Required"),s(a,{as:"p"},"Please log in to access this page."),s(L,{}))))}function X(){const e=c.useContext(p.reflex___state____state__states___auth_state____auth_state);return s(r,{size:"6"},"Good evening, "+e.current_user_rx_state_?.full_name+"!")}const ds=w(function(){return s(c.Fragment,{},s(O,{}),s("title",{},"Dashboard - Psychiatry EMR"),s("meta",{content:"favicon.ico",property:"og:image"}))});export{ds as default};
