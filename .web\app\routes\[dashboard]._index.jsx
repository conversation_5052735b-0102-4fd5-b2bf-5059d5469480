

import { Fragment, useCallback, useContext, useEffect } from "react"
import { EventLoopContext, StateContexts } from "$/utils/context"
import { Event, isTrue } from "$/utils/state"
import { Badge as RadixThemesBadge, Box as RadixThemesBox, <PERSON><PERSON> as RadixThemesButton, Card as RadixThemesCard, Container as RadixThemesContainer, DropdownMenu as RadixThemesDropdownMenu, <PERSON>lex as RadixThemesFlex, Grid as RadixThemesGrid, Heading as RadixThemesHeading, Link as RadixThemesLink, Text as RadixThemesText } from "@radix-ui/themes"
import { BarChart as LucideBarChart, BrainCircuit as LucideBrainCircuit, ChevronDown as LucideChevronDown, Circle as LucideCircle, CircleHelp as LucideCircleHelp, <PERSON><PERSON><PERSON><PERSON>ist as LucideClipboardList, ClipboardPlus as LucideClipboardPlus, Clock as <PERSON><PERSON><PERSON><PERSON>, Home as LucideHome, Key as <PERSON><PERSON><PERSON><PERSON>, LogOut as LucideLogOut, Plus as LucidePlus, Search as LucideSearch, Settings as LucideSettings, TrendingUp as LucideTrendingUp, User as LucideUser, UserPlus as LucideUserPlus, Users as LucideUsers } from "lucide-react"
import { Link as ReactRouterLink } from "react-router"
import { jsx } from "@emotion/react"



function Fragment_72192638570632301994735261530527745264 () {
  
  const reflex___state____state__states___clinical_state____clinical_state = useContext(StateContexts.reflex___state____state__states___clinical_state____clinical_state)





  
  return (
    jsx(
Fragment,
{},
(isTrue(reflex___state____state__states___clinical_state____clinical_state.success_message_rx_state_) ? (jsx(
Fragment,
{},
jsx(
RadixThemesCard,
{css:({ ["position"] : "fixed", ["top"] : "80px", ["right"] : "20px", ["zIndex"] : "9999", ["maxWidth"] : "400px", ["background"] : "green.50", ["border"] : "1px solid", ["borderColor"] : "green.200" })},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"row",gap:"2"},
jsx(LucideCircleHelp,{css:({ ["color"] : "green.500" })},)
,jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"column",gap:"1"},
jsx(
RadixThemesText,
{as:"p",css:({ ["fontWeight"] : "bold" })},
"Success"
,),jsx(Text_102820030196061442617916723669586461230,{},)
,),),),)) : (jsx(Fragment,{},)
)),)
  )
}

function Dropdownmenu__trigger_147570026540717198363334525227418462761 () {
  
  const reflex___state____state__states___auth_state____auth_state = useContext(StateContexts.reflex___state____state__states___auth_state____auth_state)





  
  return (
    jsx(
RadixThemesDropdownMenu.Trigger,
{},
jsx(
RadixThemesButton,
{size:"2",variant:"ghost"},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"row",gap:"2"},
(isTrue(reflex___state____state__states___auth_state____auth_state.current_user_rx_state_) ? reflex___state____state__states___auth_state____auth_state.current_user_rx_state_?.["full_name"] : "User")
,jsx(LucideChevronDown,{size:16},)
,),),)
  )
}

function Button_169816151628875222609663720048451338759 () {
  
  const [addEvents, connectErrors] = useContext(EventLoopContext);


  const on_click_e702a5da1364a6fc9edb845ecb9a4cfa = useCallback(((_e) => (addEvents([(Event("_redirect", ({ ["path"] : "/login", ["external"] : false, ["replace"] : false }), ({  })))], [_e], ({  })))), [addEvents, Event])



  
  return (
    jsx(
RadixThemesButton,
{color:"blue",onClick:on_click_e702a5da1364a6fc9edb845ecb9a4cfa},
"Go to Login"
,)
  )
}

function Fragment_44540825102101947695183164596470730595 () {
  
  const reflex___state____state__states___clinical_state____clinical_state = useContext(StateContexts.reflex___state____state__states___clinical_state____clinical_state)





  
  return (
    jsx(
Fragment,
{},
(isTrue(reflex___state____state__states___clinical_state____clinical_state.error_message_rx_state_) ? (jsx(
Fragment,
{},
jsx(
RadixThemesCard,
{css:({ ["position"] : "fixed", ["top"] : "80px", ["right"] : "20px", ["zIndex"] : "9999", ["maxWidth"] : "400px", ["background"] : "red.50", ["border"] : "1px solid", ["borderColor"] : "red.200" })},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"row",gap:"2"},
jsx(LucideCircleHelp,{css:({ ["color"] : "red.500" })},)
,jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"column",gap:"1"},
jsx(
RadixThemesText,
{as:"p",css:({ ["fontWeight"] : "bold" })},
"Error"
,),jsx(Text_31998917857994533249003073964684816120,{},)
,),),),)) : (jsx(Fragment,{},)
)),)
  )
}

function Text_230921353667065509099663485155314641824 () {
  
  const reflex___state____state__states___patient_state____patient_state = useContext(StateContexts.reflex___state____state__states___patient_state____patient_state)





  
  return (
    jsx(
RadixThemesText,
{as:"p"},
reflex___state____state__states___patient_state____patient_state.error_message_rx_state_
,)
  )
}

function Dropdownmenu__item_161491924517309633733547322360983427567 () {
  
  const [addEvents, connectErrors] = useContext(EventLoopContext);


  const on_click_57e4c3f640fd5e0b686ad03b43a436fd = useCallback(((_e) => (addEvents([(Event("reflex___state____state.states___auth_state____auth_state.logout", ({  }), ({  })))], [_e], ({  })))), [addEvents, Event])



  
  return (
    jsx(
RadixThemesDropdownMenu.Item,
{css:({ ["color"] : "red.600" }),onClick:on_click_57e4c3f640fd5e0b686ad03b43a436fd},
"Logout"
,)
  )
}

function Fragment_7277909197666096817838437013620370764 () {
  
  const reflex___state____state__states___auth_state____auth_state = useContext(StateContexts.reflex___state____state__states___auth_state____auth_state)





  
  return (
    jsx(
Fragment,
{},
(reflex___state____state__states___auth_state____auth_state.is_authenticated_rx_state_ ? (jsx(
Fragment,
{},
jsx(
RadixThemesFlex,
{align:"center",className:"rx-Stack",direction:"row",gap:"2"},
jsx(
RadixThemesButton,
{size:"2",title:"New Patient",variant:"ghost"},
jsx(LucidePlus,{size:16},)
,),jsx(
RadixThemesButton,
{size:"2",title:"New Assessment",variant:"ghost"},
jsx(LucideClipboardPlus,{size:16},)
,),jsx(
RadixThemesDropdownMenu.Root,
{},
jsx(Dropdownmenu__trigger_147570026540717198363334525227418462761,{},)
,jsx(
RadixThemesDropdownMenu.Content,
{},
jsx(
RadixThemesDropdownMenu.Item,
{},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"row",gap:"2"},
jsx(LucideUser,{size:16},)
,jsx(
RadixThemesText,
{as:"p"},
"Profile"
,),),),jsx(
RadixThemesDropdownMenu.Item,
{},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"row",gap:"2"},
jsx(LucideKey,{size:16},)
,jsx(
RadixThemesText,
{as:"p"},
"Change Password"
,),),),jsx(
RadixThemesDropdownMenu.Item,
{},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"row",gap:"2"},
jsx(LucideSettings,{size:16},)
,jsx(
RadixThemesText,
{as:"p"},
"Settings"
,),),),jsx(RadixThemesDropdownMenu.Separator,{},)
,jsx(Dropdownmenu__item_53865495627125640446257350871203294164,{},)
,),),),)) : (jsx(
Fragment,
{},
jsx(Button_213432182251270348700044919999605871278,{},)
,))),)
  )
}

function Fragment_240514998957387502309164644453271310886 () {
  
  const reflex___state____state__states___patient_state____patient_state = useContext(StateContexts.reflex___state____state__states___patient_state____patient_state)





  
  return (
    jsx(
Fragment,
{},
(isTrue(reflex___state____state__states___patient_state____patient_state.success_message_rx_state_) ? (jsx(
Fragment,
{},
jsx(
RadixThemesCard,
{css:({ ["position"] : "fixed", ["top"] : "20px", ["right"] : "20px", ["zIndex"] : "9999", ["maxWidth"] : "400px", ["background"] : "green.50", ["border"] : "1px solid", ["borderColor"] : "green.200" })},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"row",gap:"2"},
jsx(LucideCircleHelp,{css:({ ["color"] : "green.500" })},)
,jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"column",gap:"1"},
jsx(
RadixThemesText,
{as:"p",css:({ ["fontWeight"] : "bold" })},
"Success"
,),jsx(Text_48720832473938332111969035618750431193,{},)
,),),),)) : (jsx(Fragment,{},)
)),)
  )
}

function Heading_182852079874010467042404197047725684073 () {
  
  const reflex___state____state__states___auth_state____auth_state = useContext(StateContexts.reflex___state____state__states___auth_state____auth_state)





  
  return (
    jsx(
RadixThemesHeading,
{size:"6"},
("Good evening, "+reflex___state____state__states___auth_state____auth_state.current_user_rx_state_?.["full_name"]+"!")
,)
  )
}

function Dropdownmenu__item_53865495627125640446257350871203294164 () {
  
  const [addEvents, connectErrors] = useContext(EventLoopContext);


  const on_click_57e4c3f640fd5e0b686ad03b43a436fd = useCallback(((_e) => (addEvents([(Event("reflex___state____state.states___auth_state____auth_state.logout", ({  }), ({  })))], [_e], ({  })))), [addEvents, Event])



  
  return (
    jsx(
RadixThemesDropdownMenu.Item,
{css:({ ["color"] : "red.600" }),onClick:on_click_57e4c3f640fd5e0b686ad03b43a436fd},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"row",gap:"2"},
jsx(LucideLogOut,{size:16},)
,jsx(
RadixThemesText,
{as:"p"},
"Logout"
,),),)
  )
}

function Text_31998917857994533249003073964684816120 () {
  
  const reflex___state____state__states___clinical_state____clinical_state = useContext(StateContexts.reflex___state____state__states___clinical_state____clinical_state)





  
  return (
    jsx(
RadixThemesText,
{as:"p"},
reflex___state____state__states___clinical_state____clinical_state.error_message_rx_state_
,)
  )
}

function Fragment_4943415584891240384484760759615580758 () {
  
  const reflex___state____state__states___auth_state____auth_state = useContext(StateContexts.reflex___state____state__states___auth_state____auth_state)





  
  return (
    jsx(
Fragment,
{},
(reflex___state____state__states___auth_state____auth_state.is_authenticated_rx_state_ ? (jsx(
Fragment,
{},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",css:({ ["width"] : "100%", ["minHeight"] : "100vh" }),direction:"column",gap:"0"},
jsx(
RadixThemesBox,
{css:({ ["width"] : "100%", ["background"] : "white", ["borderBottom"] : "1px solid", ["borderColor"] : "gray.200", ["p"] : 4, ["position"] : "sticky", ["top"] : "0", ["zIndex"] : "1000" })},
jsx(
RadixThemesContainer,
{css:({ ["padding"] : "16px", ["maxWidth"] : "1200px" }),size:"3"},
jsx(
RadixThemesFlex,
{align:"center",className:"rx-Stack",css:({ ["width"] : "100%" }),direction:"row",gap:"3"},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"row",gap:"3"},
jsx(LucideBrainCircuit,{css:({ ["color"] : "blue.600" }),size:32},)
,jsx(
RadixThemesHeading,
{css:({ ["color"] : "blue.600" }),size:"6"},
"Psychiatry EMR"
,),),jsx(Fragment_252584447231808486301477474528160783206,{},)
,jsx(RadixThemesFlex,{css:({ ["flex"] : 1, ["justifySelf"] : "stretch", ["alignSelf"] : "stretch" })},)
,jsx(Fragment_7277909197666096817838437013620370764,{},)
,),),),jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"column",gap:"2"},
jsx(Fragment_240514998957387502309164644453271310886,{},)
,jsx(Fragment_192633857668537415374399445980174592197,{},)
,jsx(Fragment_72192638570632301994735261530527745264,{},)
,jsx(Fragment_44540825102101947695183164596470730595,{},)
,),jsx(
RadixThemesBox,
{css:({ ["width"] : "100%", ["flex"] : "1" })},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",css:({ ["width"] : "100%", ["minHeight"] : "100vh", ["background"] : "gray.50" }),direction:"column",gap:"3"},
jsx(
RadixThemesBox,
{css:({ ["width"] : "100%", ["background"] : "white", ["borderBottom"] : "1px solid", ["borderColor"] : "gray.200", ["p"] : 4 })},
jsx(
RadixThemesContainer,
{css:({ ["padding"] : "16px", ["maxWidth"] : "1200px" }),size:"3"},
jsx(
RadixThemesFlex,
{align:"center",className:"rx-Stack",css:({ ["width"] : "100%" }),direction:"row",gap:"3"},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"row",gap:"3"},
jsx(LucideBrainCircuit,{css:({ ["color"] : "blue.600" }),size:32},)
,jsx(
RadixThemesHeading,
{css:({ ["color"] : "blue.600" }),size:"6"},
"Psychiatry EMR"
,),),jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"row",gap:"6"},
jsx(
RadixThemesLink,
{asChild:true,css:({ ["fontWeight"] : "bold", ["&:hover"] : ({ ["color"] : "var(--accent-8)" }) })},
jsx(
ReactRouterLink,
{to:"/dashboard"},
"Dashboard"
,),),jsx(
RadixThemesLink,
{asChild:true,css:({ ["&:hover"] : ({ ["color"] : "var(--accent-8)" }) })},
jsx(
ReactRouterLink,
{to:"/patients"},
"Patients"
,),),jsx(
RadixThemesLink,
{asChild:true,css:({ ["&:hover"] : ({ ["color"] : "var(--accent-8)" }) })},
jsx(
ReactRouterLink,
{to:"/assessments"},
"Assessments"
,),),jsx(
RadixThemesLink,
{asChild:true,css:({ ["&:hover"] : ({ ["color"] : "var(--accent-8)" }) })},
jsx(
ReactRouterLink,
{to:"/reports"},
"Reports"
,),),),jsx(RadixThemesFlex,{css:({ ["flex"] : 1, ["justifySelf"] : "stretch", ["alignSelf"] : "stretch" })},)
,jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"row",gap:"3"},
jsx(Text_97664733510008381965334382672669805058,{},)
,jsx(
RadixThemesDropdownMenu.Root,
{},
jsx(
RadixThemesDropdownMenu.Trigger,
{},
jsx(
RadixThemesButton,
{size:"2",variant:"ghost"},
jsx(LucideUser,{size:20},)
,),),jsx(
RadixThemesDropdownMenu.Content,
{},
jsx(
RadixThemesDropdownMenu.Item,
{},
"Profile Settings"
,),jsx(
RadixThemesDropdownMenu.Item,
{},
"Change Password"
,),jsx(RadixThemesDropdownMenu.Separator,{},)
,jsx(Dropdownmenu__item_161491924517309633733547322360983427567,{},)
,),),),),),),jsx(
RadixThemesContainer,
{css:({ ["padding"] : "16px", ["maxWidth"] : "1200px", ["p"] : 6 }),size:"3"},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",css:({ ["width"] : "100%" }),direction:"column",gap:"6"},
jsx(
RadixThemesCard,
{css:({ ["width"] : "100%", ["p"] : 6, ["background"] : "gradient-to-r from-blue-50 to-purple-50" })},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"column",gap:"3"},
jsx(
RadixThemesFlex,
{align:"center",className:"rx-Stack",css:({ ["width"] : "100%" }),direction:"row",gap:"3"},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"column",gap:"1"},
jsx(Heading_182852079874010467042404197047725684073,{},)
,jsx(
RadixThemesText,
{as:"p",css:({ ["color"] : "gray.600" })},
"Today is Tuesday, July 29, 2025"
,),),jsx(RadixThemesFlex,{css:({ ["flex"] : 1, ["justifySelf"] : "stretch", ["alignSelf"] : "stretch" })},)
,jsx(
RadixThemesFlex,
{align:"end",className:"rx-Stack",direction:"column",gap:"1"},
jsx(
RadixThemesText,
{as:"p",css:({ ["fontSize"] : "sm", ["color"] : "gray.500" })},
"Your Role"
,),jsx(Badge_115294372017975605376429257451730167339,{},)
,),),),),jsx(
RadixThemesGrid,
{columns:"4",css:({ ["width"] : "100%" }),gap:"4"},
jsx(
RadixThemesCard,
{css:({ ["p"] : 4, ["width"] : "100%" })},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"column",gap:"3"},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",css:({ ["width"] : "100%" }),direction:"row",gap:"3"},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"column",gap:"1"},
jsx(
RadixThemesText,
{as:"p",css:({ ["fontSize"] : "sm", ["color"] : "gray.600" })},
"Total Patients"
,),jsx(
RadixThemesHeading,
{size:"6"},
"1,247"
,),),jsx(RadixThemesFlex,{css:({ ["flex"] : 1, ["justifySelf"] : "stretch", ["alignSelf"] : "stretch" })},)
,jsx(LucideUsers,{css:({ ["color"] : "blue.500" }),size:24},)
,),jsx(
RadixThemesText,
{as:"p",css:({ ["fontSize"] : "sm", ["color"] : "blue.600" })},
"+12 this month"
,),),),jsx(
RadixThemesCard,
{css:({ ["p"] : 4, ["width"] : "100%" })},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"column",gap:"3"},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",css:({ ["width"] : "100%" }),direction:"row",gap:"3"},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"column",gap:"1"},
jsx(
RadixThemesText,
{as:"p",css:({ ["fontSize"] : "sm", ["color"] : "gray.600" })},
"Active Assessments"
,),jsx(
RadixThemesHeading,
{size:"6"},
"89"
,),),jsx(RadixThemesFlex,{css:({ ["flex"] : 1, ["justifySelf"] : "stretch", ["alignSelf"] : "stretch" })},)
,jsx(LucideClipboardList,{css:({ ["color"] : "green.500" }),size:24},)
,),jsx(
RadixThemesText,
{as:"p",css:({ ["fontSize"] : "sm", ["color"] : "green.600" })},
"+5 today"
,),),),jsx(
RadixThemesCard,
{css:({ ["p"] : 4, ["width"] : "100%" })},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"column",gap:"3"},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",css:({ ["width"] : "100%" }),direction:"row",gap:"3"},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"column",gap:"1"},
jsx(
RadixThemesText,
{as:"p",css:({ ["fontSize"] : "sm", ["color"] : "gray.600" })},
"Pending Reviews"
,),jsx(
RadixThemesHeading,
{size:"6"},
"23"
,),),jsx(RadixThemesFlex,{css:({ ["flex"] : 1, ["justifySelf"] : "stretch", ["alignSelf"] : "stretch" })},)
,jsx(LucideClock,{css:({ ["color"] : "yellow.500" }),size:24},)
,),jsx(
RadixThemesText,
{as:"p",css:({ ["fontSize"] : "sm", ["color"] : "yellow.600" })},
"-3 from yesterday"
,),),),jsx(
RadixThemesCard,
{css:({ ["p"] : 4, ["width"] : "100%" })},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"column",gap:"3"},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",css:({ ["width"] : "100%" }),direction:"row",gap:"3"},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"column",gap:"1"},
jsx(
RadixThemesText,
{as:"p",css:({ ["fontSize"] : "sm", ["color"] : "gray.600" })},
"This Month"
,),jsx(
RadixThemesHeading,
{size:"6"},
"156"
,),),jsx(RadixThemesFlex,{css:({ ["flex"] : 1, ["justifySelf"] : "stretch", ["alignSelf"] : "stretch" })},)
,jsx(LucideTrendingUp,{css:({ ["color"] : "purple.500" }),size:24},)
,),jsx(
RadixThemesText,
{as:"p",css:({ ["fontSize"] : "sm", ["color"] : "purple.600" })},
"Assessments completed"
,),),),),jsx(
RadixThemesGrid,
{columns:"2",css:({ ["width"] : "100%" }),gap:"6"},
jsx(
RadixThemesCard,
{css:({ ["p"] : 4, ["width"] : "100%" })},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"column",gap:"4"},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",css:({ ["width"] : "100%" }),direction:"row",gap:"3"},
jsx(
RadixThemesHeading,
{size:"5"},
"Recent Patients"
,),jsx(RadixThemesFlex,{css:({ ["flex"] : 1, ["justifySelf"] : "stretch", ["alignSelf"] : "stretch" })},)
,jsx(
RadixThemesLink,
{asChild:true,css:({ ["color"] : "blue.600", ["&:hover"] : ({ ["color"] : "var(--accent-8)" }) })},
jsx(
ReactRouterLink,
{to:"/patients"},
"View All"
,),),),jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",css:({ ["width"] : "100%" }),direction:"column",gap:"2"},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",css:({ ["width"] : "100%", ["p"] : 2, ["borderRadius"] : "md", ["&:hover"] : ({ ["background"] : "gray.50" }) }),direction:"row",gap:"3"},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"column",gap:"1"},
jsx(
RadixThemesText,
{as:"p",css:({ ["fontWeight"] : "medium" })},
"Sarah Johnson"
,),jsx(
RadixThemesText,
{as:"p",css:({ ["fontSize"] : "sm", ["color"] : "gray.600" })},
"Follow-up scheduled"
,),),jsx(RadixThemesFlex,{css:({ ["flex"] : 1, ["justifySelf"] : "stretch", ["alignSelf"] : "stretch" })},)
,jsx(
RadixThemesText,
{as:"p",css:({ ["fontSize"] : "sm", ["color"] : "gray.500" })},
"2024-01-15"
,),),jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",css:({ ["width"] : "100%", ["p"] : 2, ["borderRadius"] : "md", ["&:hover"] : ({ ["background"] : "gray.50" }) }),direction:"row",gap:"3"},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"column",gap:"1"},
jsx(
RadixThemesText,
{as:"p",css:({ ["fontWeight"] : "medium" })},
"Michael Chen"
,),jsx(
RadixThemesText,
{as:"p",css:({ ["fontSize"] : "sm", ["color"] : "gray.600" })},
"Assessment completed"
,),),jsx(RadixThemesFlex,{css:({ ["flex"] : 1, ["justifySelf"] : "stretch", ["alignSelf"] : "stretch" })},)
,jsx(
RadixThemesText,
{as:"p",css:({ ["fontSize"] : "sm", ["color"] : "gray.500" })},
"2024-01-14"
,),),jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",css:({ ["width"] : "100%", ["p"] : 2, ["borderRadius"] : "md", ["&:hover"] : ({ ["background"] : "gray.50" }) }),direction:"row",gap:"3"},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"column",gap:"1"},
jsx(
RadixThemesText,
{as:"p",css:({ ["fontWeight"] : "medium" })},
"Emily Davis"
,),jsx(
RadixThemesText,
{as:"p",css:({ ["fontSize"] : "sm", ["color"] : "gray.600" })},
"New patient intake"
,),),jsx(RadixThemesFlex,{css:({ ["flex"] : 1, ["justifySelf"] : "stretch", ["alignSelf"] : "stretch" })},)
,jsx(
RadixThemesText,
{as:"p",css:({ ["fontSize"] : "sm", ["color"] : "gray.500" })},
"2024-01-13"
,),),jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",css:({ ["width"] : "100%", ["p"] : 2, ["borderRadius"] : "md", ["&:hover"] : ({ ["background"] : "gray.50" }) }),direction:"row",gap:"3"},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"column",gap:"1"},
jsx(
RadixThemesText,
{as:"p",css:({ ["fontWeight"] : "medium" })},
"Robert Wilson"
,),jsx(
RadixThemesText,
{as:"p",css:({ ["fontSize"] : "sm", ["color"] : "gray.600" })},
"Treatment plan updated"
,),),jsx(RadixThemesFlex,{css:({ ["flex"] : 1, ["justifySelf"] : "stretch", ["alignSelf"] : "stretch" })},)
,jsx(
RadixThemesText,
{as:"p",css:({ ["fontSize"] : "sm", ["color"] : "gray.500" })},
"2024-01-12"
,),),jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",css:({ ["width"] : "100%", ["p"] : 2, ["borderRadius"] : "md", ["&:hover"] : ({ ["background"] : "gray.50" }) }),direction:"row",gap:"3"},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"column",gap:"1"},
jsx(
RadixThemesText,
{as:"p",css:({ ["fontWeight"] : "medium" })},
"Lisa Anderson"
,),jsx(
RadixThemesText,
{as:"p",css:({ ["fontSize"] : "sm", ["color"] : "gray.600" })},
"Medication review"
,),),jsx(RadixThemesFlex,{css:({ ["flex"] : 1, ["justifySelf"] : "stretch", ["alignSelf"] : "stretch" })},)
,jsx(
RadixThemesText,
{as:"p",css:({ ["fontSize"] : "sm", ["color"] : "gray.500" })},
"2024-01-11"
,),),),),),jsx(
RadixThemesCard,
{css:({ ["p"] : 4, ["width"] : "100%" })},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"column",gap:"4"},
jsx(
RadixThemesHeading,
{css:({ ["mb"] : 4 }),size:"5"},
"Quick Actions"
,),jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",css:({ ["width"] : "100%" }),direction:"column",gap:"3"},
jsx(
RadixThemesButton,
{color:"blue",css:({ ["width"] : "100%", ["justify"] : "start" }),size:"3"},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"row",gap:"2"},
jsx(LucideUserPlus,{size:20},)
,jsx(
RadixThemesText,
{as:"p"},
"New Patient"
,),),),jsx(
RadixThemesButton,
{color:"green",css:({ ["width"] : "100%", ["justify"] : "start" }),size:"3"},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"row",gap:"2"},
jsx(LucideClipboardPlus,{size:20},)
,jsx(
RadixThemesText,
{as:"p"},
"New Assessment"
,),),),jsx(
RadixThemesButton,
{color:"purple",css:({ ["width"] : "100%", ["justify"] : "start" }),size:"3"},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"row",gap:"2"},
jsx(LucideSearch,{size:20},)
,jsx(
RadixThemesText,
{as:"p"},
"Search Patients"
,),),),jsx(
RadixThemesButton,
{color:"orange",css:({ ["width"] : "100%", ["justify"] : "start" }),size:"3"},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"row",gap:"2"},
jsx(LucideBarChart,{size:20},)
,jsx(
RadixThemesText,
{as:"p"},
"View Reports"
,),),),),),),),jsx(
RadixThemesCard,
{css:({ ["p"] : 4, ["width"] : "100%", ["background"] : "gray.50" })},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"column",gap:"4"},
jsx(
RadixThemesHeading,
{css:({ ["mb"] : 4 }),size:"5"},
"System Status"
,),jsx(
RadixThemesGrid,
{columns:"4",css:({ ["width"] : "100%" }),gap:"4"},
jsx(
RadixThemesFlex,
{align:"center",className:"rx-Stack",direction:"row",gap:"2"},
jsx(LucideCircle,{css:({ ["color"] : "green.500" }),size:12},)
,jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"column",gap:"0"},
jsx(
RadixThemesText,
{as:"p",css:({ ["fontWeight"] : "medium", ["fontSize"] : "sm" })},
"Database"
,),jsx(
RadixThemesText,
{as:"p",css:({ ["fontSize"] : "xs", ["color"] : "gray.600" })},
"Connected"
,),),),jsx(
RadixThemesFlex,
{align:"center",className:"rx-Stack",direction:"row",gap:"2"},
jsx(LucideCircle,{css:({ ["color"] : "green.500" }),size:12},)
,jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"column",gap:"0"},
jsx(
RadixThemesText,
{as:"p",css:({ ["fontWeight"] : "medium", ["fontSize"] : "sm" })},
"Encryption"
,),jsx(
RadixThemesText,
{as:"p",css:({ ["fontSize"] : "xs", ["color"] : "gray.600" })},
"Active"
,),),),jsx(
RadixThemesFlex,
{align:"center",className:"rx-Stack",direction:"row",gap:"2"},
jsx(LucideCircle,{css:({ ["color"] : "green.500" }),size:12},)
,jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"column",gap:"0"},
jsx(
RadixThemesText,
{as:"p",css:({ ["fontWeight"] : "medium", ["fontSize"] : "sm" })},
"Backup"
,),jsx(
RadixThemesText,
{as:"p",css:({ ["fontSize"] : "xs", ["color"] : "gray.600" })},
"Last: 2 hours ago"
,),),),jsx(
RadixThemesFlex,
{align:"center",className:"rx-Stack",direction:"row",gap:"2"},
jsx(LucideCircle,{css:({ ["color"] : "green.500" }),size:12},)
,jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"column",gap:"0"},
jsx(
RadixThemesText,
{as:"p",css:({ ["fontWeight"] : "medium", ["fontSize"] : "sm" })},
"Security"
,),jsx(
RadixThemesText,
{as:"p",css:({ ["fontSize"] : "xs", ["color"] : "gray.600" })},
"All systems normal"
,),),),),),),),),),),jsx(
RadixThemesBox,
{css:({ ["width"] : "100%", ["background"] : "gray.100", ["borderTop"] : "1px solid", ["borderColor"] : "gray.200", ["p"] : 4, ["mt"] : "auto" })},
jsx(
RadixThemesContainer,
{css:({ ["padding"] : "16px", ["maxWidth"] : "1200px" }),size:"3"},
jsx(
RadixThemesFlex,
{align:"center",className:"rx-Stack",css:({ ["width"] : "100%" }),direction:"row",gap:"3"},
jsx(
RadixThemesText,
{as:"p",css:({ ["color"] : "gray.600", ["fontSize"] : "sm" })},
"\u00a9 2024 Psychiatry EMR. All rights reserved."
,),jsx(RadixThemesFlex,{css:({ ["flex"] : 1, ["justifySelf"] : "stretch", ["alignSelf"] : "stretch" })},)
,jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"row",gap:"2"},
jsx(
RadixThemesText,
{as:"p",css:({ ["color"] : "gray.500", ["fontSize"] : "xs" })},
"Version 1.0.0"
,),jsx(
RadixThemesText,
{as:"p",css:({ ["color"] : "gray.400" })},
"\u2022"
,),jsx(
RadixThemesLink,
{asChild:true,css:({ ["color"] : "gray.500", ["fontSize"] : "xs", ["&:hover"] : ({ ["color"] : "var(--accent-8)" }) })},
jsx(
ReactRouterLink,
{to:"/privacy"},
"Privacy Policy"
,),),jsx(
RadixThemesText,
{as:"p",css:({ ["color"] : "gray.400" })},
"\u2022"
,),jsx(
RadixThemesLink,
{asChild:true,css:({ ["color"] : "gray.500", ["fontSize"] : "xs", ["&:hover"] : ({ ["color"] : "var(--accent-8)" }) })},
jsx(
ReactRouterLink,
{to:"/support"},
"Support"
,),),),),),),),)) : (jsx(
Fragment,
{},
jsx(
RadixThemesFlex,
{align:"center",className:"rx-Stack",css:({ ["p"] : 8 }),direction:"column",gap:"4"},
jsx(
RadixThemesHeading,
{size:"6"},
"Authentication Required"
,),jsx(
RadixThemesText,
{as:"p"},
"Please log in to access this page."
,),jsx(Button_169816151628875222609663720048451338759,{},)
,),))),)
  )
}

function Text_102820030196061442617916723669586461230 () {
  
  const reflex___state____state__states___clinical_state____clinical_state = useContext(StateContexts.reflex___state____state__states___clinical_state____clinical_state)





  
  return (
    jsx(
RadixThemesText,
{as:"p"},
reflex___state____state__states___clinical_state____clinical_state.success_message_rx_state_
,)
  )
}

function Text_97664733510008381965334382672669805058 () {
  
  const reflex___state____state__states___auth_state____auth_state = useContext(StateContexts.reflex___state____state__states___auth_state____auth_state)





  
  return (
    jsx(
RadixThemesText,
{as:"p",css:({ ["fontWeight"] : "medium" })},
("Welcome, "+reflex___state____state__states___auth_state____auth_state.current_user_rx_state_?.["full_name"])
,)
  )
}

function Fragment_252584447231808486301477474528160783206 () {
  
  const reflex___state____state__states___auth_state____auth_state = useContext(StateContexts.reflex___state____state__states___auth_state____auth_state)





  
  return (
    jsx(
Fragment,
{},
(reflex___state____state__states___auth_state____auth_state.is_authenticated_rx_state_ ? (jsx(
Fragment,
{},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"row",gap:"6"},
jsx(
RadixThemesLink,
{asChild:true,css:({ ["color"] : "gray.700", ["fontWeight"] : "medium", ["&:hover"] : ({ ["color"] : "blue.600" }), ["&:active"] : ({ ["color"] : "blue.600", ["fontWeight"] : "bold" }) })},
jsx(
ReactRouterLink,
{to:"/dashboard"},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"row",gap:"2"},
jsx(LucideHome,{size:18},)
,jsx(
RadixThemesText,
{as:"p"},
"Dashboard"
,),),),),jsx(
RadixThemesLink,
{asChild:true,css:({ ["color"] : "gray.700", ["fontWeight"] : "medium", ["&:hover"] : ({ ["color"] : "blue.600" }), ["&:active"] : ({ ["color"] : "blue.600", ["fontWeight"] : "bold" }) })},
jsx(
ReactRouterLink,
{to:"/patients"},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"row",gap:"2"},
jsx(LucideUsers,{size:18},)
,jsx(
RadixThemesText,
{as:"p"},
"Patients"
,),),),),jsx(
RadixThemesLink,
{asChild:true,css:({ ["color"] : "gray.700", ["fontWeight"] : "medium", ["&:hover"] : ({ ["color"] : "blue.600" }), ["&:active"] : ({ ["color"] : "blue.600", ["fontWeight"] : "bold" }) })},
jsx(
ReactRouterLink,
{to:"/assessments"},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"row",gap:"2"},
jsx(LucideClipboardList,{size:18},)
,jsx(
RadixThemesText,
{as:"p"},
"Assessments"
,),),),),jsx(
RadixThemesLink,
{asChild:true,css:({ ["color"] : "gray.700", ["fontWeight"] : "medium", ["&:hover"] : ({ ["color"] : "blue.600" }), ["&:active"] : ({ ["color"] : "blue.600", ["fontWeight"] : "bold" }) })},
jsx(
ReactRouterLink,
{to:"/reports"},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"row",gap:"2"},
jsx(LucideBarChart,{size:18},)
,jsx(
RadixThemesText,
{as:"p"},
"Reports"
,),),),),jsx(Fragment_166734300427242336168950855614206449165,{},)
,),)) : (jsx(Fragment,{},)
)),)
  )
}

function Fragment_166734300427242336168950855614206449165 () {
  
  const reflex___state____state__states___auth_state____auth_state = useContext(StateContexts.reflex___state____state__states___auth_state____auth_state)





  
  return (
    jsx(
Fragment,
{},
(reflex___state____state__states___auth_state____auth_state.is_admin_rx_state_ ? (jsx(
Fragment,
{},
jsx(
RadixThemesLink,
{asChild:true,css:({ ["color"] : "gray.700", ["fontWeight"] : "medium", ["&:hover"] : ({ ["color"] : "blue.600" }), ["&:active"] : ({ ["color"] : "blue.600", ["fontWeight"] : "bold" }) })},
jsx(
ReactRouterLink,
{to:"/admin"},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"row",gap:"2"},
jsx(LucideSettings,{size:18},)
,jsx(
RadixThemesText,
{as:"p"},
"Admin"
,),),),),)) : (jsx(Fragment,{},)
)),)
  )
}

function Badge_115294372017975605376429257451730167339 () {
  
  const reflex___state____state__states___auth_state____auth_state = useContext(StateContexts.reflex___state____state__states___auth_state____auth_state)





  
  return (
    jsx(
RadixThemesBadge,
{color:"blue",size:"3"},
reflex___state____state__states___auth_state____auth_state.current_user_rx_state_?.["role"].split(' ').map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()).join(' ')
,)
  )
}

function Fragment_192633857668537415374399445980174592197 () {
  
  const reflex___state____state__states___patient_state____patient_state = useContext(StateContexts.reflex___state____state__states___patient_state____patient_state)





  
  return (
    jsx(
Fragment,
{},
(isTrue(reflex___state____state__states___patient_state____patient_state.error_message_rx_state_) ? (jsx(
Fragment,
{},
jsx(
RadixThemesCard,
{css:({ ["position"] : "fixed", ["top"] : "20px", ["right"] : "20px", ["zIndex"] : "9999", ["maxWidth"] : "400px", ["background"] : "red.50", ["border"] : "1px solid", ["borderColor"] : "red.200" })},
jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"row",gap:"2"},
jsx(LucideCircleHelp,{css:({ ["color"] : "red.500" })},)
,jsx(
RadixThemesFlex,
{align:"start",className:"rx-Stack",direction:"column",gap:"1"},
jsx(
RadixThemesText,
{as:"p",css:({ ["fontWeight"] : "bold" })},
"Error"
,),jsx(Text_230921353667065509099663485155314641824,{},)
,),),),)) : (jsx(Fragment,{},)
)),)
  )
}

function Text_48720832473938332111969035618750431193 () {
  
  const reflex___state____state__states___patient_state____patient_state = useContext(StateContexts.reflex___state____state__states___patient_state____patient_state)





  
  return (
    jsx(
RadixThemesText,
{as:"p"},
reflex___state____state__states___patient_state____patient_state.success_message_rx_state_
,)
  )
}

function Button_213432182251270348700044919999605871278 () {
  
  const [addEvents, connectErrors] = useContext(EventLoopContext);


  const on_click_e702a5da1364a6fc9edb845ecb9a4cfa = useCallback(((_e) => (addEvents([(Event("_redirect", ({ ["path"] : "/login", ["external"] : false, ["replace"] : false }), ({  })))], [_e], ({  })))), [addEvents, Event])



  
  return (
    jsx(
RadixThemesButton,
{color:"blue",onClick:on_click_e702a5da1364a6fc9edb845ecb9a4cfa,size:"2"},
"Login"
,)
  )
}

export default function Component() {
    




  return (
    jsx(
Fragment,
{},
jsx(Fragment_4943415584891240384484760759615580758,{},)
,jsx(
"title",
{},
"Dashboard - Psychiatry EMR"
,),jsx("meta",{content:"favicon.ico",property:"og:image"},)
,)
  )
}
