import{a as e,o as i,p as m,w as p}from"./chunk-QMGIS6GS-suYYFPSk.js";import{i as f}from"./state-B1hYtTsq.js";import{j as t}from"./emotion-react.browser.esm-BNSIgtcs.js";const d=()=>{const[o,n]=e.useState(!1),a=e.useRef(!1),r=i(),s=m();return e.useEffect(()=>{if(!a.current){a.current=!0;const u=window.location.pathname,c=window.location.search;s(u+c,{replace:!0,state:{fromNotFound:!0}}).then(()=>{r.pathname===u&&n(!0)}).catch(()=>{n(!0)})}},[r,s]),o};function g(){const o=d();return t(e.Fragment,{},f(o)?t(e.Fragment,{},t("span",{},"404: Page not found")):t(e.Fragment,{}))}const w=p(function(){return t(e.Fragment,{},t(g,{}),t("title",{},"404 - Not Found"),t("meta",{content:"The page was not found",name:"description"}),t("meta",{content:"favicon.ico",property:"og:image"}))});export{w as default};
