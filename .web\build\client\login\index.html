<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta content="width=device-width, initial-scale=1" name="viewport"/><title>Login - Psychiatry EMR</title><meta content="favicon.ico" property="og:image"/><link rel="modulepreload" href="/assets/manifest-c1b6a5b7.js"/><link rel="modulepreload" href="/assets/entry.client-BjDPl_tA.js"/><link rel="modulepreload" href="/assets/jsx-runtime-D_zvdyIk.js"/><link rel="modulepreload" href="/assets/chunk-QMGIS6GS-suYYFPSk.js"/><link rel="modulepreload" href="/assets/index-XOwJfM4g.js"/><link rel="modulepreload" href="/assets/root-CN8mmq02.js"/><link rel="modulepreload" href="/assets/state-B1hYtTsq.js"/><link rel="modulepreload" href="/assets/emotion-react.browser.esm-BNSIgtcs.js"/><link rel="modulepreload" href="/assets/button-Ccwu8jNm.js"/><link rel="modulepreload" href="/assets/text-DCkbNTq3.js"/><link rel="modulepreload" href="/assets/createLucideIcon-BOfs0RvG.js"/><link rel="modulepreload" href="/assets/container-Do-LxYxS.js"/><link rel="modulepreload" href="/assets/card-BFhh40Od.js"/><link rel="modulepreload" href="/assets/text-area-DvQa0X7q.js"/><link rel="modulepreload" href="/assets/text-field-UsP3CXyu.js"/><link rel="modulepreload" href="/assets/select-hLIrycZp.js"/><link rel="modulepreload" href="/assets/separator-CPIfs_Op.js"/><link rel="modulepreload" href="/assets/_login_._index-CX9q6itg.js"/><link rel="modulepreload" href="/assets/index-CKoiYi4C.js"/><link rel="stylesheet" href="/assets/__reflex_global_styles-tb2FFvNE.css" type="text/css"/></head><body><style data-emotion="css 6cciat">.css-6cciat{font-family:Inter,system-ui,sans-serif;--default-font-family:Inter,system-ui,sans-serif;background-color:#f8fafc;color:#1e293b;}</style><div data-is-root-theme="true" data-accent-color="blue" data-gray-color="slate" data-has-background="true" data-panel-background="translucent" data-radius="medium" data-scaling="100%" class="radix-themes css-6cciat"><style data-emotion="css 17rg0dp">.css-17rg0dp{position:fixed;width:100vw;height:0;}</style><div title="Connection Error: " class="css-17rg0dp"></div><section aria-label="Notifications alt+T" tabindex="-1" aria-live="polite" aria-relevant="additions text" aria-atomic="false"></section><style data-emotion="css 1jc7f17">.css-1jc7f17{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-ms-flex-pack:center;-webkit-justify-content:center;justify-content:center;min-height:100vh;background:gray.50;}</style><div class="rt-Flex css-1jc7f17"><style data-emotion="css 1fvxqvc">.css-1fvxqvc{max-width:400px;p:8px;}</style><div class="rt-reset rt-BaseCard rt-Card rt-r-size-1 rt-variant-surface css-1fvxqvc"><style data-emotion="css 8atqhb">.css-8atqhb{width:100%;}</style><div class="rt-Flex rt-r-fd-column rt-r-ai-start rt-r-gap-4 rx-Stack css-8atqhb"><style data-emotion="css 16k2nqv">.css-16k2nqv{color:blue.600;mb:6px;}</style><h1 class="rt-Heading rt-r-size-8 css-16k2nqv">Psychiatry EMR</h1><style data-emotion="css 1s5j1dg">.css-1s5j1dg{color:gray.600;mb:4px;}</style><p class="rt-Text css-1s5j1dg">Secure Patient Management System</p><div class="rt-Flex rt-r-fd-column rt-r-ai-start rt-r-gap-4 rx-Stack css-8atqhb"><div class="rt-TextFieldRoot rt-r-size-2 rt-variant-surface css-8atqhb"><input spellCheck="false" placeholder="Username" type="text" class="rt-reset rt-TextFieldInput" value=""/></div><div class="rt-TextFieldRoot rt-r-size-2 rt-variant-surface css-8atqhb"><input spellCheck="false" placeholder="Password" type="password" class="rt-reset rt-TextFieldInput" value=""/></div><button data-accent-color="blue" class="rt-reset rt-BaseButton rt-r-size-3 rt-variant-solid rt-Button css-8atqhb">Login</button></div></div></div></div><style data-emotion="css 1qwptgo">.css-1qwptgo{position:fixed;bottom:1rem;right:1rem;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-flex-direction:row;-ms-flex-direction:row;flex-direction:row;gap:0.375rem;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;width:auto;border-radius:0.5rem;color:#E5E7EB;border:1px solid #27282B;background-color:#151618;padding:0.375rem;-webkit-transition:background-color 0.2s ease-in-out;transition:background-color 0.2s ease-in-out;box-shadow:0 1px 2px 0 rgba(0, 0, 0, 0.05);z-index:9998;cursor:pointer;align:center;text-align:center;}</style><a href="https://reflex.dev" target="_blank" class="css-1qwptgo"><style data-emotion="css 1wzg22f">.css-1wzg22f{fill:white;view-box:0 0 16 16;}</style><svg height="16" width="16" xmlns="http://www.w3.org/2000/svg" class="css-1wzg22f"><style data-emotion="css bnqfad">.css-bnqfad{fill:#6E56CF;}</style><rect height="16" rx="2" width="16" class="css-bnqfad"></rect><style data-emotion="css j4aqsr">.css-j4aqsr{fill:white;}</style><path d="M10 9V13H12V9H10Z" class="css-j4aqsr"></path><path d="M4 3V13H6V9H10V7H6V5H10V7H12V3H4Z" class="css-j4aqsr"></path></svg><style data-emotion="css 1v8fzbk">@media screen and (min-width: 0){.css-1v8fzbk{display:none;}}@media screen and (min-width: 30em){.css-1v8fzbk{display:none;}}@media screen and (min-width: 48em){.css-1v8fzbk{display:none;}}@media screen and (min-width: 62em){.css-1v8fzbk{display:block;}}</style><div class="rt-Box css-1v8fzbk"><style data-emotion="css vq6poo">.css-vq6poo{color:var(--slate-1);font-weight:600;font-family:'Instrument Sans',sans-serif;--default-font-family:'Instrument Sans',sans-serif;font-size:0.875rem;line-height:1rem;letter-spacing:-0.00656rem;}</style><p class="rt-Text css-vq6poo">Built with Reflex</p></div></a></div><script>((storageKey2, restoreKey) => {
    if (!window.history.state || !window.history.state.key) {
      let key = Math.random().toString(32).slice(2);
      window.history.replaceState({ key }, "");
    }
    try {
      let positions = JSON.parse(sessionStorage.getItem(storageKey2) || "{}");
      let storedY = positions[restoreKey || window.history.state.key];
      if (typeof storedY === "number") {
        window.scrollTo(0, storedY);
      }
    } catch (error) {
      console.error(error);
      sessionStorage.removeItem(storageKey2);
    }
  })("react-router-scroll-positions", null)</script><script>window.__reactRouterContext = {"basename":"/","future":{"unstable_middleware":false,"unstable_optimizeDeps":true,"unstable_splitRouteModules":false,"unstable_subResourceIntegrity":false,"unstable_viteEnvironmentApi":false},"routeDiscovery":{"mode":"initial"},"ssr":false,"isSpaMode":false};window.__reactRouterContext.stream = new ReadableStream({start(controller){window.__reactRouterContext.streamController = controller;}}).pipeThrough(new TextEncoderStream());</script><script type="module" async="">import "/assets/manifest-c1b6a5b7.js";
import * as route0 from "/assets/root-CN8mmq02.js";
import * as route1 from "/assets/_login_._index-CX9q6itg.js";
  
  window.__reactRouterRouteModules = {"root":route0,"routes/[login]._index":route1};

import("/assets/entry.client-BjDPl_tA.js");</script><!--$?--><template id="B:0"></template><!--/$--><div hidden id="S:0"><script>window.__reactRouterContext.streamController.enqueue("[{\"_1\":2,\"_3\":-5,\"_4\":-5},\"loaderData\",{},\"actionData\",\"errors\"]\n");</script><!--$?--><template id="B:1"></template><!--/$--></div><script>$RC=function(b,c,e){c=document.getElementById(c);c.parentNode.removeChild(c);var a=document.getElementById(b);if(a){b=a.previousSibling;if(e)b.data="$!",a.setAttribute("data-dgst",e);else{e=b.parentNode;a=b.nextSibling;var f=0;do{if(a&&8===a.nodeType){var d=a.data;if("/$"===d)if(0===f)break;else f--;else"$"!==d&&"$?"!==d&&"$!"!==d||f++}d=a.nextSibling;e.removeChild(a);a=d}while(a);for(;c.firstChild;)e.insertBefore(c.firstChild,a);b.data="$"}b._reactRetry&&b._reactRetry()}};$RC("B:0","S:0")</script><div hidden id="S:1"><script>window.__reactRouterContext.streamController.close();</script></div><script>$RC("B:1","S:1")</script></body></html>