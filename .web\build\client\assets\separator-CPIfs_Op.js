import{a as e}from"./chunk-QMGIS6GS-suYYFPSk.js";import{e as c,v as n,r as i,y as p}from"./text-DCkbNTq3.js";const m=["horizontal","vertical"],u=["1","2","3","4"],d={orientation:{type:"enum",className:"rt-r-orientation",values:m,default:"horizontal",responsive:!0},size:{type:"enum",className:"rt-r-size",values:u,default:"1",responsive:!0},color:{...c.color,default:"gray"},decorative:{type:"boolean",default:!0}},f=e.forwardRef((r,a)=>{const{className:t,color:o,decorative:s,...l}=n(r,d,i);return e.createElement("span",{"data-accent-color":o,role:s?void 0:"separator",...l,ref:a,className:p("rt-Separator",t)})});f.displayName="Separator";export{f as o};
