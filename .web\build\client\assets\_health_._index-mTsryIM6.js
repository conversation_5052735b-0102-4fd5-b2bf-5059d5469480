import{w as o,a as e}from"./chunk-QMGIS6GS-suYYFPSk.js";import{j as t}from"./emotion-react.browser.esm-BNSIgtcs.js";import{p as a}from"./text-DCkbNTq3.js";import"./jsx-runtime-D_zvdyIk.js";const i=o(function(){return t(e.Fragment,{},t(a,{as:"p"},"Health check: ERROR - Not an executable object: 'SELECT 1'"),t("title",{},"Health Check"),t("meta",{content:"favicon.ico",property:"og:image"}))});export{i as default};
