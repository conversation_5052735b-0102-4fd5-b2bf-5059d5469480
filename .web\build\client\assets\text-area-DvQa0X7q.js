import{a as n}from"./chunk-QMGIS6GS-suYYFPSk.js";import{u as D,d as L,e as I,v as _,r as $,y as S,l as ae}from"./text-DCkbNTq3.js";import"./text-field-UsP3CXyu.js";import{c as O,P as oe,u as V,b as T,M as se,ag as ne,V as ce}from"./createLucideIcon-BOfs0RvG.js";import{u as ie}from"./select-hLIrycZp.js";import{P,a as q}from"./button-Ccwu8jNm.js";import{j as d}from"./jsx-runtime-D_zvdyIk.js";var y="Checkbox",[le,Ve]=O(y),[ue,j]=le(y);function de(e){const{__scopeCheckbox:r,checked:c,children:s,defaultChecked:t,disabled:a,form:l,name:o,onCheckedChange:i,required:f,value:b="on",internal_do_not_use_render:m}=e,[v,k]=V({prop:c,defaultProp:t??!1,onChange:i,caller:y}),[x,C]=n.useState(null),[g,u]=n.useState(null),p=n.useRef(!1),E=x?!!l||!!x.closest("form"):!0,R={checked:v,disabled:a,setChecked:k,control:x,setControl:C,name:o,form:l,value:b,hasConsumerStoppedPropagationRef:p,required:f,defaultChecked:h(t)?!1:t,isFormControl:E,bubbleInput:g,setBubbleInput:u};return d.jsx(ue,{scope:r,...R,children:pe(m)?m(R):s})}var F="CheckboxTrigger",G=n.forwardRef(({__scopeCheckbox:e,onKeyDown:r,onClick:c,...s},t)=>{const{control:a,value:l,disabled:o,checked:i,required:f,setControl:b,setChecked:m,hasConsumerStoppedPropagationRef:v,isFormControl:k,bubbleInput:x}=j(F,e),C=D(t,b),g=n.useRef(i);return n.useEffect(()=>{const u=a?.form;if(u){const p=()=>m(g.current);return u.addEventListener("reset",p),()=>u.removeEventListener("reset",p)}},[a,m]),d.jsx(P.button,{type:"button",role:"checkbox","aria-checked":h(i)?"mixed":i,"aria-required":f,"data-state":Q(i),"data-disabled":o?"":void 0,disabled:o,value:l,...s,ref:C,onKeyDown:T(r,u=>{u.key==="Enter"&&u.preventDefault()}),onClick:T(c,u=>{m(p=>h(p)?!0:!p),x&&k&&(v.current=u.isPropagationStopped(),v.current||u.stopPropagation())})})});G.displayName=F;var H=n.forwardRef((e,r)=>{const{__scopeCheckbox:c,name:s,checked:t,defaultChecked:a,required:l,disabled:o,value:i,onCheckedChange:f,form:b,...m}=e;return d.jsx(de,{__scopeCheckbox:c,checked:t,defaultChecked:a,disabled:o,required:l,onCheckedChange:f,name:s,form:b,value:i,internal_do_not_use_render:({isFormControl:v})=>d.jsxs(d.Fragment,{children:[d.jsx(G,{...m,ref:r,__scopeCheckbox:c}),v&&d.jsx(J,{__scopeCheckbox:c})]})})});H.displayName=y;var U="CheckboxIndicator",X=n.forwardRef((e,r)=>{const{__scopeCheckbox:c,forceMount:s,...t}=e,a=j(U,c);return d.jsx(oe,{present:s||h(a.checked)||a.checked===!0,children:d.jsx(P.span,{"data-state":Q(a.checked),"data-disabled":a.disabled?"":void 0,...t,ref:r,style:{pointerEvents:"none",...e.style}})})});X.displayName=U;var K="CheckboxBubbleInput",J=n.forwardRef(({__scopeCheckbox:e,...r},c)=>{const{control:s,hasConsumerStoppedPropagationRef:t,checked:a,defaultChecked:l,required:o,disabled:i,name:f,value:b,form:m,bubbleInput:v,setBubbleInput:k}=j(K,e),x=D(c,k),C=ie(a),g=se(s);n.useEffect(()=>{const p=v;if(!p)return;const E=window.HTMLInputElement.prototype,M=Object.getOwnPropertyDescriptor(E,"checked").set,re=!t.current;if(C!==a&&M){const te=new Event("click",{bubbles:re});p.indeterminate=h(a),M.call(p,h(a)?!1:a),p.dispatchEvent(te)}},[v,C,a,t]);const u=n.useRef(h(a)?!1:a);return d.jsx(P.input,{type:"checkbox","aria-hidden":!0,defaultChecked:l??u.current,required:o,disabled:i,name:f,value:b,form:m,...r,tabIndex:-1,ref:x,style:{...r.style,...g,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});J.displayName=K;function pe(e){return typeof e=="function"}function h(e){return e==="indeterminate"}function Q(e){return h(e)?"indeterminate":e?"checked":"unchecked"}var w="Progress",A=100,[fe,qe]=O(w),[me,ve]=fe(w),W=n.forwardRef((e,r)=>{const{__scopeProgress:c,value:s=null,max:t,getValueLabel:a=he,...l}=e;(t||t===0)&&!z(t)&&console.error(be(`${t}`,"Progress"));const o=z(t)?t:A;s!==null&&!B(s,o)&&console.error(xe(`${s}`,"Progress"));const i=B(s,o)?s:null,f=N(i)?a(i,o):void 0;return d.jsx(me,{scope:c,value:i,max:o,children:d.jsx(P.div,{"aria-valuemax":o,"aria-valuemin":0,"aria-valuenow":N(i)?i:void 0,"aria-valuetext":f,role:"progressbar","data-state":ee(i,o),"data-value":i??void 0,"data-max":o,...l,ref:r})})});W.displayName=w;var Y="ProgressIndicator",Z=n.forwardRef((e,r)=>{const{__scopeProgress:c,...s}=e,t=ve(Y,c);return d.jsx(P.div,{"data-state":ee(t.value,t.max),"data-value":t.value??void 0,"data-max":t.max,...s,ref:r})});Z.displayName=Y;function he(e,r){return`${Math.round(e/r*100)}%`}function ee(e,r){return e==null?"indeterminate":e===r?"complete":"loading"}function N(e){return typeof e=="number"}function z(e){return N(e)&&!isNaN(e)&&e>0}function B(e,r){return N(e)&&!isNaN(e)&&e<=r&&e>=0}function be(e,r){return`Invalid prop \`max\` of value \`${e}\` supplied to \`${r}\`. Only numbers greater than 0 are valid max values. Defaulting to \`${A}\`.`}function xe(e,r){return`Invalid prop \`value\` of value \`${e}\` supplied to \`${r}\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or ${A} if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`}var Ce=W,ke=Z;const ge=["1","2","3"],Pe=["classic","surface","soft"],Ne={size:{type:"enum",className:"rt-r-size",values:ge,default:"2",responsive:!0},variant:{type:"enum",className:"rt-variant",values:Pe,default:"surface"},...I,...L},ye=n.forwardRef((e,r)=>{const{className:c,color:s,checked:t,defaultChecked:a,onCheckedChange:l,...o}=_(e,Ne,$),[i,f]=V({prop:t,defaultProp:a,onChange:l});return n.createElement(H,{"data-accent-color":s,...o,defaultChecked:a,checked:i,onCheckedChange:f,asChild:!1,ref:r,className:S("rt-reset","rt-BaseCheckboxRoot","rt-CheckboxRoot",c)},n.createElement(X,{asChild:!0,className:"rt-BaseCheckboxIndicator rt-CheckboxIndicator"},n.createElement(i==="indeterminate"?ne:ce,null)))});ye.displayName="Checkbox";const Ee=["1","2","3"],Re=["classic","surface","soft"],Ie={size:{type:"enum",className:"rt-r-size",values:Ee,default:"2",responsive:!0},variant:{type:"enum",className:"rt-variant",values:Re,default:"surface"},...I,...L,...q,duration:{type:"string"}},_e=n.forwardRef((e,r)=>{const{className:c,style:s,color:t,radius:a,duration:l,...o}=_(e,Ie,$);return n.createElement(Ce,{"data-accent-color":t,"data-radius":a,ref:r,className:S("rt-ProgressRoot",c),style:ae({"--progress-duration":"value"in o?void 0:l,"--progress-value":"value"in o?o.value:void 0,"--progress-max":"max"in o?o.max:void 0},s),...o,asChild:!1},n.createElement(ke,{className:"rt-ProgressIndicator"}))});_e.displayName="Progress";const $e=["1","2","3"],Se=["classic","surface","soft"],je=["none","vertical","horizontal","both"],we={size:{type:"enum",className:"rt-r-size",values:$e,default:"2",responsive:!0},variant:{type:"enum",className:"rt-variant",values:Se,default:"surface"},resize:{type:"enum",className:"rt-r-resize",values:je,responsive:!0},...I,...q},Ae=n.forwardRef((e,r)=>{const{className:c,color:s,radius:t,style:a,...l}=_(e,we,$);return n.createElement("div",{"data-accent-color":s,"data-radius":t,className:S("rt-TextAreaRoot",c),style:a},n.createElement("textarea",{className:"rt-reset rt-TextAreaInput",ref:r,...l}))});Ae.displayName="TextArea";export{H as C,X as a,ye as b,Ve as c,Ae as r,_e as s,Ne as t};
