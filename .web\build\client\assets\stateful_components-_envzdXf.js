import{ah as r,ae as l,ai as y,ak as b,al as p,am as v,aj as C}from"./createLucideIcon-BOfs0RvG.js";import{a as e,v as d}from"./chunk-QMGIS6GS-suYYFPSk.js";import{f as o,i as u,E as m,c as n}from"./state-B1hYtTsq.js";import{j as t}from"./emotion-react.browser.esm-BNSIgtcs.js";import{c as _,o as g}from"./button-Ccwu8jNm.js";import{o as x}from"./card-BFhh40Od.js";import{p as s}from"./text-DCkbNTq3.js";/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const N=[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M9 13a4.5 4.5 0 0 0 3-4",key:"10igwf"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M12 13h4",key:"1ku699"}],["path",{d:"M12 18h6a2 2 0 0 1 2 2v1",key:"105ag5"}],["path",{d:"M12 8h8",key:"1lhi5i"}],["path",{d:"M16 8V5a2 2 0 0 1 2-2",key:"u6izg6"}],["circle",{cx:"16",cy:"13",r:".5",key:"ry7gng"}],["circle",{cx:"18",cy:"3",r:".5",key:"1aiba7"}],["circle",{cx:"20",cy:"21",r:".5",key:"yhc1fs"}],["circle",{cx:"20",cy:"8",r:".5",key:"1e43v0"}]],ct=r("brain-circuit",N);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const M=[["line",{x1:"12",x2:"12",y1:"20",y2:"10",key:"1vz5eb"}],["line",{x1:"18",x2:"18",y1:"20",y2:"4",key:"cun8e5"}],["line",{x1:"6",x2:"6",y1:"20",y2:"16",key:"hq0ia6"}]],w=r("chart-no-axes-column-increasing",M);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const F=[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]],z=r("chevron-down",F);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const S=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]],h=r("circle-question-mark",S);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const W=[["rect",{width:"8",height:"4",x:"8",y:"2",rx:"1",ry:"1",key:"tgr4d6"}],["path",{d:"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2",key:"116196"}],["path",{d:"M12 11h4",key:"1jrz19"}],["path",{d:"M12 16h4",key:"n85exb"}],["path",{d:"M8 11h.01",key:"1dfujw"}],["path",{d:"M8 16h.01",key:"18s6g9"}]],$=r("clipboard-list",W);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const E=[["rect",{width:"8",height:"4",x:"8",y:"2",rx:"1",ry:"1",key:"tgr4d6"}],["path",{d:"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2",key:"116196"}],["path",{d:"M9 14h6",key:"159ibu"}],["path",{d:"M12 17v-6",key:"1y8rbf"}]],j=r("clipboard-plus",E);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const L=[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]],q=r("house",L);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const H=[["path",{d:"m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4",key:"g0fldk"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}]],A=r("key",H);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const I=[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]],P=r("log-out",I);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const V=[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]],B=r("plus",V);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const T=[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],f=r("settings",T);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const D=[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]],U=r("user",D);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const G=[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]],K=r("users",G);function O(){const a=e.useContext(o.reflex___state____state__states___auth_state____auth_state);return t(e.Fragment,{},a.is_admin_rx_state_?t(e.Fragment,{},t(l,{asChild:!0,css:{color:"gray.700",fontWeight:"medium","&:hover":{color:"blue.600"},"&:active":{color:"blue.600",fontWeight:"bold"}}},t(d,{to:"/admin"},t(_,{align:"start",className:"rx-Stack",direction:"row",gap:"2"},t(f,{size:18}),t(s,{as:"p"},"Admin"))))):t(e.Fragment,{}))}function it(){const a=e.useContext(o.reflex___state____state__states___auth_state____auth_state);return t(e.Fragment,{},a.is_authenticated_rx_state_?t(e.Fragment,{},t(_,{align:"start",className:"rx-Stack",direction:"row",gap:"6"},t(l,{asChild:!0,css:{color:"gray.700",fontWeight:"medium","&:hover":{color:"blue.600"},"&:active":{color:"blue.600",fontWeight:"bold"}}},t(d,{to:"/dashboard"},t(_,{align:"start",className:"rx-Stack",direction:"row",gap:"2"},t(q,{size:18}),t(s,{as:"p"},"Dashboard")))),t(l,{asChild:!0,css:{color:"gray.700",fontWeight:"medium","&:hover":{color:"blue.600"},"&:active":{color:"blue.600",fontWeight:"bold"}}},t(d,{to:"/patients"},t(_,{align:"start",className:"rx-Stack",direction:"row",gap:"2"},t(K,{size:18}),t(s,{as:"p"},"Patients")))),t(l,{asChild:!0,css:{color:"gray.700",fontWeight:"medium","&:hover":{color:"blue.600"},"&:active":{color:"blue.600",fontWeight:"bold"}}},t(d,{to:"/assessments"},t(_,{align:"start",className:"rx-Stack",direction:"row",gap:"2"},t($,{size:18}),t(s,{as:"p"},"Assessments")))),t(l,{asChild:!0,css:{color:"gray.700",fontWeight:"medium","&:hover":{color:"blue.600"},"&:active":{color:"blue.600",fontWeight:"bold"}}},t(d,{to:"/reports"},t(_,{align:"start",className:"rx-Stack",direction:"row",gap:"2"},t(w,{size:18}),t(s,{as:"p"},"Reports")))),t(O,{}))):t(e.Fragment,{}))}function Q(){const a=e.useContext(o.reflex___state____state__states___auth_state____auth_state);return t(C,{},t(g,{size:"2",variant:"ghost"},t(_,{align:"start",className:"rx-Stack",direction:"row",gap:"2"},u(a.current_user_rx_state_)?a.current_user_rx_state_?.full_name:"User",t(z,{size:16}))))}function R(){const[a,k]=e.useContext(m),c=e.useCallback(i=>a([n("reflex___state____state.states___auth_state____auth_state.logout",{},{})],[i],{}),[a,n]);return t(p,{css:{color:"red.600"},onClick:c},t(_,{align:"start",className:"rx-Stack",direction:"row",gap:"2"},t(P,{size:16}),t(s,{as:"p"},"Logout")))}function Z(){const[a,k]=e.useContext(m),c=e.useCallback(i=>a([n("_redirect",{path:"/login",external:!1,replace:!1},{})],[i],{}),[a,n]);return t(g,{color:"blue",onClick:c,size:"2"},"Login")}function lt(){const a=e.useContext(o.reflex___state____state__states___auth_state____auth_state);return t(e.Fragment,{},a.is_authenticated_rx_state_?t(e.Fragment,{},t(_,{align:"center",className:"rx-Stack",direction:"row",gap:"2"},t(g,{size:"2",title:"New Patient",variant:"ghost"},t(B,{size:16})),t(g,{size:"2",title:"New Assessment",variant:"ghost"},t(j,{size:16})),t(y,{},t(Q,{}),t(b,{},t(p,{},t(_,{align:"start",className:"rx-Stack",direction:"row",gap:"2"},t(U,{size:16}),t(s,{as:"p"},"Profile"))),t(p,{},t(_,{align:"start",className:"rx-Stack",direction:"row",gap:"2"},t(A,{size:16}),t(s,{as:"p"},"Change Password"))),t(p,{},t(_,{align:"start",className:"rx-Stack",direction:"row",gap:"2"},t(f,{size:16}),t(s,{as:"p"},"Settings"))),t(v,{}),t(R,{}))))):t(e.Fragment,{},t(Z,{})))}function J(){const a=e.useContext(o.reflex___state____state__states___patient_state____patient_state);return t(s,{as:"p"},a.success_message_rx_state_)}function dt(){const a=e.useContext(o.reflex___state____state__states___patient_state____patient_state);return t(e.Fragment,{},u(a.success_message_rx_state_)?t(e.Fragment,{},t(x,{css:{position:"fixed",top:"20px",right:"20px",zIndex:"9999",maxWidth:"400px",background:"green.50",border:"1px solid",borderColor:"green.200"}},t(_,{align:"start",className:"rx-Stack",direction:"row",gap:"2"},t(h,{css:{color:"green.500"}}),t(_,{align:"start",className:"rx-Stack",direction:"column",gap:"1"},t(s,{as:"p",css:{fontWeight:"bold"}},"Success"),t(J,{}))))):t(e.Fragment,{}))}function X(){const a=e.useContext(o.reflex___state____state__states___patient_state____patient_state);return t(s,{as:"p"},a.error_message_rx_state_)}function gt(){const a=e.useContext(o.reflex___state____state__states___patient_state____patient_state);return t(e.Fragment,{},u(a.error_message_rx_state_)?t(e.Fragment,{},t(x,{css:{position:"fixed",top:"20px",right:"20px",zIndex:"9999",maxWidth:"400px",background:"red.50",border:"1px solid",borderColor:"red.200"}},t(_,{align:"start",className:"rx-Stack",direction:"row",gap:"2"},t(h,{css:{color:"red.500"}}),t(_,{align:"start",className:"rx-Stack",direction:"column",gap:"1"},t(s,{as:"p",css:{fontWeight:"bold"}},"Error"),t(X,{}))))):t(e.Fragment,{}))}function Y(){const a=e.useContext(o.reflex___state____state__states___clinical_state____clinical_state);return t(s,{as:"p"},a.success_message_rx_state_)}function ut(){const a=e.useContext(o.reflex___state____state__states___clinical_state____clinical_state);return t(e.Fragment,{},u(a.success_message_rx_state_)?t(e.Fragment,{},t(x,{css:{position:"fixed",top:"80px",right:"20px",zIndex:"9999",maxWidth:"400px",background:"green.50",border:"1px solid",borderColor:"green.200"}},t(_,{align:"start",className:"rx-Stack",direction:"row",gap:"2"},t(h,{css:{color:"green.500"}}),t(_,{align:"start",className:"rx-Stack",direction:"column",gap:"1"},t(s,{as:"p",css:{fontWeight:"bold"}},"Success"),t(Y,{}))))):t(e.Fragment,{}))}function tt(){const a=e.useContext(o.reflex___state____state__states___clinical_state____clinical_state);return t(s,{as:"p"},a.error_message_rx_state_)}function pt(){const a=e.useContext(o.reflex___state____state__states___clinical_state____clinical_state);return t(e.Fragment,{},u(a.error_message_rx_state_)?t(e.Fragment,{},t(x,{css:{position:"fixed",top:"80px",right:"20px",zIndex:"9999",maxWidth:"400px",background:"red.50",border:"1px solid",borderColor:"red.200"}},t(_,{align:"start",className:"rx-Stack",direction:"row",gap:"2"},t(h,{css:{color:"red.500"}}),t(_,{align:"start",className:"rx-Stack",direction:"column",gap:"1"},t(s,{as:"p",css:{fontWeight:"bold"}},"Error"),t(tt,{}))))):t(e.Fragment,{}))}function xt(){const[a,k]=e.useContext(m),c=e.useCallback(i=>a([n("_redirect",{path:"/login",external:!1,replace:!1},{})],[i],{}),[a,n]);return t(g,{color:"blue",onClick:c},"Go to Login")}export{ct as B,h as C,it as F,U,lt as a,dt as b,gt as c,ut as d,pt as e,xt as f,K as g,$ as h,j as i,w as j,z as k};
